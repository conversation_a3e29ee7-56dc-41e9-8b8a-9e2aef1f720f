using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using FoundationService.Core.Interfaces;
using FoundationService.Core.Models;
using Microsoft.Extensions.Logging;

namespace FoundationService.Core.Services
{
    /// <summary>
    /// Service implementation for slab operations following EnvelopeService pattern
    /// </summary>
    public class SlabService : ISlabService
    {
        private readonly ISlabRepository _slabRepository;
        private readonly ILogger<SlabService> _logger;

        public SlabService(ISlabRepository slabRepository, ILogger<SlabService> logger)
        {
            _slabRepository = slabRepository ?? throw new ArgumentNullException(nameof(slabRepository));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        }

        public async Task<IEnumerable<Slab>> GetSlabsByHouseIdAsync(Guid houseId)
        {
            _logger.LogInformation("Getting slabs for house ID: {HouseId}", houseId);
            return await _slabRepository.GetSlabsByHouseIdAsync(houseId);
        }

        public async Task<Slab?> GetSlabByIdAsync(Guid id)
        {
            _logger.LogInformation("Getting slab by ID: {SlabId}", id);
            return await _slabRepository.GetSlabByIdAsync(id);
        }

        public async Task<IEnumerable<Slab>> GetAllSlabsAsync()
        {
            _logger.LogInformation("Getting all slabs");
            return await _slabRepository.GetAllSlabsAsync();
        }

        public async Task<Slab> AddSlabAsync(Slab slab)
        {
            _logger.LogInformation("Adding new slab for house ID: {HouseId}", slab.HouseId);
            
            // Ensure proper initialization of nested objects
            if (slab.Floor == null)
                slab.Floor = new FoundationFloor();
            
            if (slab.Wall == null)
                slab.Wall = new SlabWall();

            // Set up foreign key relationships
            if (slab.Floor.Id == Guid.Empty)
                slab.Floor.Id = Guid.NewGuid();
            slab.FloorId = slab.Floor.Id;

            if (slab.Wall.Id == Guid.Empty)
                slab.Wall.Id = Guid.NewGuid();
            slab.WallId = slab.Wall.Id;

            // Initialize nested objects for floor
            if (slab.Floor.Construction == null)
                slab.Floor.Construction = new FoundationFloorConstruction();
            if (slab.Floor.Measurements == null)
                slab.Floor.Measurements = new FoundationMeasurements();

            // Initialize nested objects for wall
            if (slab.Wall.RValues == null)
                slab.Wall.RValues = new WallRValues();

            // Set up floor IDs
            if (slab.Floor.Construction.Id == Guid.Empty)
                slab.Floor.Construction.Id = Guid.NewGuid();
            slab.Floor.ConstructionId = slab.Floor.Construction.Id;

            if (slab.Floor.Measurements.Id == Guid.Empty)
                slab.Floor.Measurements.Id = Guid.NewGuid();
            slab.Floor.MeasurementsId = slab.Floor.Measurements.Id;

            // Set up wall IDs
            if (slab.Wall.RValues.Id == Guid.Empty)
                slab.Wall.RValues.Id = Guid.NewGuid();
            slab.Wall.RValuesId = slab.Wall.RValues.Id;

            return await _slabRepository.AddSlabAsync(slab);
        }

        public async Task UpdateSlabAsync(Slab slab)
        {
            _logger.LogInformation("Updating slab with ID: {SlabId}", slab.Id);
            
            // Ensure foreign key relationships are maintained
            if (slab.Floor != null)
            {
                slab.FloorId = slab.Floor.Id;
                if (slab.Floor.Construction != null)
                    slab.Floor.ConstructionId = slab.Floor.Construction.Id;
                if (slab.Floor.Measurements != null)
                    slab.Floor.MeasurementsId = slab.Floor.Measurements.Id;
            }

            if (slab.Wall != null)
            {
                slab.WallId = slab.Wall.Id;
                if (slab.Wall.RValues != null)
                    slab.Wall.RValuesId = slab.Wall.RValues.Id;
            }

            await _slabRepository.UpdateSlabAsync(slab);
        }

        public async Task DeleteSlabAsync(Guid id)
        {
            _logger.LogInformation("Deleting slab with ID: {SlabId}", id);
            await _slabRepository.DeleteSlabAsync(id);
        }

        public async Task<Slab?> GetSlabByEnergyUpgradeIdAsync(Guid energyUpgradeId)
        {
            _logger.LogInformation("Getting slab for energy upgrade ID: {EnergyUpgradeId}", energyUpgradeId);
            return await _slabRepository.GetSlabByEnergyUpgradeIdAsync(energyUpgradeId);
        }

        public async Task<Slab> DuplicateSlabForEnergyUpgradeAsync(Slab baseSlab, Guid energyUpgradeId)
        {
            _logger.LogInformation("Duplicating slab {SlabId} for energy upgrade {EnergyUpgradeId}",
                baseSlab.Id, energyUpgradeId);
            return await _slabRepository.DuplicateSlabForEnergyUpgradeAsync(baseSlab, energyUpgradeId);
        }
    }
}
