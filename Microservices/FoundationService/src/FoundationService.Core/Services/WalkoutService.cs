using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using FoundationService.Core.Interfaces;
using FoundationService.Core.Models;
using Microsoft.Extensions.Logging;

namespace FoundationService.Core.Services
{
    /// <summary>
    /// Service implementation for walkout operations following EnvelopeService pattern
    /// </summary>
    public class WalkoutService : IWalkoutService
    {
        private readonly IWalkoutRepository _walkoutRepository;
        private readonly ILogger<WalkoutService> _logger;

        public WalkoutService(IWalkoutRepository walkoutRepository, ILogger<WalkoutService> logger)
        {
            _walkoutRepository = walkoutRepository ?? throw new ArgumentNullException(nameof(walkoutRepository));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        }

        public async Task<IEnumerable<Walkout>> GetWalkoutsByHouseIdAsync(Guid houseId)
        {
            _logger.LogInformation("Getting walkouts for house ID: {HouseId}", houseId);
            return await _walkoutRepository.GetWalkoutsByHouseIdAsync(houseId);
        }

        public async Task<Walkout?> GetWalkoutByIdAsync(Guid id)
        {
            _logger.LogInformation("Getting walkout by ID: {WalkoutId}", id);
            return await _walkoutRepository.GetWalkoutByIdAsync(id);
        }

        public async Task<IEnumerable<Walkout>> GetAllWalkoutsAsync()
        {
            _logger.LogInformation("Getting all walkouts");
            return await _walkoutRepository.GetAllWalkoutsAsync();
        }

        public async Task<Walkout> AddWalkoutAsync(Walkout walkout)
        {
            _logger.LogInformation("Adding new walkout for house ID: {HouseId}", walkout.HouseId);
            
            // Ensure proper initialization of nested objects
            if (walkout.Measurements == null)
                walkout.Measurements = new WalkoutMeasurements();
            if (walkout.Floor == null)
                walkout.Floor = new WalkoutFloor();
            if (walkout.Wall == null)
                walkout.Wall = new FoundationWall();
            if (walkout.ExteriorSurfaces == null)
                walkout.ExteriorSurfaces = new ExteriorSurfaces();
            if (walkout.Locations == null)
                walkout.Locations = new Locations();

            // Set up foreign key relationships
            if (walkout.Measurements.Id == Guid.Empty)
                walkout.Measurements.Id = Guid.NewGuid();
            walkout.MeasurementsId = walkout.Measurements.Id;

            if (walkout.Floor.Id == Guid.Empty)
                walkout.Floor.Id = Guid.NewGuid();
            walkout.FloorId = walkout.Floor.Id;

            if (walkout.Wall.Id == Guid.Empty)
                walkout.Wall.Id = Guid.NewGuid();
            walkout.WallId = walkout.Wall.Id;

            if (walkout.ExteriorSurfaces.Id == Guid.Empty)
                walkout.ExteriorSurfaces.Id = Guid.NewGuid();
            walkout.ExteriorSurfacesId = walkout.ExteriorSurfaces.Id;

            if (walkout.Locations.Id == Guid.Empty)
                walkout.Locations.Id = Guid.NewGuid();
            walkout.LocationsId = walkout.Locations.Id;

            // Initialize floor construction
            if (walkout.Floor.Construction == null)
                walkout.Floor.Construction = new FoundationFloorConstruction();
            if (walkout.Floor.Construction.Id == Guid.Empty)
                walkout.Floor.Construction.Id = Guid.NewGuid();
            walkout.Floor.ConstructionId = walkout.Floor.Construction.Id;

            // Initialize wall components
            if (walkout.Wall.Construction == null)
                walkout.Wall.Construction = new FoundationWallConstruction();
            if (walkout.Wall.Measurements == null)
                walkout.Wall.Measurements = new FoundationWallMeasurements();

            if (walkout.Wall.Construction.Id == Guid.Empty)
                walkout.Wall.Construction.Id = Guid.NewGuid();
            walkout.Wall.ConstructionId = walkout.Wall.Construction.Id;

            if (walkout.Wall.Measurements.Id == Guid.Empty)
                walkout.Wall.Measurements.Id = Guid.NewGuid();
            walkout.Wall.MeasurementsId = walkout.Wall.Measurements.Id;

            // Initialize location coordinates
            if (walkout.Locations.L1_1 == null)
                walkout.Locations.L1_1 = new Location();
            if (walkout.Locations.L1_2 == null)
                walkout.Locations.L1_2 = new Location();
            if (walkout.Locations.L2_1 == null)
                walkout.Locations.L2_1 = new Location();
            if (walkout.Locations.L2_2 == null)
                walkout.Locations.L2_2 = new Location();

            // Set up location IDs
            if (walkout.Locations.L1_1.Id == Guid.Empty)
                walkout.Locations.L1_1.Id = Guid.NewGuid();
            walkout.Locations.L1_1Id = walkout.Locations.L1_1.Id;

            if (walkout.Locations.L1_2.Id == Guid.Empty)
                walkout.Locations.L1_2.Id = Guid.NewGuid();
            walkout.Locations.L1_2Id = walkout.Locations.L1_2.Id;

            if (walkout.Locations.L2_1.Id == Guid.Empty)
                walkout.Locations.L2_1.Id = Guid.NewGuid();
            walkout.Locations.L2_1Id = walkout.Locations.L2_1.Id;

            if (walkout.Locations.L2_2.Id == Guid.Empty)
                walkout.Locations.L2_2.Id = Guid.NewGuid();
            walkout.Locations.L2_2Id = walkout.Locations.L2_2.Id;

            return await _walkoutRepository.AddWalkoutAsync(walkout);
        }

        public async Task UpdateWalkoutAsync(Walkout walkout)
        {
            _logger.LogInformation("Updating walkout with ID: {WalkoutId}", walkout.Id);
            
            // Ensure foreign key relationships are maintained
            if (walkout.Measurements != null)
                walkout.MeasurementsId = walkout.Measurements.Id;

            if (walkout.Floor != null)
            {
                walkout.FloorId = walkout.Floor.Id;
                if (walkout.Floor.Construction != null)
                    walkout.Floor.ConstructionId = walkout.Floor.Construction.Id;
            }

            if (walkout.Wall != null)
            {
                walkout.WallId = walkout.Wall.Id;
                if (walkout.Wall.Construction != null)
                    walkout.Wall.ConstructionId = walkout.Wall.Construction.Id;
                if (walkout.Wall.Measurements != null)
                    walkout.Wall.MeasurementsId = walkout.Wall.Measurements.Id;
            }

            if (walkout.ExteriorSurfaces != null)
                walkout.ExteriorSurfacesId = walkout.ExteriorSurfaces.Id;

            if (walkout.Locations != null)
            {
                walkout.LocationsId = walkout.Locations.Id;
                if (walkout.Locations.L1_1 != null)
                    walkout.Locations.L1_1Id = walkout.Locations.L1_1.Id;
                if (walkout.Locations.L1_2 != null)
                    walkout.Locations.L1_2Id = walkout.Locations.L1_2.Id;
                if (walkout.Locations.L2_1 != null)
                    walkout.Locations.L2_1Id = walkout.Locations.L2_1.Id;
                if (walkout.Locations.L2_2 != null)
                    walkout.Locations.L2_2Id = walkout.Locations.L2_2.Id;
            }

            await _walkoutRepository.UpdateWalkoutAsync(walkout);
        }

        public async Task DeleteWalkoutAsync(Guid id)
        {
            _logger.LogInformation("Deleting walkout with ID: {WalkoutId}", id);
            await _walkoutRepository.DeleteWalkoutAsync(id);
        }

        public async Task<Walkout?> GetWalkoutByEnergyUpgradeIdAsync(Guid energyUpgradeId)
        {
            _logger.LogInformation("Getting walkout for energy upgrade ID: {EnergyUpgradeId}", energyUpgradeId);
            return await _walkoutRepository.GetWalkoutByEnergyUpgradeIdAsync(energyUpgradeId);
        }

        public async Task<Walkout> DuplicateWalkoutForEnergyUpgradeAsync(Walkout baseWalkout, Guid energyUpgradeId)
        {
            _logger.LogInformation("Duplicating walkout {WalkoutId} for energy upgrade {EnergyUpgradeId}",
                baseWalkout.Id, energyUpgradeId);
            return await _walkoutRepository.DuplicateWalkoutForEnergyUpgradeAsync(baseWalkout, energyUpgradeId);
        }
    }
}
