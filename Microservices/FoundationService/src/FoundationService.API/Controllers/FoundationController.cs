using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using AutoMapper;
using FoundationService.API.Models;
using FoundationService.Core.Interfaces;
using FoundationService.Core.Models;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;

namespace FoundationService.API.Controllers
{
    [ApiController]
    [Route("api/foundation")]
    public class FoundationController : ControllerBase
    {
        private readonly IBasementService _basementService;
        private readonly ICrawlspaceService _crawlspaceService;
        private readonly ISlabService _slabService;
        private readonly IWalkoutService _walkoutService;
        private readonly IMapper _mapper;
        private readonly ILogger<FoundationController> _logger;

        public FoundationController(
            IBasementService basementService,
            ICrawlspaceService crawlspaceService,
            ISlabService slabService,
            IWalkoutService walkoutService,
            IMapper mapper,
            ILogger<FoundationController> logger)
        {
            _basementService = basementService ?? throw new ArgumentNullException(nameof(basementService));
            _crawlspaceService = crawlspaceService ?? throw new ArgumentNullException(nameof(crawlspaceService));
            _slabService = slabService ?? throw new ArgumentNullException(nameof(slabService));
            _walkoutService = walkoutService ?? throw new ArgumentNullException(nameof(walkoutService));
            _mapper = mapper ?? throw new ArgumentNullException(nameof(mapper));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        }

        // ===============================
        // BASEMENT ENDPOINTS (Following EnvelopeService pattern)
        // ===============================

        // GET: api/foundation/{houseId}/basements
        [HttpGet("{houseId}/basements")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<ActionResult<List<BasementDto>>> GetBasements(Guid houseId)
        {
            _logger.LogInformation("Getting all basements for house with ID: {HouseId}", houseId);

            var basements = await _basementService.GetBasementsByHouseIdAsync(houseId);

            if (basements == null || !basements.Any())
            {
                _logger.LogWarning("No basements found for house with ID: {HouseId}", houseId);
                return NotFound();
            }

            var basementDtos = _mapper.Map<List<BasementDto>>(basements);
            return Ok(basementDtos);
        }

        // GET: api/foundation/basements/{id}
        [HttpGet("basements/{id}")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<ActionResult<BasementDto>> GetBasementById(Guid id)
        {
            _logger.LogInformation("Getting basement with ID: {BasementId}", id);

            var basement = await _basementService.GetBasementByIdAsync(id);

            if (basement == null)
            {
                _logger.LogWarning("Basement with ID: {BasementId} not found", id);
                return NotFound();
            }

            var basementDto = _mapper.Map<BasementDto>(basement);
            return Ok(basementDto);
        }

        // GET: api/foundation/basements
        [HttpGet("basements")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        public async Task<ActionResult<List<BasementDto>>> GetAllBasements()
        {
            _logger.LogInformation("Getting all basements");

            var basements = await _basementService.GetAllBasementsAsync();
            var basementDtos = _mapper.Map<List<BasementDto>>(basements);

            return Ok(basementDtos);
        }

        // POST: api/foundation/basements
        [HttpPost("basements")]
        [ProducesResponseType(StatusCodes.Status201Created)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        public async Task<ActionResult<BasementDto>> AddBasement([FromBody] BasementDto basementDto)
        {
            if (basementDto == null)
            {
                _logger.LogWarning("Basement data is null");
                return BadRequest("Basement data is required");
            }

            _logger.LogInformation("Creating new basement for house ID: {HouseId}", basementDto.HouseId);

            // Auto-populate resource text fields if only code provided
            if (!string.IsNullOrEmpty(basementDto.OpeningUpstairs?.Code) &&
                string.IsNullOrEmpty(basementDto.OpeningUpstairs?.English))
            {
                var opening = OpeningsUpstairs.FromCode(basementDto.OpeningUpstairs.Code);
                basementDto.OpeningUpstairs.English = opening.English;
                basementDto.OpeningUpstairs.French = opening.French;
            }

            if (!string.IsNullOrEmpty(basementDto.RoomType?.Code) &&
                string.IsNullOrEmpty(basementDto.RoomType?.English))
            {
                var roomType = RoomTypes.FromCode(basementDto.RoomType.Code);
                basementDto.RoomType.English = roomType.English;
                basementDto.RoomType.French = roomType.French;
            }

            var basement = _mapper.Map<Basement>(basementDto);

            var createdBasement = await _basementService.AddBasementAsync(basement);
            var createdBasementDto = _mapper.Map<BasementDto>(createdBasement);

            return CreatedAtAction(
                nameof(GetBasementById),
                new { id = createdBasementDto.Id },
                createdBasementDto);
        }

        // PUT: api/foundation/basements/{id}
        [HttpPut("basements/{id}")]
        [ProducesResponseType(StatusCodes.Status204NoContent)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<IActionResult> UpdateBasement(Guid id, [FromBody] BasementDto basementDto)
        {
            if (basementDto == null)
            {
                _logger.LogWarning("Basement data is null");
                return BadRequest("Basement data is required");
            }

            if (id != basementDto.Id)
            {
                _logger.LogWarning("Basement ID mismatch: {PathId} vs {BodyId}", id, basementDto.Id);
                return BadRequest("The ID in the path must match the ID in the body");
            }

            _logger.LogInformation("Updating basement with ID: {BasementId}", id);

            // Auto-populate resource text fields if only code provided
            if (!string.IsNullOrEmpty(basementDto.OpeningUpstairs?.Code) &&
                string.IsNullOrEmpty(basementDto.OpeningUpstairs?.English))
            {
                var opening = OpeningsUpstairs.FromCode(basementDto.OpeningUpstairs.Code);
                basementDto.OpeningUpstairs.English = opening.English;
                basementDto.OpeningUpstairs.French = opening.French;
            }

            if (!string.IsNullOrEmpty(basementDto.RoomType?.Code) &&
                string.IsNullOrEmpty(basementDto.RoomType?.English))
            {
                var roomType = RoomTypes.FromCode(basementDto.RoomType.Code);
                basementDto.RoomType.English = roomType.English;
                basementDto.RoomType.French = roomType.French;
            }

            var existingBasement = await _basementService.GetBasementByIdAsync(id);
            if (existingBasement == null)
            {
                _logger.LogWarning("Basement with ID: {BasementId} not found for update", id);
                return NotFound();
            }

            var basement = _mapper.Map<Basement>(basementDto);
            basement.Id = id;

            await _basementService.UpdateBasementAsync(basement);
            return NoContent();
        }

        // DELETE: api/foundation/basements/{id}
        [HttpDelete("basements/{id}")]
        [ProducesResponseType(StatusCodes.Status204NoContent)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<IActionResult> DeleteBasement(Guid id)
        {
            try
            {
                _logger.LogInformation("Deleting basement with ID: {BasementId}", id);

                var existingBasement = await _basementService.GetBasementByIdAsync(id);
                if (existingBasement == null)
                {
                    _logger.LogWarning("Basement with ID: {BasementId} not found for deletion", id);
                    return NotFound();
                }

                await _basementService.DeleteBasementAsync(id);
                return NoContent();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting basement with ID: {BasementId}", id);
                return StatusCode(StatusCodes.Status500InternalServerError, "An error occurred while deleting the basement");
            }
        }

        // GET: api/foundation/energy-upgrades/{energyUpgradeId}/basements
        [HttpGet("energy-upgrades/{energyUpgradeId}/basements")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<ActionResult<BasementDto>> GetBasementByEnergyUpgradeId(Guid energyUpgradeId)
        {
            _logger.LogInformation("Getting basement for energy upgrade ID: {EnergyUpgradeId}", energyUpgradeId);

            var basement = await _basementService.GetBasementByEnergyUpgradeIdAsync(energyUpgradeId);

            if (basement == null)
            {
                _logger.LogWarning("Basement not found for energy upgrade ID: {EnergyUpgradeId}", energyUpgradeId);
                return NotFound($"Basement not found for energy upgrade ID: {energyUpgradeId}");
            }

            var basementDto = _mapper.Map<BasementDto>(basement);
            return Ok(basementDto);
        }

        // POST: api/foundation/basements/{baseBasementId}/duplicate-for-energy-upgrade
        [HttpPost("basements/{baseBasementId}/duplicate-for-energy-upgrade")]
        [ProducesResponseType(StatusCodes.Status201Created)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        public async Task<ActionResult<BasementDto>> DuplicateBasementForEnergyUpgrade(
            Guid baseBasementId,
            [FromQuery] Guid energyUpgradeId)
        {
            try
            {
                _logger.LogInformation("Duplicating basement {BaseBasementId} for energy upgrade {EnergyUpgradeId}",
                    baseBasementId, energyUpgradeId);

                // Get the base basement
                var baseBasement = await _basementService.GetBasementByIdAsync(baseBasementId);
                if (baseBasement == null)
                {
                    _logger.LogWarning("Basement with ID: {BaseBasementId} not found", baseBasementId);
                    return NotFound($"Basement with ID {baseBasementId} not found");
                }

                // Create a duplicate with new ID but same values
                var duplicatedBasement = await _basementService.DuplicateBasementForEnergyUpgradeAsync(baseBasement, energyUpgradeId);

                var duplicatedBasementDto = _mapper.Map<BasementDto>(duplicatedBasement);

                _logger.LogInformation("Successfully duplicated basement {BaseBasementId} as {NewBasementId} for energy upgrade {EnergyUpgradeId}",
                    baseBasementId, duplicatedBasement.Id, energyUpgradeId);

                return CreatedAtAction(nameof(GetBasementById),
                    new { id = duplicatedBasement.Id },
                    duplicatedBasementDto);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error duplicating basement {BaseBasementId} for energy upgrade {EnergyUpgradeId}",
                    baseBasementId, energyUpgradeId);
                return StatusCode(StatusCodes.Status500InternalServerError,
                    "An error occurred while duplicating the basement for energy upgrade");
            }
        }

        // ===============================
        // RESOURCE ENDPOINTS (Following EnvelopeService pattern)
        // ===============================

        // GET: api/foundation/openings-upstairs
        [HttpGet("openings-upstairs")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        public ActionResult<List<OpeningsUpstairsDto>> GetOpeningsUpstairs()
        {
            _logger.LogInformation("Getting all available openings upstairs");

            var openings = OpeningsUpstairs.All;
            var openingDtos = _mapper.Map<List<OpeningsUpstairsDto>>(openings);

            return Ok(openingDtos);
        }

        // GET: api/foundation/room-types
        [HttpGet("room-types")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        public ActionResult<List<RoomTypesDto>> GetRoomTypes()
        {
            _logger.LogInformation("Getting all available room types");

            var roomTypes = RoomTypes.All;
            var roomTypeDtos = _mapper.Map<List<RoomTypesDto>>(roomTypes);

            return Ok(roomTypeDtos);
        }

        // GET: api/foundation/ventilation-types
        [HttpGet("ventilation-types")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        public ActionResult<List<VentilationTypesDto>> GetVentilationTypes()
        {
            _logger.LogInformation("Getting all available ventilation types");

            var ventilationTypes = VentilationTypes.All;
            var ventilationTypeDtos = _mapper.Map<List<VentilationTypesDto>>(ventilationTypes);

            return Ok(ventilationTypeDtos);
        }

        // GET: api/foundation/energy-upgrades/{energyUpgradeId}/crawlspaces
        [HttpGet("energy-upgrades/{energyUpgradeId}/crawlspaces")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<ActionResult<CrawlspaceDto>> GetCrawlspaceByEnergyUpgradeId(Guid energyUpgradeId)
        {
            _logger.LogInformation("Getting crawlspace for energy upgrade ID: {EnergyUpgradeId}", energyUpgradeId);

            var crawlspace = await _crawlspaceService.GetCrawlspaceByEnergyUpgradeIdAsync(energyUpgradeId);

            if (crawlspace == null)
            {
                _logger.LogWarning("Crawlspace not found for energy upgrade ID: {EnergyUpgradeId}", energyUpgradeId);
                return NotFound($"Crawlspace not found for energy upgrade ID: {energyUpgradeId}");
            }

            var crawlspaceDto = _mapper.Map<CrawlspaceDto>(crawlspace);
            return Ok(crawlspaceDto);
        }

        // POST: api/foundation/crawlspaces/{baseCrawlspaceId}/duplicate-for-energy-upgrade
        [HttpPost("crawlspaces/{baseCrawlspaceId}/duplicate-for-energy-upgrade")]
        [ProducesResponseType(StatusCodes.Status201Created)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        public async Task<ActionResult<CrawlspaceDto>> DuplicateCrawlspaceForEnergyUpgrade(
            Guid baseCrawlspaceId,
            [FromQuery] Guid energyUpgradeId)
        {
            try
            {
                _logger.LogInformation("Duplicating crawlspace {BaseCrawlspaceId} for energy upgrade {EnergyUpgradeId}",
                    baseCrawlspaceId, energyUpgradeId);

                // Get the base crawlspace
                var baseCrawlspace = await _crawlspaceService.GetCrawlspaceByIdAsync(baseCrawlspaceId);
                if (baseCrawlspace == null)
                {
                    _logger.LogWarning("Crawlspace with ID: {BaseCrawlspaceId} not found", baseCrawlspaceId);
                    return NotFound($"Crawlspace with ID {baseCrawlspaceId} not found");
                }

                // Create a duplicate with new ID but same values
                var duplicatedCrawlspace = await _crawlspaceService.DuplicateCrawlspaceForEnergyUpgradeAsync(baseCrawlspace, energyUpgradeId);

                var duplicatedCrawlspaceDto = _mapper.Map<CrawlspaceDto>(duplicatedCrawlspace);

                _logger.LogInformation("Successfully duplicated crawlspace {BaseCrawlspaceId} as {NewCrawlspaceId} for energy upgrade {EnergyUpgradeId}",
                    baseCrawlspaceId, duplicatedCrawlspace.Id, energyUpgradeId);

                return CreatedAtAction(nameof(GetCrawlspaceById),
                    new { id = duplicatedCrawlspace.Id },
                    duplicatedCrawlspaceDto);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error duplicating crawlspace {BaseCrawlspaceId} for energy upgrade {EnergyUpgradeId}",
                    baseCrawlspaceId, energyUpgradeId);
                return StatusCode(StatusCodes.Status500InternalServerError,
                    "An error occurred while duplicating the crawlspace for energy upgrade");
            }
        }

        // ===============================
        // CRAWLSPACE ENDPOINTS (Following EnvelopeService pattern)
        // ===============================

        // GET: api/foundation/{houseId}/crawlspaces
        [HttpGet("{houseId}/crawlspaces")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<ActionResult<List<CrawlspaceDto>>> GetCrawlspaces(Guid houseId)
        {
            _logger.LogInformation("Getting all crawlspaces for house with ID: {HouseId}", houseId);

            var crawlspaces = await _crawlspaceService.GetCrawlspacesByHouseIdAsync(houseId);

            if (crawlspaces == null || !crawlspaces.Any())
            {
                _logger.LogWarning("No crawlspaces found for house with ID: {HouseId}", houseId);
                return NotFound();
            }

            var crawlspaceDtos = _mapper.Map<List<CrawlspaceDto>>(crawlspaces);
            return Ok(crawlspaceDtos);
        }

        // GET: api/foundation/crawlspaces/{id}
        [HttpGet("crawlspaces/{id}")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<ActionResult<CrawlspaceDto>> GetCrawlspaceById(Guid id)
        {
            _logger.LogInformation("Getting crawlspace with ID: {CrawlspaceId}", id);

            var crawlspace = await _crawlspaceService.GetCrawlspaceByIdAsync(id);

            if (crawlspace == null)
            {
                _logger.LogWarning("Crawlspace with ID: {CrawlspaceId} not found", id);
                return NotFound();
            }

            var crawlspaceDto = _mapper.Map<CrawlspaceDto>(crawlspace);
            return Ok(crawlspaceDto);
        }

        // GET: api/foundation/crawlspaces
        [HttpGet("crawlspaces")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        public async Task<ActionResult<List<CrawlspaceDto>>> GetAllCrawlspaces()
        {
            _logger.LogInformation("Getting all crawlspaces");

            var crawlspaces = await _crawlspaceService.GetAllCrawlspacesAsync();
            var crawlspaceDtos = _mapper.Map<List<CrawlspaceDto>>(crawlspaces);

            return Ok(crawlspaceDtos);
        }

        // POST: api/foundation/crawlspaces
        [HttpPost("crawlspaces")]
        [ProducesResponseType(StatusCodes.Status201Created)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        public async Task<ActionResult<CrawlspaceDto>> AddCrawlspace([FromBody] CrawlspaceDto crawlspaceDto)
        {
            if (crawlspaceDto == null)
            {
                _logger.LogWarning("Crawlspace data is null");
                return BadRequest("Crawlspace data is required");
            }

            _logger.LogInformation("Creating new crawlspace for house ID: {HouseId}", crawlspaceDto.HouseId);

            // Auto-populate resource text fields if only code provided
            if (!string.IsNullOrEmpty(crawlspaceDto.VentilationType?.Code) &&
                string.IsNullOrEmpty(crawlspaceDto.VentilationType?.English))
            {
                var ventilationType = VentilationTypes.FromCode(crawlspaceDto.VentilationType.Code);
                crawlspaceDto.VentilationType.English = ventilationType.English;
                crawlspaceDto.VentilationType.French = ventilationType.French;
            }

            var crawlspace = _mapper.Map<Crawlspace>(crawlspaceDto);

            var createdCrawlspace = await _crawlspaceService.AddCrawlspaceAsync(crawlspace);
            var createdCrawlspaceDto = _mapper.Map<CrawlspaceDto>(createdCrawlspace);

            return CreatedAtAction(
                nameof(GetCrawlspaceById),
                new { id = createdCrawlspaceDto.Id },
                createdCrawlspaceDto);
        }

        // PUT: api/foundation/crawlspaces/{id}
        [HttpPut("crawlspaces/{id}")]
        [ProducesResponseType(StatusCodes.Status204NoContent)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<IActionResult> UpdateCrawlspace(Guid id, [FromBody] CrawlspaceDto crawlspaceDto)
        {
            if (crawlspaceDto == null)
            {
                _logger.LogWarning("Crawlspace data is null");
                return BadRequest("Crawlspace data is required");
            }

            if (id != crawlspaceDto.Id)
            {
                _logger.LogWarning("Crawlspace ID mismatch: {PathId} vs {BodyId}", id, crawlspaceDto.Id);
                return BadRequest("The ID in the path must match the ID in the body");
            }

            _logger.LogInformation("Updating crawlspace with ID: {CrawlspaceId}", id);

            // Auto-populate resource text fields if only code provided
            if (!string.IsNullOrEmpty(crawlspaceDto.VentilationType?.Code) &&
                string.IsNullOrEmpty(crawlspaceDto.VentilationType?.English))
            {
                var ventilationType = VentilationTypes.FromCode(crawlspaceDto.VentilationType.Code);
                crawlspaceDto.VentilationType.English = ventilationType.English;
                crawlspaceDto.VentilationType.French = ventilationType.French;
            }

            var existingCrawlspace = await _crawlspaceService.GetCrawlspaceByIdAsync(id);
            if (existingCrawlspace == null)
            {
                _logger.LogWarning("Crawlspace with ID: {CrawlspaceId} not found for update", id);
                return NotFound();
            }

            var crawlspace = _mapper.Map<Crawlspace>(crawlspaceDto);
            crawlspace.Id = id;

            await _crawlspaceService.UpdateCrawlspaceAsync(crawlspace);
            return NoContent();
        }

        // DELETE: api/foundation/crawlspaces/{id}
        [HttpDelete("crawlspaces/{id}")]
        [ProducesResponseType(StatusCodes.Status204NoContent)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<IActionResult> DeleteCrawlspace(Guid id)
        {
            try
            {
                _logger.LogInformation("Deleting crawlspace with ID: {CrawlspaceId}", id);

                var existingCrawlspace = await _crawlspaceService.GetCrawlspaceByIdAsync(id);
                if (existingCrawlspace == null)
                {
                    _logger.LogWarning("Crawlspace with ID: {CrawlspaceId} not found for deletion", id);
                    return NotFound();
                }

                await _crawlspaceService.DeleteCrawlspaceAsync(id);
                return NoContent();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting crawlspace with ID: {CrawlspaceId}", id);
                return StatusCode(StatusCodes.Status500InternalServerError, "An error occurred while deleting the crawlspace");
            }
        }

        // GET: api/foundation/energy-upgrades/{energyUpgradeId}/slabs
        [HttpGet("energy-upgrades/{energyUpgradeId}/slabs")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<ActionResult<SlabDto>> GetSlabByEnergyUpgradeId(Guid energyUpgradeId)
        {
            _logger.LogInformation("Getting slab for energy upgrade ID: {EnergyUpgradeId}", energyUpgradeId);

            var slab = await _slabService.GetSlabByEnergyUpgradeIdAsync(energyUpgradeId);

            if (slab == null)
            {
                _logger.LogWarning("Slab not found for energy upgrade ID: {EnergyUpgradeId}", energyUpgradeId);
                return NotFound($"Slab not found for energy upgrade ID: {energyUpgradeId}");
            }

            var slabDto = _mapper.Map<SlabDto>(slab);
            return Ok(slabDto);
        }

        // POST: api/foundation/slabs/{baseSlabId}/duplicate-for-energy-upgrade
        [HttpPost("slabs/{baseSlabId}/duplicate-for-energy-upgrade")]
        [ProducesResponseType(StatusCodes.Status201Created)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        public async Task<ActionResult<SlabDto>> DuplicateSlabForEnergyUpgrade(
            Guid baseSlabId,
            [FromQuery] Guid energyUpgradeId)
        {
            try
            {
                _logger.LogInformation("Duplicating slab {BaseSlabId} for energy upgrade {EnergyUpgradeId}",
                    baseSlabId, energyUpgradeId);

                // Get the base slab
                var baseSlab = await _slabService.GetSlabByIdAsync(baseSlabId);
                if (baseSlab == null)
                {
                    _logger.LogWarning("Slab with ID: {BaseSlabId} not found", baseSlabId);
                    return NotFound($"Slab with ID {baseSlabId} not found");
                }

                // Create a duplicate with new ID but same values
                var duplicatedSlab = await _slabService.DuplicateSlabForEnergyUpgradeAsync(baseSlab, energyUpgradeId);

                var duplicatedSlabDto = _mapper.Map<SlabDto>(duplicatedSlab);

                _logger.LogInformation("Successfully duplicated slab {BaseSlabId} as {NewSlabId} for energy upgrade {EnergyUpgradeId}",
                    baseSlabId, duplicatedSlab.Id, energyUpgradeId);

                return CreatedAtAction(nameof(GetSlabById),
                    new { id = duplicatedSlab.Id },
                    duplicatedSlabDto);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error duplicating slab {BaseSlabId} for energy upgrade {EnergyUpgradeId}",
                    baseSlabId, energyUpgradeId);
                return StatusCode(StatusCodes.Status500InternalServerError,
                    "An error occurred while duplicating the slab for energy upgrade");
            }
        }

        // ===============================
        // SLAB ENDPOINTS (Following EnvelopeService pattern)
        // ===============================

        // GET: api/foundation/{houseId}/slabs
        [HttpGet("{houseId}/slabs")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<ActionResult<List<SlabDto>>> GetSlabs(Guid houseId)
        {
            _logger.LogInformation("Getting all slabs for house with ID: {HouseId}", houseId);

            var slabs = await _slabService.GetSlabsByHouseIdAsync(houseId);

            if (slabs == null || !slabs.Any())
            {
                _logger.LogWarning("No slabs found for house with ID: {HouseId}", houseId);
                return NotFound();
            }

            var slabDtos = _mapper.Map<List<SlabDto>>(slabs);
            return Ok(slabDtos);
        }

        // GET: api/foundation/slabs/{id}
        [HttpGet("slabs/{id}")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<ActionResult<SlabDto>> GetSlabById(Guid id)
        {
            _logger.LogInformation("Getting slab with ID: {SlabId}", id);

            var slab = await _slabService.GetSlabByIdAsync(id);

            if (slab == null)
            {
                _logger.LogWarning("Slab with ID: {SlabId} not found", id);
                return NotFound();
            }

            var slabDto = _mapper.Map<SlabDto>(slab);
            return Ok(slabDto);
        }

        // GET: api/foundation/slabs
        [HttpGet("slabs")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        public async Task<ActionResult<List<SlabDto>>> GetAllSlabs()
        {
            _logger.LogInformation("Getting all slabs");

            var slabs = await _slabService.GetAllSlabsAsync();
            var slabDtos = _mapper.Map<List<SlabDto>>(slabs);

            return Ok(slabDtos);
        }

        // POST: api/foundation/slabs
        [HttpPost("slabs")]
        [ProducesResponseType(StatusCodes.Status201Created)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        public async Task<ActionResult<SlabDto>> AddSlab([FromBody] SlabDto slabDto)
        {
            if (slabDto == null)
            {
                _logger.LogWarning("Slab data is null");
                return BadRequest("Slab data is required");
            }

            _logger.LogInformation("Creating new slab for house ID: {HouseId}", slabDto.HouseId);

            var slab = _mapper.Map<Slab>(slabDto);

            var createdSlab = await _slabService.AddSlabAsync(slab);
            var createdSlabDto = _mapper.Map<SlabDto>(createdSlab);

            return CreatedAtAction(
                nameof(GetSlabById),
                new { id = createdSlabDto.Id },
                createdSlabDto);
        }

        // PUT: api/foundation/slabs/{id}
        [HttpPut("slabs/{id}")]
        [ProducesResponseType(StatusCodes.Status204NoContent)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<IActionResult> UpdateSlab(Guid id, [FromBody] SlabDto slabDto)
        {
            if (slabDto == null)
            {
                _logger.LogWarning("Slab data is null");
                return BadRequest("Slab data is required");
            }

            if (id != slabDto.Id)
            {
                _logger.LogWarning("Slab ID mismatch: {PathId} vs {BodyId}", id, slabDto.Id);
                return BadRequest("The ID in the path must match the ID in the body");
            }

            _logger.LogInformation("Updating slab with ID: {SlabId}", id);

            var existingSlab = await _slabService.GetSlabByIdAsync(id);
            if (existingSlab == null)
            {
                _logger.LogWarning("Slab with ID: {SlabId} not found for update", id);
                return NotFound();
            }

            var slab = _mapper.Map<Slab>(slabDto);
            slab.Id = id;

            await _slabService.UpdateSlabAsync(slab);
            return NoContent();
        }

        // DELETE: api/foundation/slabs/{id}
        [HttpDelete("slabs/{id}")]
        [ProducesResponseType(StatusCodes.Status204NoContent)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<IActionResult> DeleteSlab(Guid id)
        {
            try
            {
                _logger.LogInformation("Deleting slab with ID: {SlabId}", id);

                var existingSlab = await _slabService.GetSlabByIdAsync(id);
                if (existingSlab == null)
                {
                    _logger.LogWarning("Slab with ID: {SlabId} not found for deletion", id);
                    return NotFound();
                }

                await _slabService.DeleteSlabAsync(id);
                return NoContent();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting slab with ID: {SlabId}", id);
                return StatusCode(StatusCodes.Status500InternalServerError, "An error occurred while deleting the slab");
            }
        }

        // GET: api/foundation/energy-upgrades/{energyUpgradeId}/walkouts
        [HttpGet("energy-upgrades/{energyUpgradeId}/walkouts")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<ActionResult<WalkoutDto>> GetWalkoutByEnergyUpgradeId(Guid energyUpgradeId)
        {
            _logger.LogInformation("Getting walkout for energy upgrade ID: {EnergyUpgradeId}", energyUpgradeId);

            var walkout = await _walkoutService.GetWalkoutByEnergyUpgradeIdAsync(energyUpgradeId);

            if (walkout == null)
            {
                _logger.LogWarning("Walkout not found for energy upgrade ID: {EnergyUpgradeId}", energyUpgradeId);
                return NotFound($"Walkout not found for energy upgrade ID: {energyUpgradeId}");
            }

            var walkoutDto = _mapper.Map<WalkoutDto>(walkout);
            return Ok(walkoutDto);
        }

        // POST: api/foundation/walkouts/{baseWalkoutId}/duplicate-for-energy-upgrade
        [HttpPost("walkouts/{baseWalkoutId}/duplicate-for-energy-upgrade")]
        [ProducesResponseType(StatusCodes.Status201Created)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        public async Task<ActionResult<WalkoutDto>> DuplicateWalkoutForEnergyUpgrade(
            Guid baseWalkoutId,
            [FromQuery] Guid energyUpgradeId)
        {
            try
            {
                _logger.LogInformation("Duplicating walkout {BaseWalkoutId} for energy upgrade {EnergyUpgradeId}",
                    baseWalkoutId, energyUpgradeId);

                // Get the base walkout
                var baseWalkout = await _walkoutService.GetWalkoutByIdAsync(baseWalkoutId);
                if (baseWalkout == null)
                {
                    _logger.LogWarning("Walkout with ID: {BaseWalkoutId} not found", baseWalkoutId);
                    return NotFound($"Walkout with ID {baseWalkoutId} not found");
                }

                // Create a duplicate with new ID but same values
                var duplicatedWalkout = await _walkoutService.DuplicateWalkoutForEnergyUpgradeAsync(baseWalkout, energyUpgradeId);

                var duplicatedWalkoutDto = _mapper.Map<WalkoutDto>(duplicatedWalkout);

                _logger.LogInformation("Successfully duplicated walkout {BaseWalkoutId} as {NewWalkoutId} for energy upgrade {EnergyUpgradeId}",
                    baseWalkoutId, duplicatedWalkout.Id, energyUpgradeId);

                return CreatedAtAction(nameof(GetWalkoutById),
                    new { id = duplicatedWalkout.Id },
                    duplicatedWalkoutDto);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error duplicating walkout {BaseWalkoutId} for energy upgrade {EnergyUpgradeId}",
                    baseWalkoutId, energyUpgradeId);
                return StatusCode(StatusCodes.Status500InternalServerError,
                    "An error occurred while duplicating the walkout for energy upgrade");
            }
        }

        // ===============================
        // WALKOUT ENDPOINTS (Following EnvelopeService pattern)
        // ===============================

        // GET: api/foundation/{houseId}/walkouts
        [HttpGet("{houseId}/walkouts")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<ActionResult<List<WalkoutDto>>> GetWalkouts(Guid houseId)
        {
            _logger.LogInformation("Getting all walkouts for house with ID: {HouseId}", houseId);

            var walkouts = await _walkoutService.GetWalkoutsByHouseIdAsync(houseId);

            if (walkouts == null || !walkouts.Any())
            {
                _logger.LogWarning("No walkouts found for house with ID: {HouseId}", houseId);
                return NotFound();
            }

            var walkoutDtos = _mapper.Map<List<WalkoutDto>>(walkouts);
            return Ok(walkoutDtos);
        }

        // GET: api/foundation/walkouts/{id}
        [HttpGet("walkouts/{id}")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<ActionResult<WalkoutDto>> GetWalkoutById(Guid id)
        {
            _logger.LogInformation("Getting walkout with ID: {WalkoutId}", id);

            var walkout = await _walkoutService.GetWalkoutByIdAsync(id);

            if (walkout == null)
            {
                _logger.LogWarning("Walkout with ID: {WalkoutId} not found", id);
                return NotFound();
            }

            var walkoutDto = _mapper.Map<WalkoutDto>(walkout);
            return Ok(walkoutDto);
        }

        // GET: api/foundation/walkouts
        [HttpGet("walkouts")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        public async Task<ActionResult<List<WalkoutDto>>> GetAllWalkouts()
        {
            _logger.LogInformation("Getting all walkouts");

            var walkouts = await _walkoutService.GetAllWalkoutsAsync();
            var walkoutDtos = _mapper.Map<List<WalkoutDto>>(walkouts);

            return Ok(walkoutDtos);
        }

        // POST: api/foundation/walkouts
        [HttpPost("walkouts")]
        [ProducesResponseType(StatusCodes.Status201Created)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        public async Task<ActionResult<WalkoutDto>> AddWalkout([FromBody] WalkoutDto walkoutDto)
        {
            if (walkoutDto == null)
            {
                _logger.LogWarning("Walkout data is null");
                return BadRequest("Walkout data is required");
            }

            _logger.LogInformation("Creating new walkout for house ID: {HouseId}", walkoutDto.HouseId);

            // Auto-populate resource text fields if only code provided
            if (!string.IsNullOrEmpty(walkoutDto.OpeningUpstairs?.Code) &&
                string.IsNullOrEmpty(walkoutDto.OpeningUpstairs?.English))
            {
                var opening = OpeningsUpstairs.FromCode(walkoutDto.OpeningUpstairs.Code);
                walkoutDto.OpeningUpstairs.English = opening.English;
                walkoutDto.OpeningUpstairs.French = opening.French;
            }

            if (!string.IsNullOrEmpty(walkoutDto.RoomType?.Code) &&
                string.IsNullOrEmpty(walkoutDto.RoomType?.English))
            {
                var roomType = RoomTypes.FromCode(walkoutDto.RoomType.Code);
                walkoutDto.RoomType.English = roomType.English;
                walkoutDto.RoomType.French = roomType.French;
            }

            var walkout = _mapper.Map<Walkout>(walkoutDto);

            var createdWalkout = await _walkoutService.AddWalkoutAsync(walkout);
            var createdWalkoutDto = _mapper.Map<WalkoutDto>(createdWalkout);

            return CreatedAtAction(
                nameof(GetWalkoutById),
                new { id = createdWalkoutDto.Id },
                createdWalkoutDto);
        }

        // PUT: api/foundation/walkouts/{id}
        [HttpPut("walkouts/{id}")]
        [ProducesResponseType(StatusCodes.Status204NoContent)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<IActionResult> UpdateWalkout(Guid id, [FromBody] WalkoutDto walkoutDto)
        {
            if (walkoutDto == null)
            {
                _logger.LogWarning("Walkout data is null");
                return BadRequest("Walkout data is required");
            }

            if (id != walkoutDto.Id)
            {
                _logger.LogWarning("Walkout ID mismatch: {PathId} vs {BodyId}", id, walkoutDto.Id);
                return BadRequest("The ID in the path must match the ID in the body");
            }

            _logger.LogInformation("Updating walkout with ID: {WalkoutId}", id);

            // Auto-populate resource text fields if only code provided
            if (!string.IsNullOrEmpty(walkoutDto.OpeningUpstairs?.Code) &&
                string.IsNullOrEmpty(walkoutDto.OpeningUpstairs?.English))
            {
                var opening = OpeningsUpstairs.FromCode(walkoutDto.OpeningUpstairs.Code);
                walkoutDto.OpeningUpstairs.English = opening.English;
                walkoutDto.OpeningUpstairs.French = opening.French;
            }

            if (!string.IsNullOrEmpty(walkoutDto.RoomType?.Code) &&
                string.IsNullOrEmpty(walkoutDto.RoomType?.English))
            {
                var roomType = RoomTypes.FromCode(walkoutDto.RoomType.Code);
                walkoutDto.RoomType.English = roomType.English;
                walkoutDto.RoomType.French = roomType.French;
            }

            var existingWalkout = await _walkoutService.GetWalkoutByIdAsync(id);
            if (existingWalkout == null)
            {
                _logger.LogWarning("Walkout with ID: {WalkoutId} not found for update", id);
                return NotFound();
            }

            var walkout = _mapper.Map<Walkout>(walkoutDto);
            walkout.Id = id;

            await _walkoutService.UpdateWalkoutAsync(walkout);
            return NoContent();
        }

        // DELETE: api/foundation/walkouts/{id}
        [HttpDelete("walkouts/{id}")]
        [ProducesResponseType(StatusCodes.Status204NoContent)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<IActionResult> DeleteWalkout(Guid id)
        {
            try
            {
                _logger.LogInformation("Deleting walkout with ID: {WalkoutId}", id);

                var existingWalkout = await _walkoutService.GetWalkoutByIdAsync(id);
                if (existingWalkout == null)
                {
                    _logger.LogWarning("Walkout with ID: {WalkoutId} not found for deletion", id);
                    return NotFound();
                }

                await _walkoutService.DeleteWalkoutAsync(id);
                return NoContent();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting walkout with ID: {WalkoutId}", id);
                return StatusCode(StatusCodes.Status500InternalServerError, "An error occurred while deleting the walkout");
            }
        }
    }
}