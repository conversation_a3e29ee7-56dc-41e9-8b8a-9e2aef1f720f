using System;

namespace FoundationService.API.Models
{
    /// <summary>
    /// Base DTO for foundation entities
    /// </summary>
    public class FoundationDto
    {
        public Guid Id { get; set; }
        public Guid HouseId { get; set; }
        public Guid? EnergyUpgradeId { get; set; }
        public string Label { get; set; } = string.Empty;
        public bool IsExposedSurface { get; set; } = false;
        public decimal? ExposedSurfacePerimeter { get; set; } = null;
        public ConfigurationDto Configuration { get; set; } = new ConfigurationDto();

        // Computed properties
        public decimal InteriorWallCoreRsiValue { get; set; }
        public decimal InteriorWallEffectiveRsiValue { get; set; }
    }
}