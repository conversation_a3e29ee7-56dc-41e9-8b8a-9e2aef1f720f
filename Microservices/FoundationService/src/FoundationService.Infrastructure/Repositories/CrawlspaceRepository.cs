using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using FoundationService.Core.Interfaces;
using FoundationService.Core.Models;
using FoundationService.Infrastructure.Data;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace FoundationService.Infrastructure.Repositories
{
    /// <summary>
    /// Repository implementation for crawlspace operations following EnvelopeService pattern
    /// </summary>
    public class CrawlspaceRepository : ICrawlspaceRepository
    {
        private readonly FoundationDbContext _context;
        private readonly ILogger<CrawlspaceRepository> _logger;

        public CrawlspaceRepository(FoundationDbContext context, ILogger<CrawlspaceRepository> logger)
        {
            _context = context ?? throw new ArgumentNullException(nameof(context));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        }

        public async Task<IEnumerable<Crawlspace>> GetCrawlspacesByHouseIdAsync(Guid houseId)
        {
            _logger.LogInformation("Getting crawlspaces for house ID: {HouseId}", houseId);
            
            return await _context.Crawlspaces
                .Include(c => c.Configuration)
                .Include(c => c.Floor)
                    .ThenInclude(f => f.Construction)
                .Include(c => c.Floor)
                    .ThenInclude(f => f.Measurements)
                .Include(c => c.Wall)
                    .ThenInclude(w => w.Construction)
                .Include(c => c.Wall)
                    .ThenInclude(w => w.Measurements)
                .Include(c => c.Wall)
                    .ThenInclude(w => w.RValues)
                .Where(c => c.HouseId == houseId)
                .ToListAsync();
        }

        public async Task<Crawlspace?> GetCrawlspaceByIdAsync(Guid id)
        {
            _logger.LogInformation("Getting crawlspace by ID: {CrawlspaceId}", id);
            
            return await _context.Crawlspaces
                .AsNoTracking()
                .Include(c => c.Configuration)
                .Include(c => c.Floor)
                    .ThenInclude(f => f.Construction)
                .Include(c => c.Floor)
                    .ThenInclude(f => f.Measurements)
                .Include(c => c.Wall)
                    .ThenInclude(w => w.Construction)
                .Include(c => c.Wall)
                    .ThenInclude(w => w.Measurements)
                .Include(c => c.Wall)
                    .ThenInclude(w => w.RValues)
                .FirstOrDefaultAsync(c => c.Id == id);
        }

        public async Task<IEnumerable<Crawlspace>> GetAllCrawlspacesAsync()
        {
            _logger.LogInformation("Getting all crawlspaces");
            
            return await _context.Crawlspaces
                .Include(c => c.Configuration)
                .Include(c => c.Floor)
                    .ThenInclude(f => f.Construction)
                .Include(c => c.Floor)
                    .ThenInclude(f => f.Measurements)
                .Include(c => c.Wall)
                    .ThenInclude(w => w.Construction)
                .Include(c => c.Wall)
                    .ThenInclude(w => w.Measurements)
                .Include(c => c.Wall)
                    .ThenInclude(w => w.RValues)
                .ToListAsync();
        }

        public async Task<Crawlspace> AddCrawlspaceAsync(Crawlspace crawlspace)
        {
            _logger.LogInformation("Adding crawlspace for house ID: {HouseId}", crawlspace.HouseId);
            
            _context.Crawlspaces.Add(crawlspace);
            await _context.SaveChangesAsync();
            
            return crawlspace;
        }

        public async Task UpdateCrawlspaceAsync(Crawlspace crawlspace)
        {
            _logger.LogInformation("Updating crawlspace with ID: {CrawlspaceId}", crawlspace.Id);
            
            var existingCrawlspace = await _context.Crawlspaces
                .Include(c => c.Configuration)
                .Include(c => c.Floor)
                    .ThenInclude(f => f.Construction)
                .Include(c => c.Floor)
                    .ThenInclude(f => f.Measurements)
                .Include(c => c.Wall)
                    .ThenInclude(w => w.Construction)
                .Include(c => c.Wall)
                    .ThenInclude(w => w.Measurements)
                .Include(c => c.Wall)
                    .ThenInclude(w => w.RValues)
                .FirstOrDefaultAsync(c => c.Id == crawlspace.Id);

            if (existingCrawlspace == null)
            {
                throw new InvalidOperationException($"Crawlspace with ID {crawlspace.Id} not found");
            }

            // Update properties manually following EnvelopeService pattern
            existingCrawlspace.HouseId = crawlspace.HouseId;
            existingCrawlspace.Label = crawlspace.Label;
            existingCrawlspace.IsExposedSurface = crawlspace.IsExposedSurface;
            existingCrawlspace.ExposedSurfacePerimeter = crawlspace.ExposedSurfacePerimeter;

            // Update owned entities
            if (crawlspace.VentilationType != null)
            {
                existingCrawlspace.VentilationType = crawlspace.VentilationType;
            }

            // Update configuration
            if (crawlspace.Configuration != null && existingCrawlspace.Configuration != null)
            {
                existingCrawlspace.Configuration.Type = crawlspace.Configuration.Type;
                existingCrawlspace.Configuration.Subtype = crawlspace.Configuration.Subtype;
                existingCrawlspace.Configuration.Overlap = crawlspace.Configuration.Overlap;
            }

            // Update floor
            if (crawlspace.Floor != null && existingCrawlspace.Floor != null)
            {
                if (crawlspace.Floor.Construction != null && existingCrawlspace.Floor.Construction != null)
                {
                    existingCrawlspace.Floor.Construction.IsBelowFrostline = crawlspace.Floor.Construction.IsBelowFrostline;
                    existingCrawlspace.Floor.Construction.HasIntegralFooting = crawlspace.Floor.Construction.HasIntegralFooting;
                    existingCrawlspace.Floor.Construction.HeatedFloor = crawlspace.Floor.Construction.HeatedFloor;
                    existingCrawlspace.Floor.Construction.AddedToSlab = crawlspace.Floor.Construction.AddedToSlab;
                    existingCrawlspace.Floor.Construction.FloorsAbove = crawlspace.Floor.Construction.FloorsAbove;
                }

                if (crawlspace.Floor.Measurements != null && existingCrawlspace.Floor.Measurements != null)
                {
                    existingCrawlspace.Floor.Measurements.IsRectangular = crawlspace.Floor.Measurements.IsRectangular;
                    existingCrawlspace.Floor.Measurements.Area = crawlspace.Floor.Measurements.Area;
                    existingCrawlspace.Floor.Measurements.Width = crawlspace.Floor.Measurements.Width;
                    existingCrawlspace.Floor.Measurements.Length = crawlspace.Floor.Measurements.Length;
                    existingCrawlspace.Floor.Measurements.Perimeter = crawlspace.Floor.Measurements.Perimeter;
                }
            }

            // Update wall
            if (crawlspace.Wall != null && existingCrawlspace.Wall != null)
            {
                // Update construction (optional for open crawlspaces)
                if (crawlspace.Wall.Construction != null && existingCrawlspace.Wall.Construction != null)
                {
                    existingCrawlspace.Wall.Construction.Corners = crawlspace.Wall.Construction.Corners;
                    existingCrawlspace.Wall.Construction.Type = crawlspace.Wall.Construction.Type;
                    existingCrawlspace.Wall.Construction.Lintels = crawlspace.Wall.Construction.Lintels;
                }

                if (crawlspace.Wall.Measurements != null && existingCrawlspace.Wall.Measurements != null)
                {
                    existingCrawlspace.Wall.Measurements.Height = crawlspace.Wall.Measurements.Height;
                    existingCrawlspace.Wall.Measurements.Depth = crawlspace.Wall.Measurements.Depth;
                }

                if (crawlspace.Wall.RValues != null && existingCrawlspace.Wall.RValues != null)
                {
                    existingCrawlspace.Wall.RValues.Skirt = crawlspace.Wall.RValues.Skirt;
                    existingCrawlspace.Wall.RValues.ThermalBreak = crawlspace.Wall.RValues.ThermalBreak;
                }
            }

            _context.Entry(existingCrawlspace).State = EntityState.Modified;
            await _context.SaveChangesAsync();
        }

        public async Task DeleteCrawlspaceAsync(Guid id)
        {
            _logger.LogInformation("Deleting crawlspace with ID: {CrawlspaceId}", id);

            var crawlspace = await _context.Crawlspaces.FindAsync(id);
            if (crawlspace != null)
            {
                _context.Crawlspaces.Remove(crawlspace);
                await _context.SaveChangesAsync();
            }
        }

        public async Task<Crawlspace?> GetCrawlspaceByEnergyUpgradeIdAsync(Guid energyUpgradeId)
        {
            _logger.LogInformation("Getting crawlspace for energy upgrade ID: {EnergyUpgradeId}", energyUpgradeId);

            return await _context.Crawlspaces
                .Where(c => c.EnergyUpgradeId == energyUpgradeId)
                .Include(c => c.Configuration)
                .Include(c => c.Floor)
                    .ThenInclude(f => f.Construction)
                .Include(c => c.Floor)
                    .ThenInclude(f => f.Measurements)
                .Include(c => c.Wall)
                    .ThenInclude(w => w.Construction)
                .Include(c => c.Wall)
                    .ThenInclude(w => w.Measurements)
                .Include(c => c.Wall)
                    .ThenInclude(w => w.RValues)
                .FirstOrDefaultAsync();
        }

        public async Task<Crawlspace> DuplicateCrawlspaceForEnergyUpgradeAsync(Crawlspace baseCrawlspace, Guid energyUpgradeId)
        {
            _logger.LogInformation("Duplicating crawlspace {CrawlspaceId} for energy upgrade {EnergyUpgradeId}",
                baseCrawlspace.Id, energyUpgradeId);

            // Create a deep copy of the crawlspace
            var duplicatedCrawlspace = new Crawlspace(baseCrawlspace)
            {
                Id = Guid.NewGuid(),
                EnergyUpgradeId = energyUpgradeId,
                FloorId = Guid.NewGuid(),
                WallId = Guid.NewGuid()
            };

            // Deep copy Floor
            if (baseCrawlspace.Floor != null)
            {
                duplicatedCrawlspace.Floor = new FoundationFloor(baseCrawlspace.Floor)
                {
                    Id = duplicatedCrawlspace.FloorId,
                    ConstructionId = Guid.NewGuid(),
                    MeasurementsId = Guid.NewGuid()
                };

                if (baseCrawlspace.Floor.Construction != null)
                {
                    duplicatedCrawlspace.Floor.Construction = new FoundationFloorConstruction(baseCrawlspace.Floor.Construction)
                    {
                        Id = duplicatedCrawlspace.Floor.ConstructionId
                    };
                }

                if (baseCrawlspace.Floor.Measurements != null)
                {
                    duplicatedCrawlspace.Floor.Measurements = new FoundationMeasurements(baseCrawlspace.Floor.Measurements)
                    {
                        Id = duplicatedCrawlspace.Floor.MeasurementsId
                    };
                }
            }

            // Deep copy Wall
            if (baseCrawlspace.Wall != null)
            {
                duplicatedCrawlspace.Wall = new CrawlspaceWall(baseCrawlspace.Wall)
                {
                    Id = duplicatedCrawlspace.WallId,
                    ConstructionId = baseCrawlspace.Wall.Construction != null ? Guid.NewGuid() : (Guid?)null,
                    MeasurementsId = Guid.NewGuid(),
                    RValuesId = Guid.NewGuid()
                };

                if (baseCrawlspace.Wall.Construction != null)
                {
                    duplicatedCrawlspace.Wall.Construction = new CrawlspaceWallConstruction(baseCrawlspace.Wall.Construction)
                    {
                        Id = duplicatedCrawlspace.Wall.ConstructionId.Value
                    };
                }

                if (baseCrawlspace.Wall.Measurements != null)
                {
                    duplicatedCrawlspace.Wall.Measurements = new CrawlspaceWallMeasurements(baseCrawlspace.Wall.Measurements)
                    {
                        Id = duplicatedCrawlspace.Wall.MeasurementsId
                    };
                }

                if (baseCrawlspace.Wall.RValues != null)
                {
                    duplicatedCrawlspace.Wall.RValues = new WallRValues(baseCrawlspace.Wall.RValues)
                    {
                        Id = duplicatedCrawlspace.Wall.RValuesId
                    };
                }
            }

            _context.Crawlspaces.Add(duplicatedCrawlspace);
            await _context.SaveChangesAsync();
            return duplicatedCrawlspace;
        }
    }
}
