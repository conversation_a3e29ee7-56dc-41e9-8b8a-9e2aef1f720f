using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using FoundationService.Core.Interfaces;
using FoundationService.Core.Models;
using FoundationService.Infrastructure.Data;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace FoundationService.Infrastructure.Repositories
{
    /// <summary>
    /// Repository implementation for walkout operations following EnvelopeService pattern
    /// </summary>
    public class WalkoutRepository : IWalkoutRepository
    {
        private readonly FoundationDbContext _context;
        private readonly ILogger<WalkoutRepository> _logger;

        public WalkoutRepository(FoundationDbContext context, ILogger<WalkoutRepository> logger)
        {
            _context = context ?? throw new ArgumentNullException(nameof(context));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        }

        public async Task<IEnumerable<Walkout>> GetWalkoutsByHouseIdAsync(Guid houseId)
        {
            _logger.LogInformation("Getting walkouts for house ID: {HouseId}", houseId);
            
            return await _context.Walkouts
                .Include(w => w.Configuration)
                .Include(w => w.Measurements)
                .Include(w => w.Floor)
                    .ThenInclude(f => f.Construction)
                .Include(w => w.Wall)
                    .ThenInclude(wall => wall.Construction)
                .Include(w => w.Wall)
                    .ThenInclude(wall => wall.Measurements)
                .Include(w => w.ExteriorSurfaces)
                .Include(w => w.Locations)
                    .ThenInclude(l => l.L1_1)
                .Include(w => w.Locations)
                    .ThenInclude(l => l.L1_2)
                .Include(w => w.Locations)
                    .ThenInclude(l => l.L2_1)
                .Include(w => w.Locations)
                    .ThenInclude(l => l.L2_2)
                .Where(w => w.HouseId == houseId)
                .ToListAsync();
        }

        public async Task<Walkout?> GetWalkoutByIdAsync(Guid id)
        {
            _logger.LogInformation("Getting walkout by ID: {WalkoutId}", id);
            
            return await _context.Walkouts
                .AsNoTracking()
                .Include(w => w.Configuration)
                .Include(w => w.Measurements)
                .Include(w => w.Floor)
                    .ThenInclude(f => f.Construction)
                .Include(w => w.Wall)
                    .ThenInclude(wall => wall.Construction)
                .Include(w => w.Wall)
                    .ThenInclude(wall => wall.Measurements)
                .Include(w => w.ExteriorSurfaces)
                .Include(w => w.Locations)
                    .ThenInclude(l => l.L1_1)
                .Include(w => w.Locations)
                    .ThenInclude(l => l.L1_2)
                .Include(w => w.Locations)
                    .ThenInclude(l => l.L2_1)
                .Include(w => w.Locations)
                    .ThenInclude(l => l.L2_2)
                .FirstOrDefaultAsync(w => w.Id == id);
        }

        public async Task<IEnumerable<Walkout>> GetAllWalkoutsAsync()
        {
            _logger.LogInformation("Getting all walkouts");
            
            return await _context.Walkouts
                .Include(w => w.Configuration)
                .Include(w => w.Measurements)
                .Include(w => w.Floor)
                    .ThenInclude(f => f.Construction)
                .Include(w => w.Wall)
                    .ThenInclude(wall => wall.Construction)
                .Include(w => w.Wall)
                    .ThenInclude(wall => wall.Measurements)
                .Include(w => w.ExteriorSurfaces)
                .Include(w => w.Locations)
                    .ThenInclude(l => l.L1_1)
                .Include(w => w.Locations)
                    .ThenInclude(l => l.L1_2)
                .Include(w => w.Locations)
                    .ThenInclude(l => l.L2_1)
                .Include(w => w.Locations)
                    .ThenInclude(l => l.L2_2)
                .ToListAsync();
        }

        public async Task<Walkout> AddWalkoutAsync(Walkout walkout)
        {
            _logger.LogInformation("Adding walkout for house ID: {HouseId}", walkout.HouseId);
            
            _context.Walkouts.Add(walkout);
            await _context.SaveChangesAsync();
            
            return walkout;
        }

        public async Task UpdateWalkoutAsync(Walkout walkout)
        {
            _logger.LogInformation("Updating walkout with ID: {WalkoutId}", walkout.Id);
            
            var existingWalkout = await _context.Walkouts
                .Include(w => w.Configuration)
                .Include(w => w.Measurements)
                .Include(w => w.Floor)
                    .ThenInclude(f => f.Construction)
                .Include(w => w.Wall)
                    .ThenInclude(wall => wall.Construction)
                .Include(w => w.Wall)
                    .ThenInclude(wall => wall.Measurements)
                .Include(w => w.ExteriorSurfaces)
                .Include(w => w.Locations)
                    .ThenInclude(l => l.L1_1)
                .Include(w => w.Locations)
                    .ThenInclude(l => l.L1_2)
                .Include(w => w.Locations)
                    .ThenInclude(l => l.L2_1)
                .Include(w => w.Locations)
                    .ThenInclude(l => l.L2_2)
                .FirstOrDefaultAsync(w => w.Id == walkout.Id);

            if (existingWalkout == null)
            {
                throw new InvalidOperationException($"Walkout with ID {walkout.Id} not found");
            }

            // Update properties manually following EnvelopeService pattern
            existingWalkout.HouseId = walkout.HouseId;
            existingWalkout.Label = walkout.Label;
            existingWalkout.IsExposedSurface = walkout.IsExposedSurface;
            existingWalkout.ExposedSurfacePerimeter = walkout.ExposedSurfacePerimeter;

            // Update owned entities
            if (walkout.OpeningUpstairs != null)
            {
                existingWalkout.OpeningUpstairs = walkout.OpeningUpstairs;
            }

            if (walkout.RoomType != null)
            {
                existingWalkout.RoomType = walkout.RoomType;
            }

            // Update configuration
            if (walkout.Configuration != null && existingWalkout.Configuration != null)
            {
                existingWalkout.Configuration.Type = walkout.Configuration.Type;
                existingWalkout.Configuration.Subtype = walkout.Configuration.Subtype;
                existingWalkout.Configuration.Overlap = walkout.Configuration.Overlap;
            }

            // Update measurements
            if (walkout.Measurements != null && existingWalkout.Measurements != null)
            {
                existingWalkout.Measurements.WithSlab = walkout.Measurements.WithSlab;
                existingWalkout.Measurements.Height = walkout.Measurements.Height;
                existingWalkout.Measurements.D1 = walkout.Measurements.D1;
                existingWalkout.Measurements.D2 = walkout.Measurements.D2;
                existingWalkout.Measurements.D3 = walkout.Measurements.D3;
                existingWalkout.Measurements.D4 = walkout.Measurements.D4;
                existingWalkout.Measurements.D5 = walkout.Measurements.D5;
                existingWalkout.Measurements.L1 = walkout.Measurements.L1;
                existingWalkout.Measurements.L2 = walkout.Measurements.L2;
                existingWalkout.Measurements.L3 = walkout.Measurements.L3;
                existingWalkout.Measurements.L4 = walkout.Measurements.L4;
            }

            // Update floor
            if (walkout.Floor != null && existingWalkout.Floor != null)
            {
                if (walkout.Floor.Construction != null && existingWalkout.Floor.Construction != null)
                {
                    existingWalkout.Floor.Construction.IsBelowFrostline = walkout.Floor.Construction.IsBelowFrostline;
                    existingWalkout.Floor.Construction.HasIntegralFooting = walkout.Floor.Construction.HasIntegralFooting;
                    existingWalkout.Floor.Construction.HeatedFloor = walkout.Floor.Construction.HeatedFloor;
                    existingWalkout.Floor.Construction.AddedToSlab = walkout.Floor.Construction.AddedToSlab;
                    existingWalkout.Floor.Construction.FloorsAbove = walkout.Floor.Construction.FloorsAbove;
                }
            }

            // Update wall
            if (walkout.Wall != null && existingWalkout.Wall != null)
            {
                existingWalkout.Wall.HasPonyWall = walkout.Wall.HasPonyWall;

                if (walkout.Wall.Construction != null && existingWalkout.Wall.Construction != null)
                {
                    existingWalkout.Wall.Construction.Corners = walkout.Wall.Construction.Corners;
                    existingWalkout.Wall.Construction.InteriorAddedInsulation = walkout.Wall.Construction.InteriorAddedInsulation;
                    existingWalkout.Wall.Construction.ExteriorAddedInsulation = walkout.Wall.Construction.ExteriorAddedInsulation;
                    existingWalkout.Wall.Construction.Lintels = walkout.Wall.Construction.Lintels;
                    existingWalkout.Wall.Construction.PonyWallType = walkout.Wall.Construction.PonyWallType;
                }

                if (walkout.Wall.Measurements != null && existingWalkout.Wall.Measurements != null)
                {
                    existingWalkout.Wall.Measurements.Height = walkout.Wall.Measurements.Height;
                    existingWalkout.Wall.Measurements.Depth = walkout.Wall.Measurements.Depth;
                    existingWalkout.Wall.Measurements.PonyWallHeight = walkout.Wall.Measurements.PonyWallHeight;
                }
            }

            // Update exterior surfaces
            if (walkout.ExteriorSurfaces != null && existingWalkout.ExteriorSurfaces != null)
            {
                existingWalkout.ExteriorSurfaces.AboveGradeArea = walkout.ExteriorSurfaces.AboveGradeArea;
                existingWalkout.ExteriorSurfaces.BelowGradeArea = walkout.ExteriorSurfaces.BelowGradeArea;
                existingWalkout.ExteriorSurfaces.PonyWallArea = walkout.ExteriorSurfaces.PonyWallArea;
                existingWalkout.ExteriorSurfaces.SlabPerimeter = walkout.ExteriorSurfaces.SlabPerimeter;
            }

            // Update locations
            if (walkout.Locations != null && existingWalkout.Locations != null)
            {
                if (walkout.Locations.L1_1 != null && existingWalkout.Locations.L1_1 != null)
                {
                    existingWalkout.Locations.L1_1.X1 = walkout.Locations.L1_1.X1;
                    existingWalkout.Locations.L1_1.X2 = walkout.Locations.L1_1.X2;
                }

                if (walkout.Locations.L1_2 != null && existingWalkout.Locations.L1_2 != null)
                {
                    existingWalkout.Locations.L1_2.X1 = walkout.Locations.L1_2.X1;
                    existingWalkout.Locations.L1_2.X2 = walkout.Locations.L1_2.X2;
                }

                if (walkout.Locations.L2_1 != null && existingWalkout.Locations.L2_1 != null)
                {
                    existingWalkout.Locations.L2_1.X1 = walkout.Locations.L2_1.X1;
                    existingWalkout.Locations.L2_1.X2 = walkout.Locations.L2_1.X2;
                }

                if (walkout.Locations.L2_2 != null && existingWalkout.Locations.L2_2 != null)
                {
                    existingWalkout.Locations.L2_2.X1 = walkout.Locations.L2_2.X1;
                    existingWalkout.Locations.L2_2.X2 = walkout.Locations.L2_2.X2;
                }
            }

            _context.Entry(existingWalkout).State = EntityState.Modified;
            await _context.SaveChangesAsync();
        }

        public async Task DeleteWalkoutAsync(Guid id)
        {
            _logger.LogInformation("Deleting walkout with ID: {WalkoutId}", id);

            var walkout = await _context.Walkouts.FindAsync(id);
            if (walkout != null)
            {
                _context.Walkouts.Remove(walkout);
                await _context.SaveChangesAsync();
            }
        }

        public async Task<Walkout?> GetWalkoutByEnergyUpgradeIdAsync(Guid energyUpgradeId)
        {
            _logger.LogInformation("Getting walkout for energy upgrade ID: {EnergyUpgradeId}", energyUpgradeId);

            return await _context.Walkouts
                .Where(w => w.EnergyUpgradeId == energyUpgradeId)
                .Include(w => w.Configuration)
                .Include(w => w.Floor)
                    .ThenInclude(f => f.Construction)
                .Include(w => w.Wall)
                    .ThenInclude(w => w.Construction)
                .Include(w => w.Wall)
                    .ThenInclude(w => w.Measurements)
                .Include(w => w.Measurements)
                .Include(w => w.ExteriorSurfaces)
                .Include(w => w.Locations)
                    .ThenInclude(l => l.L1_1)
                .Include(w => w.Locations)
                    .ThenInclude(l => l.L1_2)
                .Include(w => w.Locations)
                    .ThenInclude(l => l.L2_1)
                .Include(w => w.Locations)
                    .ThenInclude(l => l.L2_2)
                .FirstOrDefaultAsync();
        }

        public async Task<Walkout> DuplicateWalkoutForEnergyUpgradeAsync(Walkout baseWalkout, Guid energyUpgradeId)
        {
            _logger.LogInformation("Duplicating walkout {WalkoutId} for energy upgrade {EnergyUpgradeId}",
                baseWalkout.Id, energyUpgradeId);

            // Create a deep copy of the walkout
            var duplicatedWalkout = new Walkout(baseWalkout)
            {
                Id = Guid.NewGuid(),
                EnergyUpgradeId = energyUpgradeId,
                FloorId = Guid.NewGuid(),
                WallId = Guid.NewGuid(),
                MeasurementsId = Guid.NewGuid(),
                ExteriorSurfacesId = Guid.NewGuid(),
                LocationsId = Guid.NewGuid()
            };

            // Deep copy Floor
            if (baseWalkout.Floor != null)
            {
                duplicatedWalkout.Floor = new WalkoutFloor(baseWalkout.Floor)
                {
                    Id = duplicatedWalkout.FloorId,
                    ConstructionId = Guid.NewGuid()
                };

                if (baseWalkout.Floor.Construction != null)
                {
                    duplicatedWalkout.Floor.Construction = new FoundationFloorConstruction(baseWalkout.Floor.Construction)
                    {
                        Id = duplicatedWalkout.Floor.ConstructionId
                    };
                }
            }

            // Deep copy Wall
            if (baseWalkout.Wall != null)
            {
                duplicatedWalkout.Wall = new FoundationWall(baseWalkout.Wall)
                {
                    Id = duplicatedWalkout.WallId,
                    ConstructionId = Guid.NewGuid(),
                    MeasurementsId = Guid.NewGuid()
                };

                if (baseWalkout.Wall.Construction != null)
                {
                    duplicatedWalkout.Wall.Construction = new FoundationWallConstruction(baseWalkout.Wall.Construction)
                    {
                        Id = duplicatedWalkout.Wall.ConstructionId
                    };
                }

                if (baseWalkout.Wall.Measurements != null)
                {
                    duplicatedWalkout.Wall.Measurements = new FoundationWallMeasurements(baseWalkout.Wall.Measurements)
                    {
                        Id = duplicatedWalkout.Wall.MeasurementsId
                    };
                }
            }

            // Deep copy other complex objects
            if (baseWalkout.Measurements != null)
            {
                duplicatedWalkout.Measurements = new WalkoutMeasurements(baseWalkout.Measurements)
                {
                    Id = duplicatedWalkout.MeasurementsId
                };
            }

            if (baseWalkout.ExteriorSurfaces != null)
            {
                duplicatedWalkout.ExteriorSurfaces = new ExteriorSurfaces(baseWalkout.ExteriorSurfaces)
                {
                    Id = duplicatedWalkout.ExteriorSurfacesId
                };
            }

            if (baseWalkout.Locations != null)
            {
                duplicatedWalkout.Locations = new Locations(baseWalkout.Locations)
                {
                    Id = duplicatedWalkout.LocationsId
                };

                // Deep copy location coordinates if they exist
                if (baseWalkout.Locations.L1_1 != null)
                {
                    duplicatedWalkout.Locations.L1_1 = new Location(baseWalkout.Locations.L1_1)
                    {
                        Id = Guid.NewGuid()
                    };
                }

                if (baseWalkout.Locations.L1_2 != null)
                {
                    duplicatedWalkout.Locations.L1_2 = new Location(baseWalkout.Locations.L1_2)
                    {
                        Id = Guid.NewGuid()
                    };
                }

                if (baseWalkout.Locations.L2_1 != null)
                {
                    duplicatedWalkout.Locations.L2_1 = new Location(baseWalkout.Locations.L2_1)
                    {
                        Id = Guid.NewGuid()
                    };
                }

                if (baseWalkout.Locations.L2_2 != null)
                {
                    duplicatedWalkout.Locations.L2_2 = new Location(baseWalkout.Locations.L2_2)
                    {
                        Id = Guid.NewGuid()
                    };
                }
            }

            _context.Walkouts.Add(duplicatedWalkout);
            await _context.SaveChangesAsync();
            return duplicatedWalkout;
        }
    }
}
