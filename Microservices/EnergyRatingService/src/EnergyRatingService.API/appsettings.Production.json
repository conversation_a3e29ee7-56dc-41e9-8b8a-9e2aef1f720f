{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "AllowedHosts": "*", "ConnectionStrings": {"DefaultConnection": "Host=energyrating-db;Database=energyratingservice;Username=********;Password=********"}, "ServiceUrls": {"EnvelopeService": "http://envelope:8080", "HvacService": "http://hvac:8080", "HotWaterService": "http://hotwater:8080", "AirInfiltrationService": "http://airinfiltration:8080", "VentilationService": "http://ventilation:8080", "BaseLoadService": "http://baseload:8080", "FoundationService": "http://foundation:8080", "GenerationService": "http://generation:8080", "TemperatureService": "http://temperature:8080"}}