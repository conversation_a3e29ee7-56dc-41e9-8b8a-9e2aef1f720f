namespace EnergyRatingService.API.Configuration
{
    /// <summary>
    /// Configuration class for microservice URLs
    /// Used for component discovery across all microservices
    /// Follows the same pattern as SimulationEngineService
    /// </summary>
    public class ServiceUrls
    {
        public string EnvelopeService { get; set; } = string.Empty;
        public string HvacService { get; set; } = string.Empty;
        public string HotWaterService { get; set; } = string.Empty;
        public string AirInfiltrationService { get; set; } = string.Empty;
        public string VentilationService { get; set; } = string.Empty;
        public string BaseLoadService { get; set; } = string.Empty;
        public string FoundationService { get; set; } = string.Empty;
        public string GenerationService { get; set; } = string.Empty;
        public string TemperatureService { get; set; } = string.Empty;

        /// <summary>
        /// Get microservice configuration for component discovery
        /// </summary>
        public MicroserviceConfig[] GetMicroserviceConfigs()
        {
            return new[]
            {
                new MicroserviceConfig
                {
                    Name = "EnvelopeService",
                    BaseUrl = EnvelopeService,
                    ApiPrefix = "envelope",
                    Endpoints = new[] { "ceilings", "walls", "floors", "doors", "windows", "floorheaders", "rooms" }
                },
                new MicroserviceConfig
                {
                    Name = "HvacService",
                    BaseUrl = HvacService,
                    ApiPrefix = "HeatingCooling",
                    Endpoints = new[] { "heating-coolings" }
                },
                new MicroserviceConfig
                {
                    Name = "HotWaterService",
                    BaseUrl = HotWaterService,
                    ApiPrefix = "HotWater",
                    Endpoints = new[] { "hotwater" }
                },
                new MicroserviceConfig
                {
                    Name = "AirInfiltrationService",
                    BaseUrl = AirInfiltrationService,
                    ApiPrefix = "AirInfiltration",
                    Endpoints = new[] { "air-infiltrations" }
                },
                new MicroserviceConfig
                {
                    Name = "VentilationService",
                    BaseUrl = VentilationService,
                    ApiPrefix = "Ventilation",
                    Endpoints = new[] { "ventilations" }
                },
                new MicroserviceConfig
                {
                    Name = "BaseLoadService",
                    BaseUrl = BaseLoadService,
                    ApiPrefix = "BaseLoad",
                    Endpoints = new[] { "baseloads" }
                },
                new MicroserviceConfig
                {
                    Name = "FoundationService",
                    BaseUrl = FoundationService,
                    ApiPrefix = "foundation",
                    Endpoints = new[] { "foundations" }
                },
                new MicroserviceConfig
                {
                    Name = "GenerationService",
                    BaseUrl = GenerationService,
                    ApiPrefix = "Generation",
                    Endpoints = new[] { "generations" }
                },
                new MicroserviceConfig
                {
                    Name = "TemperatureService",
                    BaseUrl = TemperatureService,
                    ApiPrefix = "Temperature",
                    Endpoints = new[] { "temperatures" }
                }
            };
        }
    }

    /// <summary>
    /// Configuration for a single microservice
    /// </summary>
    public class MicroserviceConfig
    {
        public string Name { get; set; } = string.Empty;
        public string BaseUrl { get; set; } = string.Empty;
        public string ApiPrefix { get; set; } = string.Empty;
        public string[] Endpoints { get; set; } = Array.Empty<string>();
    }
}
