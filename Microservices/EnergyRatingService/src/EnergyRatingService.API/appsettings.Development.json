{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "AllowedHosts": "*", "ConnectionStrings": {"DefaultConnection": "Server=sql-server,1433;Database=EnergyRatingService;User Id=sa;Password=************;TrustServerCertificate=true;"}, "ServiceUrls": {"EnvelopeService": "http://envelope-api:8080", "HvacService": "http://hvac-api:8080", "HotWaterService": "http://hotwater-api:8080", "AirInfiltrationService": "http://airinfiltration-api:8080", "VentilationService": "http://ventilation-api:8080", "BaseLoadService": "http://baseload-api:8080", "FoundationService": "http://foundation-api:8080", "GenerationService": "http://generation-api:8080", "TemperatureService": "http://temperature-api:8080"}}