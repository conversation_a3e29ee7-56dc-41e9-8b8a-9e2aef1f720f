using BaseLoadService.Core.Models;
using Microsoft.EntityFrameworkCore;

namespace BaseLoadService.Infrastructure.Data
{
    public class BaseLoadDbContext : DbContext
    {
        public BaseLoadDbContext(DbContextOptions<BaseLoadDbContext> options) : base(options) { }
        
        public DbSet<BaseLoad> BaseLoads { get; set; } = null!;
        public DbSet<Occupancy> Occupancies { get; set; } = null!;
        public DbSet<Summary> Summaries { get; set; } = null!;
        public DbSet<WaterUsage> WaterUsages { get; set; } = null!;
        public DbSet<ElectricalUsage> ElectricalUsages { get; set; } = null!;
        public DbSet<AdvancedUserSpecified> AdvancedUserSpecifieds { get; set; } = null!;
        
        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            // Set default schema for all entities in this context
            modelBuilder.HasDefaultSchema("baseload");
            
            base.OnModelCreating(modelBuilder);
            
            // BaseLoad configuration
            modelBuilder.Entity<BaseLoad>(entity =>
            {
                entity.ToTable("BaseLoads", "baseload"); // Explicitly specify table name and schema
                entity.HasKey(e => e.Id);
                entity.Property(e => e.BasementFractionOfInternalGains).HasPrecision(18, 2);
                entity.Property(e => e.CommonSpaceElectricalConsumption).HasPrecision(18, 2);
                entity.Property(e => e.EnergyUpgradeId).IsRequired(false);
                
                entity.HasOne(e => e.Occupancy)
                    .WithOne(e => e.BaseLoad)
                    .HasForeignKey<Occupancy>(e => e.BaseLoadId)
                    .OnDelete(DeleteBehavior.Cascade);
                
                entity.HasOne(e => e.Summary)
                    .WithOne(e => e.BaseLoad)
                    .HasForeignKey<Summary>(e => e.BaseLoadId)
                    .OnDelete(DeleteBehavior.Cascade);
                
                entity.HasOne(e => e.WaterUsage)
                    .WithOne(e => e.BaseLoad)
                    .HasForeignKey<WaterUsage>(e => e.BaseLoadId)
                    .OnDelete(DeleteBehavior.Cascade);
                
                entity.HasOne(e => e.ElectricalUsage)
                    .WithOne(e => e.BaseLoad)
                    .HasForeignKey<ElectricalUsage>(e => e.BaseLoadId)
                    .OnDelete(DeleteBehavior.Cascade);
                
                entity.HasOne(e => e.AdvancedUserSpecified)
                    .WithOne(e => e.BaseLoad)
                    .HasForeignKey<AdvancedUserSpecified>(e => e.BaseLoadId)
                    .OnDelete(DeleteBehavior.Cascade);
            });
            
            // Occupancy configuration
            modelBuilder.Entity<Occupancy>(entity =>
            {
                entity.ToTable("Occupancies", "baseload"); // Specify schema
                entity.HasKey(e => e.Id);
                
                // Rest of your Occupancy configuration...
                entity.OwnsOne(e => e.Adults, a =>
                {
                    a.Property(p => p.Occupants);
                    a.Property(p => p.AtHome).HasPrecision(18, 2);
                    a.Property(p => p.OccupantType).HasConversion<int>();
                });
                
                // Configure Children as owned entity
                entity.OwnsOne(e => e.Children, c =>
                {
                    c.Property(p => p.Occupants);
                    c.Property(p => p.AtHome).HasPrecision(18, 2);
                    c.Property(p => p.OccupantType).HasConversion<int>();
                });
                
                // Configure Infants as owned entity
                entity.OwnsOne(e => e.Infants, i =>
                {
                    i.Property(p => p.Occupants);
                    i.Property(p => p.AtHome).HasPrecision(18, 2);
                    i.Property(p => p.OccupantType).HasConversion<int>();
                });
            });
            
            // Summary configuration
            modelBuilder.Entity<Summary>(entity =>
            {
                entity.ToTable("Summaries", "baseload"); // Specify schema
                entity.HasKey(e => e.Id);
                entity.Property(e => e.IsSpecified);
                entity.Property(e => e.ElectricalAppliances).HasPrecision(18, 2);
                entity.Property(e => e.Lighting).HasPrecision(18, 2);
                entity.Property(e => e.OtherElectric).HasPrecision(18, 2);
                entity.Property(e => e.ExteriorUse).HasPrecision(18, 2);
                entity.Property(e => e.HotWaterLoad).HasPrecision(18, 2);
            });

            // WaterUsage configuration
            modelBuilder.Entity<WaterUsage>(entity => 
            {
                entity.ToTable("WaterUsages", "baseload"); // Specify schema
                entity.HasKey(e => e.Id);
                entity.Property(e => e.Temperature).HasPrecision(18, 2);
                entity.Property(e => e.OtherHotWaterUse).HasPrecision(18, 2);
                
                // Configure BathroomFaucets as owned entity
                entity.OwnsOne(e => e.BathroomFaucets, bf =>
                {
                    bf.Property(p => p.Code);
                    bf.Property(p => p.EnglishText);
                    bf.Property(p => p.FrenchText);
                    bf.Property(p => p.Value).HasPrecision(18, 2);
                    bf.Property(p => p.NumberPerOccupantPerDay).HasPrecision(18, 2);
                });
                
                // Configure Shower as owned entity
                entity.OwnsOne(e => e.Shower, s =>
                {
                    s.Property(p => p.AverageDuration).HasPrecision(18, 2);
                    s.Property(p => p.NumberPerOccupantPerWeek).HasPrecision(18, 2);
                    s.Property(p => p.TotalDurationPerDay).HasPrecision(18, 2);
                    
                    // Configure ShowerTemperature as owned entity
                    s.OwnsOne(ss => ss.Temperature, temp =>
                    {
                        temp.Property(p => p.Code);
                        temp.Property(p => p.EnglishText);
                        temp.Property(p => p.FrenchText);
                        temp.Property(p => p.Value).HasPrecision(18, 2);
                        temp.Property(p => p.IsUserSpecified);
                    });
                    
                    // Configure ShowerFlowRate as owned entity
                    s.OwnsOne(ss => ss.FlowRate, flow =>
                    {
                        flow.Property(p => p.Code);
                        flow.Property(p => p.EnglishText);
                        flow.Property(p => p.FrenchText);
                        flow.Property(p => p.Value).HasPrecision(18, 2);
                        flow.Property(p => p.IsUserSpecified);
                    });
                });
                
                // Configure ClothesWasher as owned entity
                entity.OwnsOne(e => e.ClothesWasher, cw =>
                {
                    cw.Property(p => p.NumberPerOccupantPerWeek).HasPrecision(18, 2);
                    
                    // Configure RatedValue as owned entity
                    cw.OwnsOne(c => c.RatedValues, rv =>
                    {
                        rv.Property(p => p.Code);
                        rv.Property(p => p.Text);
                        rv.Property(p => p.EnglishText);
                        rv.Property(p => p.FrenchText);
                        rv.Property(p => p.RatedAnnualEnergyConsumption).HasPrecision(18, 2);
                        rv.Property(p => p.RatedWaterConsumptionPerCycle).HasPrecision(18, 2);
                    });
                    
                    // Configure ClothesWasherTemperature as owned entity
                    cw.OwnsOne(c => c.Temperature, temp =>
                    {
                        temp.Property(p => p.Code);
                        temp.Property(p => p.EnglishText);
                        temp.Property(p => p.FrenchText);
                        temp.Property(p => p.IsUserSpecified);
                    });
                });
                
                // Configure DishWasher as owned entity
                entity.OwnsOne(e => e.DishWasher, dw =>
                {
                    dw.Property(p => p.NumberPerOccupantPerWeek).HasPrecision(18, 2);
                    
                    // Configure RatedValue as owned entity
                    dw.OwnsOne(d => d.RatedValues, rv =>
                    {
                        rv.Property(p => p.Code);
                        rv.Property(p => p.Text);
                        rv.Property(p => p.EnglishText);
                        rv.Property(p => p.FrenchText);
                        rv.Property(p => p.RatedAnnualEnergyConsumption).HasPrecision(18, 2);
                        rv.Property(p => p.RatedWaterConsumptionPerCycle).HasPrecision(18, 2);
                    });
                });
            });

            
            // ElectricalUsage configuration
            modelBuilder.Entity<ElectricalUsage>(entity =>
            {
                entity.ToTable("ElectricalUsages", "baseload"); // Specify schema
                entity.HasKey(e => e.Id);
                entity.Property(e => e.OtherLoad).HasPrecision(18, 2);
                entity.Property(e => e.AverageExteriorUse).HasPrecision(18, 2);
                
                // Configure ClothesDryer as owned entity
                entity.OwnsOne(e => e.ClothesDryer, cd =>
                {
                    cd.Property(p => p.Installed);
                    cd.Property(p => p.PercentageOfWasherLoads).HasPrecision(18, 2);
                    
                    // Configure ApplianceEnergySources as owned entity
                    cd.OwnsOne(c => c.EnergySource, es =>
                    {
                        es.Property(p => p.Code);
                        es.Property(p => p.EnglishText);
                        es.Property(p => p.FrenchText);
                        es.Property(p => p.IsUserSpecified);
                    });
                    
                    // Configure DryerRatedConsumptions as owned entity
                    cd.OwnsOne(c => c.RatedValue, rv =>
                    {
                        rv.Property(p => p.Code);
                        rv.Property(p => p.EnglishText);
                        rv.Property(p => p.FrenchText);
                        rv.Property(p => p.Value).HasPrecision(18, 2);
                        rv.Property(p => p.IsUserSpecified);
                    });
                    
                    // Configure CodeAndText as owned entity for Location
                    cd.OwnsOne(c => c.Location, loc =>
                    {
                        loc.Property(p => p.Code);
                        loc.Property(p => p.EnglishText);
                        loc.Property(p => p.FrenchText);
                    });
                });
                
                // Configure Stove as owned entity
                entity.OwnsOne(e => e.Stove, s =>
                {
                    // Configure ApplianceEnergySources as owned entity
                    s.OwnsOne(st => st.EnergySource, es =>
                    {
                        es.Property(p => p.Code);
                        es.Property(p => p.EnglishText);
                        es.Property(p => p.FrenchText);
                        es.Property(p => p.IsUserSpecified);
                    });
                    
                    // Configure StoveRatedConsumptions as owned entity
                    s.OwnsOne(st => st.RatedValue, rv =>
                    {
                        rv.Property(p => p.Code);
                        rv.Property(p => p.EnglishText);
                        rv.Property(p => p.FrenchText);
                        rv.Property(p => p.Value).HasPrecision(18, 2);
                        rv.Property(p => p.IsUserSpecified);
                    });
                });
                
                // Configure RefrigeratorRatedConsumptions as owned entity
                entity.OwnsOne(e => e.Refrigerator, r =>
                {
                    r.Property(p => p.Code);
                    r.Property(p => p.EnglishText);
                    r.Property(p => p.FrenchText);
                    r.Property(p => p.Value).HasPrecision(18, 2);
                    r.Property(p => p.IsUserSpecified);
                });
                
                // Configure InteriorLightingTypes as owned entity
                entity.OwnsOne(e => e.InteriorLighting, il =>
                {
                    il.Property(p => p.Code);
                    il.Property(p => p.EnglishText);
                    il.Property(p => p.FrenchText);
                    il.Property(p => p.Value).HasPrecision(18, 2);
                    il.Property(p => p.IsUserSpecified);
                });
            });
                // Rest of your ElectricalUsage configuration...
 
            
            // AdvancedUserSpecified configuration
            modelBuilder.Entity<AdvancedUserSpecified>(entity =>
            {
                entity.ToTable("AdvancedUserSpecifieds", "baseload"); // Specify schema
                entity.HasKey(e => e.Id);
                entity.Property(e => e.HotWaterTemperature).HasPrecision(18, 2);
        
                // Configure GasStove as owned entity
                entity.OwnsOne(e => e.GasStove, gasStove =>
                {
                    gasStove.Property(p => p.Code);
                    gasStove.Property(p => p.EnglishText);
                    gasStove.Property(p => p.FrenchText);
                    gasStove.Property(p => p.Value).HasPrecision(18, 2);
                    gasStove.Property(p => p.IsUserSpecified);
                });
        
                // Configure GasDryer as owned entity
                entity.OwnsOne(e => e.GasDryer, gasDryer =>
                {
                    gasDryer.Property(p => p.Code);
                    gasDryer.Property(p => p.EnglishText);
                    gasDryer.Property(p => p.FrenchText);
                    gasDryer.Property(p => p.Value).HasPrecision(18, 2);
                    gasDryer.Property(p => p.IsUserSpecified);
                });
        
                // Configure DryerLocation as owned entity
                entity.OwnsOne(e => e.DryerLocation, dryerLocation =>
                {
                    dryerLocation.Property(p => p.Code);
                    dryerLocation.Property(p => p.EnglishText);
                    dryerLocation.Property(p => p.FrenchText);
                    dryerLocation.Property(p => p.Text);
                });
            });
        }
    }
}