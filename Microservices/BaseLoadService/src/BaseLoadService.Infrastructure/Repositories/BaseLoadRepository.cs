using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using BaseLoadService.Core.Interfaces;
using BaseLoadService.Core.Models;
using BaseLoadService.Infrastructure.Data;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace BaseLoadService.Infrastructure.Repositories
{
    public class BaseLoadRepository : IBaseLoadRepository
    {
        private readonly BaseLoadDbContext _context;
        private readonly ILogger<BaseLoadRepository> _logger;
        
        public BaseLoadRepository(
            BaseLoadDbContext context,
            ILogger<BaseLoadRepository> logger)
        {
            _context = context;
            _logger = logger;
        }
        
        public async Task<IEnumerable<BaseLoad>> GetAllBaseLoadsAsync()
        {
            _logger.LogInformation("Getting all base loads from repository");
            
            return await _context.BaseLoads
                .Include(b => b.Occupancy)
                    .ThenInclude(o => o.Adults)
                .Include(b => b.Occupancy)
                    .ThenInclude(o => o.Children)
                .Include(b => b.Occupancy)
                    .ThenInclude(o => o.Infants)
                .Include(b => b.Summary)
                .Include(b => b.WaterUsage)
                    .ThenInclude(w => w.BathroomFaucets)
                .Include(b => b.WaterUsage)
                    .ThenInclude(w => w.Shower)
                        .ThenInclude(s => s.Temperature)
                .Include(b => b.WaterUsage)
                    .ThenInclude(w => w.Shower)
                        .ThenInclude(s => s.FlowRate)
                .Include(b => b.WaterUsage)
                    .ThenInclude(w => w.ClothesWasher)
                        .ThenInclude(c => c.RatedValues)
                .Include(b => b.WaterUsage)
                    .ThenInclude(w => w.ClothesWasher)
                        .ThenInclude(c => c.Temperature)
                .Include(b => b.WaterUsage)
                    .ThenInclude(w => w.DishWasher)
                        .ThenInclude(d => d.RatedValues)
                .Include(b => b.ElectricalUsage)
                    .ThenInclude(e => e.ClothesDryer)
                        .ThenInclude(c => c.EnergySource)
                .Include(b => b.ElectricalUsage)
                    .ThenInclude(e => e.ClothesDryer)
                        .ThenInclude(c => c.RatedValue)
                .Include(b => b.ElectricalUsage)
                    .ThenInclude(e => e.ClothesDryer)
                        .ThenInclude(c => c.Location)
                .Include(b => b.ElectricalUsage)
                    .ThenInclude(e => e.Stove)
                        .ThenInclude(s => s.EnergySource)
                .Include(b => b.ElectricalUsage)
                    .ThenInclude(e => e.Stove)
                        .ThenInclude(s => s.RatedValue)
                .Include(b => b.ElectricalUsage)
                    .ThenInclude(e => e.Refrigerator)
                .Include(b => b.ElectricalUsage)
                    .ThenInclude(e => e.InteriorLighting)
                .Include(b => b.AdvancedUserSpecified)
                .ToListAsync();
        }

        public async Task<BaseLoad?> GetBaseLoadsByHouseIdAsync(Guid houseId)
        {
            _logger.LogInformation("Getting base load with ID: {Id} from repository", houseId);
            
            return await _context.BaseLoads
                .Where(b => b.HouseId == houseId)
                .Include(b => b.Occupancy)
                    .ThenInclude(o => o.Adults)
                .Include(b => b.Occupancy)
                    .ThenInclude(o => o.Children)
                .Include(b => b.Occupancy)
                    .ThenInclude(o => o.Infants)
                .Include(b => b.Summary)
                .Include(b => b.WaterUsage)
                    .ThenInclude(w => w.BathroomFaucets)
                .Include(b => b.WaterUsage)
                    .ThenInclude(w => w.Shower)
                        .ThenInclude(s => s.Temperature)
                .Include(b => b.WaterUsage)
                    .ThenInclude(w => w.Shower)
                        .ThenInclude(s => s.FlowRate)
                .Include(b => b.WaterUsage)
                    .ThenInclude(w => w.ClothesWasher)
                        .ThenInclude(c => c.RatedValues)
                .Include(b => b.WaterUsage)
                    .ThenInclude(w => w.ClothesWasher)
                        .ThenInclude(c => c.Temperature)
                .Include(b => b.WaterUsage)
                    .ThenInclude(w => w.DishWasher)
                        .ThenInclude(d => d.RatedValues)
                .Include(b => b.ElectricalUsage)
                    .ThenInclude(e => e.ClothesDryer)
                        .ThenInclude(c => c.EnergySource)
                .Include(b => b.ElectricalUsage)
                    .ThenInclude(e => e.ClothesDryer)
                        .ThenInclude(c => c.RatedValue)
                .Include(b => b.ElectricalUsage)
                    .ThenInclude(e => e.ClothesDryer)
                        .ThenInclude(c => c.Location)
                .Include(b => b.ElectricalUsage)
                    .ThenInclude(e => e.Stove)
                        .ThenInclude(s => s.EnergySource)
                .Include(b => b.ElectricalUsage)
                    .ThenInclude(e => e.Stove)
                        .ThenInclude(s => s.RatedValue)
                .Include(b => b.ElectricalUsage)
                    .ThenInclude(e => e.Refrigerator)
                .Include(b => b.ElectricalUsage)
                    .ThenInclude(e => e.InteriorLighting)
                .Include(b => b.AdvancedUserSpecified)
                .FirstOrDefaultAsync(b => b.HouseId == houseId);
        }
        
        public async Task<BaseLoad?> GetBaseLoadByIdAsync(Guid id)
        {
            _logger.LogInformation("Getting base load with ID: {Id} from repository", id);
            
            return await _context.BaseLoads
                .AsNoTracking()
                .Include(b => b.Occupancy)
                    .ThenInclude(o => o.Adults)
                .Include(b => b.Occupancy)
                    .ThenInclude(o => o.Children)
                .Include(b => b.Occupancy)
                    .ThenInclude(o => o.Infants)
                .Include(b => b.Summary)
                .Include(b => b.WaterUsage)
                    .ThenInclude(w => w.BathroomFaucets)
                .Include(b => b.WaterUsage)
                    .ThenInclude(w => w.Shower)
                        .ThenInclude(s => s.Temperature)
                .Include(b => b.WaterUsage)
                    .ThenInclude(w => w.Shower)
                        .ThenInclude(s => s.FlowRate)
                .Include(b => b.WaterUsage)
                    .ThenInclude(w => w.ClothesWasher)
                        .ThenInclude(c => c.RatedValues)
                .Include(b => b.WaterUsage)
                    .ThenInclude(w => w.ClothesWasher)
                        .ThenInclude(c => c.Temperature)
                .Include(b => b.WaterUsage)
                    .ThenInclude(w => w.DishWasher)
                        .ThenInclude(d => d.RatedValues)
                .Include(b => b.ElectricalUsage)
                    .ThenInclude(e => e.ClothesDryer)
                        .ThenInclude(c => c.EnergySource)
                .Include(b => b.ElectricalUsage)
                    .ThenInclude(e => e.ClothesDryer)
                        .ThenInclude(c => c.RatedValue)
                .Include(b => b.ElectricalUsage)
                    .ThenInclude(e => e.ClothesDryer)
                        .ThenInclude(c => c.Location)
                .Include(b => b.ElectricalUsage)
                    .ThenInclude(e => e.Stove)
                        .ThenInclude(s => s.EnergySource)
                .Include(b => b.ElectricalUsage)
                    .ThenInclude(e => e.Stove)
                        .ThenInclude(s => s.RatedValue)
                .Include(b => b.ElectricalUsage)
                    .ThenInclude(e => e.Refrigerator)
                .Include(b => b.ElectricalUsage)
                    .ThenInclude(e => e.InteriorLighting)
                .Include(b => b.AdvancedUserSpecified)
                .FirstOrDefaultAsync(b => b.Id == id);
        }
        
       public async Task<BaseLoad> CreateBaseLoadAsync(BaseLoad baseLoad)
        {
            _logger.LogInformation("Creating new base load for house ID: {HouseId} from repository", baseLoad.HouseId);
    
    // No need to modify the entity here - just persist it as provided by the service
            _context.BaseLoads.Add(baseLoad);
            await _context.SaveChangesAsync();
    
            return baseLoad;
        }        
        public async Task UpdateBaseLoadAsync(BaseLoad baseLoad)
        {
            _logger.LogInformation("Updating base load with ID: {Id} from repository", baseLoad.Id);

            try
            {
        // Get the existing base load with all its related entities
                var existingBaseLoad = await _context.BaseLoads
                    .Include(b => b.Occupancy)
                        .ThenInclude(o => o.Adults)
                    .Include(b => b.Occupancy)
                        .ThenInclude(o => o.Children)
                    .Include(b => b.Occupancy)
                        .ThenInclude(o => o.Infants)
                    .Include(b => b.Summary)
                    .Include(b => b.WaterUsage)
                        .ThenInclude(w => w.BathroomFaucets)
                    .Include(b => b.WaterUsage)
                        .ThenInclude(w => w.Shower)
                            .ThenInclude(s => s.Temperature)
                    .Include(b => b.WaterUsage)
                        .ThenInclude(w => w.Shower)
                            .ThenInclude(s => s.FlowRate)
                    .Include(b => b.WaterUsage)
                        .ThenInclude(w => w.ClothesWasher)
                            .ThenInclude(c => c.RatedValues)
                    .Include(b => b.WaterUsage)
                        .ThenInclude(w => w.ClothesWasher)
                            .ThenInclude(c => c.Temperature)
                    .Include(b => b.WaterUsage)
                        .ThenInclude(w => w.DishWasher)
                            .ThenInclude(d => d.RatedValues)
                    .Include(b => b.ElectricalUsage)
                        .ThenInclude(e => e.ClothesDryer)
                            .ThenInclude(c => c.EnergySource)
                    .Include(b => b.ElectricalUsage)
                        .ThenInclude(e => e.ClothesDryer)
                            .ThenInclude(c => c.RatedValue)
                    .Include(b => b.ElectricalUsage)
                        .ThenInclude(e => e.ClothesDryer)
                            .ThenInclude(c => c.Location)
                    .Include(b => b.ElectricalUsage)
                        .ThenInclude(e => e.Stove)
                            .ThenInclude(s => s.EnergySource)
                    .Include(b => b.ElectricalUsage)
                        .ThenInclude(e => e.Stove)
                            .ThenInclude(s => s.RatedValue)
                    .Include(b => b.ElectricalUsage)
                        .ThenInclude(e => e.Refrigerator)
                    .Include(b => b.ElectricalUsage)
                        .ThenInclude(e => e.InteriorLighting)
                    .Include(b => b.AdvancedUserSpecified)
                    .FirstOrDefaultAsync(b => b.Id == baseLoad.Id);
        
                if (existingBaseLoad == null)
                {
                    throw new KeyNotFoundException($"BaseLoad with ID {baseLoad.Id} not found.");
                }

        // Update main entity properties
                existingBaseLoad.BasementFractionOfInternalGains = baseLoad.BasementFractionOfInternalGains;
                existingBaseLoad.CommonSpaceElectricalConsumption = baseLoad.CommonSpaceElectricalConsumption;
                existingBaseLoad.HouseId = baseLoad.HouseId;

        // Update Occupancy and its child entities
                UpdateOccupancy(existingBaseLoad, baseLoad);
        
        // Update Summary
                UpdateSummary(existingBaseLoad, baseLoad);
        
        // Update WaterUsage and its child entities
                UpdateWaterUsage(existingBaseLoad, baseLoad);
        
        // Update ElectricalUsage and its child entities
                UpdateElectricalUsage(existingBaseLoad, baseLoad);
        
        // Update AdvancedUserSpecified and its child entities
                UpdateAdvancedUserSpecified(existingBaseLoad, baseLoad);
        
                await _context.SaveChangesAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating BaseLoad with ID {Id}", baseLoad.Id);
                throw;
            }
        }

        private void UpdateOccupancy(BaseLoad existingBaseLoad, BaseLoad baseLoad)
        {
            if (baseLoad.Occupancy == null)
                return;
    
            if (existingBaseLoad.Occupancy == null)
            {
                existingBaseLoad.Occupancy = new Occupancy
                {
                    Id = Guid.NewGuid(),
                    BaseLoadId = existingBaseLoad.Id,
                    IsHouseOccupied = baseLoad.Occupancy.IsHouseOccupied
                };
            }
            else
            {
                existingBaseLoad.Occupancy.IsHouseOccupied = baseLoad.Occupancy.IsHouseOccupied;
            }
    
    // Handle Adults
            if (baseLoad.Occupancy.Adults != null)
            {
                if (existingBaseLoad.Occupancy.Adults == null)
                {
                    existingBaseLoad.Occupancy.Adults = new OccupantsAtHome
                    {
                        Id = Guid.NewGuid(),
                        OccupancyId = existingBaseLoad.Occupancy.Id,
                        OccupantType = OccupantType.Adults,
                        Occupants = baseLoad.Occupancy.Adults.Occupants,
                        AtHome = baseLoad.Occupancy.Adults.AtHome
                    };
                }
                else
                {
                    existingBaseLoad.Occupancy.Adults.Occupants = baseLoad.Occupancy.Adults.Occupants;
                    existingBaseLoad.Occupancy.Adults.AtHome = baseLoad.Occupancy.Adults.AtHome;
                }
            }
    
    // Handle Children
            if (baseLoad.Occupancy.Children != null)
            {
                if (existingBaseLoad.Occupancy.Children == null)
                {
                    existingBaseLoad.Occupancy.Children = new OccupantsAtHome
                    {
                        Id = Guid.NewGuid(),
                        OccupancyId = existingBaseLoad.Occupancy.Id,
                        OccupantType = OccupantType.Children,
                        Occupants = baseLoad.Occupancy.Children.Occupants,
                        AtHome = baseLoad.Occupancy.Children.AtHome
                    };
                }
                else
                {
                    existingBaseLoad.Occupancy.Children.Occupants = baseLoad.Occupancy.Children.Occupants;
                    existingBaseLoad.Occupancy.Children.AtHome = baseLoad.Occupancy.Children.AtHome;
                }
            }
    
    // Handle Infants
            if (baseLoad.Occupancy.Infants != null)
            {
                if (existingBaseLoad.Occupancy.Infants == null)
                {
                    existingBaseLoad.Occupancy.Infants = new OccupantsAtHome
                    {
                        Id = Guid.NewGuid(),
                        OccupancyId = existingBaseLoad.Occupancy.Id,
                        OccupantType = OccupantType.Infants,
                        Occupants = baseLoad.Occupancy.Infants.Occupants,
                        AtHome = baseLoad.Occupancy.Infants.AtHome
                    };
                }
                else
                {
                    existingBaseLoad.Occupancy.Infants.Occupants = baseLoad.Occupancy.Infants.Occupants;
                    existingBaseLoad.Occupancy.Infants.AtHome = baseLoad.Occupancy.Infants.AtHome;
                }
            }
        }

        private void UpdateSummary(BaseLoad existingBaseLoad, BaseLoad baseLoad)
        {
            if (baseLoad.Summary == null)
                return;
    
            if (existingBaseLoad.Summary == null)
            {
                existingBaseLoad.Summary = new Summary
                {
                    Id = Guid.NewGuid(),
                    BaseLoadId = existingBaseLoad.Id
                };
            }
    
    // Update Summary properties
            existingBaseLoad.Summary.IsSpecified = baseLoad.Summary.IsSpecified;
            existingBaseLoad.Summary.ElectricalAppliances = baseLoad.Summary.ElectricalAppliances;
            existingBaseLoad.Summary.Lighting = baseLoad.Summary.Lighting;
            existingBaseLoad.Summary.OtherElectric = baseLoad.Summary.OtherElectric;
            existingBaseLoad.Summary.ExteriorUse = baseLoad.Summary.ExteriorUse;
            existingBaseLoad.Summary.HotWaterLoad = baseLoad.Summary.HotWaterLoad;
        }

        private void UpdateWaterUsage(BaseLoad existingBaseLoad, BaseLoad baseLoad)
        {
            if (baseLoad.WaterUsage == null)
                return;
    
            if (existingBaseLoad.WaterUsage == null)
            {
                existingBaseLoad.WaterUsage = new WaterUsage
                {
                    Id = Guid.NewGuid(),
                    BaseLoadId = existingBaseLoad.Id
                };
                existingBaseLoad.WaterUsage.SetDefaults();
            }
    
    // Update WaterUsage properties
            existingBaseLoad.WaterUsage.Temperature = baseLoad.WaterUsage.Temperature;
            existingBaseLoad.WaterUsage.OtherHotWaterUse = baseLoad.WaterUsage.OtherHotWaterUse;
            existingBaseLoad.WaterUsage.LowFlushToilets = baseLoad.WaterUsage.LowFlushToilets;
    
    // Handle BathroomFaucets
            UpdateBathroomFaucets(existingBaseLoad.WaterUsage, baseLoad.WaterUsage);
    
    // Handle Shower
            UpdateShower(existingBaseLoad.WaterUsage, baseLoad.WaterUsage);
    
    // Handle ClothesWasher
            UpdateClothesWasher(existingBaseLoad.WaterUsage, baseLoad.WaterUsage);
    
    // Handle DishWasher
            UpdateDishWasher(existingBaseLoad.WaterUsage, baseLoad.WaterUsage);
        }

        private void UpdateBathroomFaucets(WaterUsage existingWaterUsage, WaterUsage newWaterUsage)
        {
            if (newWaterUsage.BathroomFaucets == null)
                return;
    
            if (existingWaterUsage.BathroomFaucets == null)
            {
                existingWaterUsage.BathroomFaucets = new BathroomFaucets();
            }
    
            existingWaterUsage.BathroomFaucets.Code = newWaterUsage.BathroomFaucets.Code;
            existingWaterUsage.BathroomFaucets.EnglishText = newWaterUsage.BathroomFaucets.EnglishText;
            existingWaterUsage.BathroomFaucets.FrenchText = newWaterUsage.BathroomFaucets.FrenchText;
            existingWaterUsage.BathroomFaucets.Value = newWaterUsage.BathroomFaucets.Value;
            existingWaterUsage.BathroomFaucets.NumberPerOccupantPerDay = newWaterUsage.BathroomFaucets.NumberPerOccupantPerDay;
        }

        private void UpdateShower(WaterUsage existingWaterUsage, WaterUsage newWaterUsage)
        {
            if (newWaterUsage.Shower == null)
                return;
    
            if (existingWaterUsage.Shower == null)
            {
                existingWaterUsage.Shower = new Shower();
            }
    
            existingWaterUsage.Shower.AverageDuration = newWaterUsage.Shower.AverageDuration;
            existingWaterUsage.Shower.NumberPerOccupantPerWeek = newWaterUsage.Shower.NumberPerOccupantPerWeek;
            existingWaterUsage.Shower.TotalDurationPerDay = newWaterUsage.Shower.TotalDurationPerDay;
    
    // Handle ShowerTemperature
            if (newWaterUsage.Shower.Temperature != null)
            {
                if (existingWaterUsage.Shower.Temperature == null)
                {
                    existingWaterUsage.Shower.Temperature = new ShowerTemperature();
                }
        
                existingWaterUsage.Shower.Temperature.Code = newWaterUsage.Shower.Temperature.Code;
                existingWaterUsage.Shower.Temperature.EnglishText = newWaterUsage.Shower.Temperature.EnglishText;
                existingWaterUsage.Shower.Temperature.FrenchText = newWaterUsage.Shower.Temperature.FrenchText;
                existingWaterUsage.Shower.Temperature.Value = newWaterUsage.Shower.Temperature.Value;
                existingWaterUsage.Shower.Temperature.IsUserSpecified = newWaterUsage.Shower.Temperature.IsUserSpecified;
            }
            else if (existingWaterUsage.Shower.Temperature == null)
            {
                existingWaterUsage.Shower.Temperature = new ShowerTemperature();
            }
    
    // Handle ShowerFlowRate
            if (newWaterUsage.Shower.FlowRate != null)
            {
                if (existingWaterUsage.Shower.FlowRate == null)
                {
                    existingWaterUsage.Shower.FlowRate = new ShowerFlowRate();
                }
        
                existingWaterUsage.Shower.FlowRate.Code = newWaterUsage.Shower.FlowRate.Code;
                existingWaterUsage.Shower.FlowRate.EnglishText = newWaterUsage.Shower.FlowRate.EnglishText;
                existingWaterUsage.Shower.FlowRate.FrenchText = newWaterUsage.Shower.FlowRate.FrenchText;
                existingWaterUsage.Shower.FlowRate.Value = newWaterUsage.Shower.FlowRate.Value;
                existingWaterUsage.Shower.FlowRate.IsUserSpecified = newWaterUsage.Shower.FlowRate.IsUserSpecified;
            }
            else if (existingWaterUsage.Shower.FlowRate == null)
            {
                existingWaterUsage.Shower.FlowRate = new ShowerFlowRate();
            }
        }

        private void UpdateClothesWasher(WaterUsage existingWaterUsage, WaterUsage newWaterUsage)
            {
            if (newWaterUsage.ClothesWasher == null)
                return;
    
            if (existingWaterUsage.ClothesWasher == null)
            {
                existingWaterUsage.ClothesWasher = new ClothesWasher();
            }
    
            existingWaterUsage.ClothesWasher.NumberPerOccupantPerWeek = newWaterUsage.ClothesWasher.NumberPerOccupantPerWeek;
    
    // Handle RatedValues
            if (newWaterUsage.ClothesWasher.RatedValues != null)
            {
                if (existingWaterUsage.ClothesWasher.RatedValues == null)
                {
                    existingWaterUsage.ClothesWasher.RatedValues = new RatedValue();
                }
        
                existingWaterUsage.ClothesWasher.RatedValues.Code = newWaterUsage.ClothesWasher.RatedValues.Code;
                existingWaterUsage.ClothesWasher.RatedValues.Text = newWaterUsage.ClothesWasher.RatedValues.Text;
                existingWaterUsage.ClothesWasher.RatedValues.EnglishText = newWaterUsage.ClothesWasher.RatedValues.EnglishText;
                existingWaterUsage.ClothesWasher.RatedValues.FrenchText = newWaterUsage.ClothesWasher.RatedValues.FrenchText;
                existingWaterUsage.ClothesWasher.RatedValues.RatedAnnualEnergyConsumption = newWaterUsage.ClothesWasher.RatedValues.RatedAnnualEnergyConsumption;
                existingWaterUsage.ClothesWasher.RatedValues.RatedWaterConsumptionPerCycle = newWaterUsage.ClothesWasher.RatedValues.RatedWaterConsumptionPerCycle;
            }
            else if (existingWaterUsage.ClothesWasher.RatedValues == null)
            {
                existingWaterUsage.ClothesWasher.RatedValues = new RatedValue();
            }
    
    // Handle Temperature
            if (newWaterUsage.ClothesWasher.Temperature != null)
            {
                if (existingWaterUsage.ClothesWasher.Temperature == null)
                {
                    existingWaterUsage.ClothesWasher.Temperature = new ClothesWasherTemperature();
                }
        
                existingWaterUsage.ClothesWasher.Temperature.Code = newWaterUsage.ClothesWasher.Temperature.Code;
                existingWaterUsage.ClothesWasher.Temperature.EnglishText = newWaterUsage.ClothesWasher.Temperature.EnglishText;
                existingWaterUsage.ClothesWasher.Temperature.FrenchText = newWaterUsage.ClothesWasher.Temperature.FrenchText;
                existingWaterUsage.ClothesWasher.Temperature.IsUserSpecified = newWaterUsage.ClothesWasher.Temperature.IsUserSpecified;
            }
            else if (existingWaterUsage.ClothesWasher.Temperature == null)
            {
                existingWaterUsage.ClothesWasher.Temperature = new ClothesWasherTemperature();
            }
        }

        private void UpdateDishWasher(WaterUsage existingWaterUsage, WaterUsage newWaterUsage)
        {
            if (newWaterUsage.DishWasher == null)
                return;
    
            if (existingWaterUsage.DishWasher == null)
            {
                existingWaterUsage.DishWasher = new DishWasher();
            }
    
            existingWaterUsage.DishWasher.NumberPerOccupantPerWeek = newWaterUsage.DishWasher.NumberPerOccupantPerWeek;
    
    // Handle RatedValues
            if (newWaterUsage.DishWasher.RatedValues != null)
            {
                if (existingWaterUsage.DishWasher.RatedValues == null)
                {
                    existingWaterUsage.DishWasher.RatedValues = new RatedValue();
                }
        
                existingWaterUsage.DishWasher.RatedValues.Code = newWaterUsage.DishWasher.RatedValues.Code;
                existingWaterUsage.DishWasher.RatedValues.Text = newWaterUsage.DishWasher.RatedValues.Text;
                existingWaterUsage.DishWasher.RatedValues.EnglishText = newWaterUsage.DishWasher.RatedValues.EnglishText;
                existingWaterUsage.DishWasher.RatedValues.FrenchText = newWaterUsage.DishWasher.RatedValues.FrenchText;
                existingWaterUsage.DishWasher.RatedValues.RatedAnnualEnergyConsumption = newWaterUsage.DishWasher.RatedValues.RatedAnnualEnergyConsumption;
                existingWaterUsage.DishWasher.RatedValues.RatedWaterConsumptionPerCycle = newWaterUsage.DishWasher.RatedValues.RatedWaterConsumptionPerCycle;
            }
            else if (existingWaterUsage.DishWasher.RatedValues == null)
            {
                existingWaterUsage.DishWasher.RatedValues = new RatedValue();
            }
        }

        private void UpdateElectricalUsage(BaseLoad existingBaseLoad, BaseLoad baseLoad)
        {
            if (baseLoad.ElectricalUsage == null)
                return;
    
            if (existingBaseLoad.ElectricalUsage == null)
            {
                existingBaseLoad.ElectricalUsage = new ElectricalUsage
                {
                    Id = Guid.NewGuid(),
                    BaseLoadId = existingBaseLoad.Id
                };
                existingBaseLoad.ElectricalUsage.SetDefaults();
            }
    
    // Update ElectricalUsage properties
            existingBaseLoad.ElectricalUsage.OtherLoad = baseLoad.ElectricalUsage.OtherLoad;
            existingBaseLoad.ElectricalUsage.AverageExteriorUse = baseLoad.ElectricalUsage.AverageExteriorUse;
    
    // Handle ClothesDryer
            UpdateClothesDryer(existingBaseLoad.ElectricalUsage, baseLoad.ElectricalUsage);
    
    // Handle Stove
            UpdateStove(existingBaseLoad.ElectricalUsage, baseLoad.ElectricalUsage);
    
    // Handle Refrigerator
            UpdateRefrigerator(existingBaseLoad.ElectricalUsage, baseLoad.ElectricalUsage);
    
    // Handle InteriorLighting
            UpdateInteriorLighting(existingBaseLoad.ElectricalUsage, baseLoad.ElectricalUsage);
        }

        private void UpdateClothesDryer(ElectricalUsage existingElectricalUsage, ElectricalUsage newElectricalUsage)
        {
            if (newElectricalUsage.ClothesDryer == null)
                return;
    
            if (existingElectricalUsage.ClothesDryer == null)
            {
                existingElectricalUsage.ClothesDryer = new ClothesDryer();
            }
    
    // Update EnergySource
            if (newElectricalUsage.ClothesDryer.EnergySource != null)
            {
                if (existingElectricalUsage.ClothesDryer.EnergySource == null)
                {
                    existingElectricalUsage.ClothesDryer.EnergySource = new ApplianceEnergySources();
                }
        
                existingElectricalUsage.ClothesDryer.EnergySource.Code = newElectricalUsage.ClothesDryer.EnergySource.Code;
                existingElectricalUsage.ClothesDryer.EnergySource.EnglishText = newElectricalUsage.ClothesDryer.EnergySource.EnglishText;
                existingElectricalUsage.ClothesDryer.EnergySource.FrenchText = newElectricalUsage.ClothesDryer.EnergySource.FrenchText;
            }
    
    // Update RatedValue
            if (newElectricalUsage.ClothesDryer.RatedValue != null)
            {
                if (existingElectricalUsage.ClothesDryer.RatedValue == null)
                {
                    existingElectricalUsage.ClothesDryer.RatedValue = new DryerRatedConsumptions();
                }
        
                existingElectricalUsage.ClothesDryer.RatedValue.Code = newElectricalUsage.ClothesDryer.RatedValue.Code;
                existingElectricalUsage.ClothesDryer.RatedValue.EnglishText = newElectricalUsage.ClothesDryer.RatedValue.EnglishText;
                existingElectricalUsage.ClothesDryer.RatedValue.FrenchText = newElectricalUsage.ClothesDryer.RatedValue.FrenchText;
            }
    
    // Update Location
            if (newElectricalUsage.ClothesDryer.Location != null)
            {
                if (existingElectricalUsage.ClothesDryer.Location == null)
                {
                    existingElectricalUsage.ClothesDryer.Location = new CodeAndText();
                }
        
                existingElectricalUsage.ClothesDryer.Location.Code = newElectricalUsage.ClothesDryer.Location.Code;
                existingElectricalUsage.ClothesDryer.Location.EnglishText = newElectricalUsage.ClothesDryer.Location.EnglishText;
                existingElectricalUsage.ClothesDryer.Location.FrenchText = newElectricalUsage.ClothesDryer.Location.FrenchText;
            }
        }

        private void UpdateStove(ElectricalUsage existingElectricalUsage, ElectricalUsage newElectricalUsage)
        {
            if (newElectricalUsage.Stove == null)
                return;
    
            if (existingElectricalUsage.Stove == null)
            {
                existingElectricalUsage.Stove = new Stove();
            }
    
    // Update EnergySource
            if (newElectricalUsage.Stove.EnergySource != null)
            {
                if (existingElectricalUsage.Stove.EnergySource == null)
                {
                    existingElectricalUsage.Stove.EnergySource = new ApplianceEnergySources();
                }
        
                existingElectricalUsage.Stove.EnergySource.Code = newElectricalUsage.Stove.EnergySource.Code;
                existingElectricalUsage.Stove.EnergySource.EnglishText = newElectricalUsage.Stove.EnergySource.EnglishText;
                existingElectricalUsage.Stove.EnergySource.FrenchText = newElectricalUsage.Stove.EnergySource.FrenchText;
            }
    
    // Update RatedValue
            if (newElectricalUsage.Stove.RatedValue != null)
            {
                if (existingElectricalUsage.Stove.RatedValue == null)
                {
                    existingElectricalUsage.Stove.RatedValue = new StoveRatedConsumptions();
                }
        
                existingElectricalUsage.Stove.RatedValue.Code = newElectricalUsage.Stove.RatedValue.Code;
                existingElectricalUsage.Stove.RatedValue.EnglishText = newElectricalUsage.Stove.RatedValue.EnglishText;
                existingElectricalUsage.Stove.RatedValue.FrenchText = newElectricalUsage.Stove.RatedValue.FrenchText;
            }
        }

        private void UpdateRefrigerator(ElectricalUsage existingElectricalUsage, ElectricalUsage newElectricalUsage)
        {
            if (newElectricalUsage.Refrigerator == null)
                return;
    
    // Create a new instance of RefrigeratorRatedConsumptions
            existingElectricalUsage.Refrigerator = new RefrigeratorRatedConsumptions 
            {
                Code = newElectricalUsage.Refrigerator.Code,
                EnglishText = newElectricalUsage.Refrigerator.EnglishText,
                FrenchText = newElectricalUsage.Refrigerator.FrenchText
            };
        }

        private void UpdateInteriorLighting(ElectricalUsage existingElectricalUsage, ElectricalUsage newElectricalUsage)
        {
            if (newElectricalUsage.InteriorLighting == null)
                return;
    
    // Create a new instance of InteriorLightingTypes
            existingElectricalUsage.InteriorLighting = new InteriorLightingTypes
            {
                Code = newElectricalUsage.InteriorLighting.Code,
                EnglishText = newElectricalUsage.InteriorLighting.EnglishText,
                FrenchText = newElectricalUsage.InteriorLighting.FrenchText
            };
        }

        private void UpdateAdvancedUserSpecified(BaseLoad existingBaseLoad, BaseLoad baseLoad)
        {
            if (baseLoad.AdvancedUserSpecified == null)
                return;
    
            if (existingBaseLoad.AdvancedUserSpecified == null)
            {
                existingBaseLoad.AdvancedUserSpecified = new AdvancedUserSpecified
                {
                    Id = Guid.NewGuid(),
                    BaseLoadId = existingBaseLoad.Id
                };
                existingBaseLoad.AdvancedUserSpecified.SetDefaults();
            }
    
    // Update HotWaterTemperature property
            existingBaseLoad.AdvancedUserSpecified.HotWaterTemperature = baseLoad.AdvancedUserSpecified.HotWaterTemperature;
    
    // Handle GasStove
            UpdateGasStove(existingBaseLoad.AdvancedUserSpecified, baseLoad.AdvancedUserSpecified);
    
    // Handle GasDryer
            UpdateGasDryer(existingBaseLoad.AdvancedUserSpecified, baseLoad.AdvancedUserSpecified);
    
    // Handle DryerLocation
            UpdateDryerLocation(existingBaseLoad.AdvancedUserSpecified, baseLoad.AdvancedUserSpecified);
        }

        private void UpdateGasStove(AdvancedUserSpecified existingAdvancedUserSpecified, AdvancedUserSpecified newAdvancedUserSpecified)
        {
            if (newAdvancedUserSpecified.GasStove == null)
            {
                existingAdvancedUserSpecified.GasStove = null;
                return;
            }
    
    // If code is provided, use factory methods to get the correct instance
            if (!string.IsNullOrEmpty(newAdvancedUserSpecified.GasStove.Code))
            {
                if (newAdvancedUserSpecified.GasStove.Code == "2")
                {
                    var gasStove = ApplianceEnergySourceSpecified.NaturalGas();
                    gasStove.Value = newAdvancedUserSpecified.GasStove.Value;
                    existingAdvancedUserSpecified.GasStove = gasStove;
                }
                else if (newAdvancedUserSpecified.GasStove.Code == "4")
                {
                    var gasStove = ApplianceEnergySourceSpecified.Propane();
                    gasStove.Value = newAdvancedUserSpecified.GasStove.Value;
                    existingAdvancedUserSpecified.GasStove = gasStove;
                }
            }
            else
            {
                existingAdvancedUserSpecified.GasStove = null;
            }
        }

        private void UpdateGasDryer(AdvancedUserSpecified existingAdvancedUserSpecified, AdvancedUserSpecified newAdvancedUserSpecified)
        {
            if (newAdvancedUserSpecified.GasDryer == null)
            {
                existingAdvancedUserSpecified.GasDryer = null;
                return;
            }
    
    // If code is provided, use factory methods to get the correct instance
            if (!string.IsNullOrEmpty(newAdvancedUserSpecified.GasDryer.Code))
                {
                if (newAdvancedUserSpecified.GasDryer.Code == "2")
                {
                    var gasDryer = ApplianceEnergySourceSpecified.NaturalGas();
                    gasDryer.Value = newAdvancedUserSpecified.GasDryer.Value;
                    existingAdvancedUserSpecified.GasDryer = gasDryer;
                }
                else if (newAdvancedUserSpecified.GasDryer.Code == "4")
                {
                    var gasDryer = ApplianceEnergySourceSpecified.Propane();
                    gasDryer.Value = newAdvancedUserSpecified.GasDryer.Value;
                    existingAdvancedUserSpecified.GasDryer = gasDryer;
                }
            }
            else
            {
                existingAdvancedUserSpecified.GasDryer = null;
            }
        }

        private void UpdateDryerLocation(AdvancedUserSpecified existingAdvancedUserSpecified, AdvancedUserSpecified newAdvancedUserSpecified)
        {
            if (newAdvancedUserSpecified.DryerLocation == null)
            {
                existingAdvancedUserSpecified.DryerLocation = new CodeAndText("1", "Main Floor", "Plancher Principal");
                return;
            }
    
            existingAdvancedUserSpecified.DryerLocation = new CodeAndText(
                newAdvancedUserSpecified.DryerLocation.Code,
                newAdvancedUserSpecified.DryerLocation.EnglishText,
                newAdvancedUserSpecified.DryerLocation.FrenchText
            );
    
            if (newAdvancedUserSpecified.DryerLocation.Text != null)
            {
                existingAdvancedUserSpecified.DryerLocation.Text = newAdvancedUserSpecified.DryerLocation.Text;
            }
        }

        
        public async Task DeleteBaseLoadAsync(Guid id)
        {
            _logger.LogInformation("Deleting base load with ID: {Id} from repository", id);
    
            var baseLoad = await _context.BaseLoads
                .Include(b => b.Occupancy)
                    .ThenInclude(o => o.Adults)
                .Include(b => b.Occupancy)
                    .ThenInclude(o => o.Children)
                .Include(b => b.Occupancy)
                    .ThenInclude(o => o.Infants)
                .Include(b => b.Summary)
                .Include(b => b.WaterUsage)
                    .ThenInclude(w => w.BathroomFaucets)
                .Include(b => b.WaterUsage)
                    .ThenInclude(w => w.Shower)
                        .ThenInclude(s => s.Temperature)
                .Include(b => b.WaterUsage)
                    .ThenInclude(w => w.Shower)
                        .ThenInclude(s => s.FlowRate)
                .Include(b => b.WaterUsage)
                    .ThenInclude(w => w.ClothesWasher)
                        .ThenInclude(c => c.RatedValues)
                .Include(b => b.WaterUsage)
                    .ThenInclude(w => w.ClothesWasher)
                        .ThenInclude(c => c.Temperature)
                .Include(b => b.WaterUsage)
                    .ThenInclude(w => w.DishWasher)
                        .ThenInclude(d => d.RatedValues)
                .Include(b => b.ElectricalUsage)
                    .ThenInclude(e => e.ClothesDryer)
                        .ThenInclude(c => c.EnergySource)
                .Include(b => b.ElectricalUsage)
                    .ThenInclude(e => e.ClothesDryer)
                        .ThenInclude(c => c.RatedValue)
                .Include(b => b.ElectricalUsage)
                    .ThenInclude(e => e.ClothesDryer)
                        .ThenInclude(c => c.Location)
                .Include(b => b.ElectricalUsage)
                    .ThenInclude(e => e.Stove)
                        .ThenInclude(s => s.EnergySource)
                .Include(b => b.ElectricalUsage)
                    .ThenInclude(e => e.Stove)
                        .ThenInclude(s => s.RatedValue)
                .Include(b => b.ElectricalUsage)
                    .ThenInclude(e => e.Refrigerator)
                .Include(b => b.ElectricalUsage)
                    .ThenInclude(e => e.InteriorLighting)
                .Include(b => b.AdvancedUserSpecified)
                .FirstOrDefaultAsync(b => b.Id == id);
        
            if (baseLoad != null)
            {
                _context.BaseLoads.Remove(baseLoad);
                await _context.SaveChangesAsync();
            }
        }

        public async Task<BaseLoad?> GetBaseLoadByEnergyUpgradeIdAsync(Guid energyUpgradeId)
        {
            _logger.LogInformation("Getting base load for energy upgrade ID: {EnergyUpgradeId} from repository", energyUpgradeId);

            return await _context.BaseLoads
                .Where(b => b.EnergyUpgradeId == energyUpgradeId)
                .Include(b => b.Occupancy)
                    .ThenInclude(o => o.Adults)
                .Include(b => b.Occupancy)
                    .ThenInclude(o => o.Children)
                .Include(b => b.Occupancy)
                    .ThenInclude(o => o.Infants)
                .Include(b => b.Summary)
                .Include(b => b.WaterUsage)
                    .ThenInclude(w => w.BathroomFaucets)
                .Include(b => b.WaterUsage)
                    .ThenInclude(w => w.Shower)
                        .ThenInclude(s => s.Temperature)
                .Include(b => b.WaterUsage)
                    .ThenInclude(w => w.Shower)
                        .ThenInclude(s => s.FlowRate)
                .Include(b => b.WaterUsage)
                    .ThenInclude(w => w.ClothesWasher)
                        .ThenInclude(c => c.RatedValues)
                .Include(b => b.WaterUsage)
                    .ThenInclude(w => w.ClothesWasher)
                        .ThenInclude(c => c.Temperature)
                .Include(b => b.WaterUsage)
                    .ThenInclude(w => w.DishWasher)
                        .ThenInclude(d => d.RatedValues)
                .Include(b => b.ElectricalUsage)
                    .ThenInclude(e => e.ClothesDryer)
                        .ThenInclude(c => c.EnergySource)
                .Include(b => b.ElectricalUsage)
                    .ThenInclude(e => e.ClothesDryer)
                        .ThenInclude(c => c.RatedValue)
                .Include(b => b.ElectricalUsage)
                    .ThenInclude(e => e.ClothesDryer)
                        .ThenInclude(c => c.Location)
                .Include(b => b.ElectricalUsage)
                    .ThenInclude(e => e.Stove)
                        .ThenInclude(s => s.EnergySource)
                .Include(b => b.ElectricalUsage)
                    .ThenInclude(e => e.Stove)
                        .ThenInclude(s => s.RatedValue)
                .Include(b => b.ElectricalUsage)
                    .ThenInclude(e => e.Refrigerator)
                .Include(b => b.ElectricalUsage)
                    .ThenInclude(e => e.InteriorLighting)
                .Include(b => b.AdvancedUserSpecified)
                .FirstOrDefaultAsync(b => b.EnergyUpgradeId == energyUpgradeId);
        }

        public async Task<BaseLoad> DuplicateBaseLoadForEnergyUpgradeAsync(BaseLoad baseBaseLoad, Guid energyUpgradeId)
        {
            _logger.LogInformation("Duplicating base load {BaseLoadId} for energy upgrade {EnergyUpgradeId} from repository",
                baseBaseLoad.Id, energyUpgradeId);

            // Create a deep copy of the base load
            var duplicatedBaseLoad = new BaseLoad
            {
                Id = Guid.NewGuid(),
                HouseId = baseBaseLoad.HouseId,
                EnergyUpgradeId = energyUpgradeId,
                BasementFractionOfInternalGains = baseBaseLoad.BasementFractionOfInternalGains,
                CommonSpaceElectricalConsumption = baseBaseLoad.CommonSpaceElectricalConsumption
            };

            // Deep copy Occupancy using copy constructor pattern
            if (baseBaseLoad.Occupancy != null)
            {
                var occupancyId = Guid.NewGuid();
                duplicatedBaseLoad.Occupancy = new Occupancy
                {
                    Id = occupancyId,
                    BaseLoadId = duplicatedBaseLoad.Id,
                    IsHouseOccupied = baseBaseLoad.Occupancy.IsHouseOccupied,
                    Adults = baseBaseLoad.Occupancy.Adults != null ? new OccupantsAtHome(baseBaseLoad.Occupancy.Adults.Occupants, baseBaseLoad.Occupancy.Adults.AtHome)
                    {
                        Id = Guid.NewGuid(),
                        OccupancyId = occupancyId,
                        OccupantType = baseBaseLoad.Occupancy.Adults.OccupantType
                    } : null,
                    Children = baseBaseLoad.Occupancy.Children != null ? new OccupantsAtHome(baseBaseLoad.Occupancy.Children.Occupants, baseBaseLoad.Occupancy.Children.AtHome)
                    {
                        Id = Guid.NewGuid(),
                        OccupancyId = occupancyId,
                        OccupantType = baseBaseLoad.Occupancy.Children.OccupantType
                    } : null,
                    Infants = baseBaseLoad.Occupancy.Infants != null ? new OccupantsAtHome(baseBaseLoad.Occupancy.Infants.Occupants, baseBaseLoad.Occupancy.Infants.AtHome)
                    {
                        Id = Guid.NewGuid(),
                        OccupancyId = occupancyId,
                        OccupantType = baseBaseLoad.Occupancy.Infants.OccupantType
                    } : null
                };
            }

            // Deep copy Summary using copy constructor
            if (baseBaseLoad.Summary != null)
            {
                duplicatedBaseLoad.Summary = new Summary(baseBaseLoad.Summary)
                {
                    Id = Guid.NewGuid(),
                    BaseLoadId = duplicatedBaseLoad.Id
                };
            }

            // Deep copy WaterUsage using copy constructor
            if (baseBaseLoad.WaterUsage != null)
            {
                duplicatedBaseLoad.WaterUsage = new WaterUsage(baseBaseLoad.WaterUsage)
                {
                    Id = Guid.NewGuid(),
                    BaseLoadId = duplicatedBaseLoad.Id
                };
            }

            // Deep copy ElectricalUsage - create empty instance for now since copy constructor may not be complete
            if (baseBaseLoad.ElectricalUsage != null)
            {
                duplicatedBaseLoad.ElectricalUsage = new ElectricalUsage
                {
                    Id = Guid.NewGuid(),
                    BaseLoadId = duplicatedBaseLoad.Id
                };
                duplicatedBaseLoad.ElectricalUsage.SetDefaults();
            }

            if (baseBaseLoad.AdvancedUserSpecified != null)
            {
                duplicatedBaseLoad.AdvancedUserSpecified = new AdvancedUserSpecified
                {
                    Id = Guid.NewGuid(),
                    BaseLoadId = duplicatedBaseLoad.Id
                };
                duplicatedBaseLoad.AdvancedUserSpecified.SetDefaults();
            }

            _context.BaseLoads.Add(duplicatedBaseLoad);
            await _context.SaveChangesAsync();
            return duplicatedBaseLoad;
        }
    }
}