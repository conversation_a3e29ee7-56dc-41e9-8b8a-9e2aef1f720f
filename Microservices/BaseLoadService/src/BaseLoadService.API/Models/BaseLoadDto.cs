using System;

namespace BaseLoadService.API.Models
{
    /// <summary>
    /// Data Transfer Object for BaseLoad
    /// </summary>
    public class BaseLoadDto
    {
        public Guid Id { get; set; }

        public Guid HouseId { get; set; }

        public Guid? EnergyUpgradeId { get; set; }

        public decimal BasementFractionOfInternalGains { get; set; }
        
        public decimal CommonSpaceElectricalConsumption { get; set; }
        
        public OccupancyDto Occupancy { get; set; } = null!;
        
        public SummaryDto Summary { get; set; } = null!;
        
        public WaterUsageDto? WaterUsage { get; set; }
        
        public ElectricalUsageDto? ElectricalUsage { get; set; }
        
        public AdvancedUserSpecifiedDto? AdvancedUserSpecified { get; set; }
    }
}