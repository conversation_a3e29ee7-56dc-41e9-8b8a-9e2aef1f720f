using System;
using System.Collections.Generic;
using System.Linq;
using System.Text.Json;
using System.Text.Json.Serialization;
using System.Threading.Tasks;
using AutoMapper;
using BaseLoadService.API.Models;
using BaseLoadService.Core.Interfaces;
using BaseLoadService.Core.Models;
using Hot2K.Shared.Validation.Services;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;

namespace BaseLoadService.API.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class BaseLoadController : ControllerBase
    {
        private readonly IBaseLoadService _baseLoadService;
        private readonly IMapper _mapper;
        private readonly ILogger<BaseLoadController> _logger;
        private readonly IDeclarativeValidationService _validationService;

        public BaseLoadController(
            IBaseLoadService baseLoadService,
            IMapper mapper,
            ILogger<BaseLoadController> logger,
            IDeclarativeValidationService validationService)
        {
            _baseLoadService = baseLoadService;
            _mapper = mapper;
            _logger = logger;
            _validationService = validationService;
        }
        
        /// <summary>
        /// Gets all base loads
        /// </summary>
        [HttpPost("{id}/calculate")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<ActionResult<BaseLoad>> CalculateBaseLoad(Guid id, [FromQuery] bool restoreDefaults = false)
        {
            _logger.LogInformation("Calculating baseloads for ID: {Id}", id);
            
            var baseLoad = await _baseLoadService.GetBaseLoadByIdAsync(id);
            if (baseLoad == null)
            {
                _logger.LogWarning("BaseLoad not found with ID: {Id}", id);
                return NotFound();
            }
            
            // Now directly uses the interface method
            var calculatedBaseLoad = await _baseLoadService.CalculateBaseLoadsAsync(baseLoad, restoreDefaults);
            
            return Ok(_mapper.Map<BaseLoadDto>(calculatedBaseLoad));
        }
        
        /// <summary>
        /// Sets default baseloads for a specific house
        /// </summary>
        [HttpPost("{id}/defaults")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<ActionResult<BaseLoad>> SetDefaultBaseLoads(Guid id, [FromQuery] bool pageOnly = false)
        {
            _logger.LogInformation("Setting default baseloads for ID: {Id}", id);
            
            var baseLoad = await _baseLoadService.GetBaseLoadByIdAsync(id);
            if (baseLoad == null)
            {
                _logger.LogWarning("BaseLoad not found with ID: {Id}", id);
                return NotFound();
            }
            
            // Now directly uses the interface method
            var defaultBaseLoad = await _baseLoadService.SetDefaultBaseloadsAsync(baseLoad, pageOnly);
            
            return Ok(_mapper.Map<BaseLoadDto>(defaultBaseLoad));
        }
        
        /// <summary>
        /// Sets MURB defaults for a specific house
        /// </summary>
        [HttpPost("{id}/murb-defaults")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<ActionResult<BaseLoad>> SetMURBDefaults(Guid id, [FromQuery] int unitCount = 1)
        {
            _logger.LogInformation("Setting MURB defaults for ID: {Id} with {UnitCount} units", id, unitCount);
            
            var baseLoad = await _baseLoadService.GetBaseLoadByIdAsync(id);
            if (baseLoad == null)
            {
                _logger.LogWarning("BaseLoad not found with ID: {Id}", id);
                return NotFound();
            }
            
            // Now directly uses the interface method
            var murbBaseLoad = await _baseLoadService.SetDefaultsForMURBAsync(baseLoad, unitCount);
            
            return Ok(_mapper.Map<BaseLoadDto>(murbBaseLoad));
        }
        
        /// <summary>
        /// Converts legacy gas appliance settings to new format
        /// </summary>
        [HttpPost("{id}/convert-legacy-gas")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<ActionResult<BaseLoad>> ConvertLegacyGasAppliances(Guid id)
        {
            _logger.LogInformation("Converting legacy gas appliances for ID: {Id}", id);
            
            var baseLoad = await _baseLoadService.GetBaseLoadByIdAsync(id);
            if (baseLoad == null)
            {
                _logger.LogWarning("BaseLoad not found with ID: {Id}", id);
                return NotFound();
            }
            
            // Now directly uses the interface method
            var updatedBaseLoad = await _baseLoadService.ConvertLegacyGasAppliancesAsync(baseLoad);
            
            return Ok(_mapper.Map<BaseLoadDto>(updatedBaseLoad));
        }
    
        [HttpGet]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<ActionResult<IEnumerable<BaseLoadDto>>> GetAllBaseLoads()
        {
            _logger.LogInformation("Getting all base loads");
            
            var baseLoads = await _baseLoadService.GetAllBaseLoadsAsync();
            
            if (baseLoads == null || !baseLoads.Any())
            {
                _logger.LogWarning("No base loads found");
                return NotFound();
            }
            
            var baseLoadDtos = _mapper.Map<IEnumerable<BaseLoadDto>>(baseLoads);
            
            return Ok(baseLoadDtos);
        }
        
        /// <summary>
        /// Gets base loads for a specific house
        /// </summary>
        [HttpGet("house/{houseId}")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<ActionResult<BaseLoadDto>> GetBaseLoadsByHouseId(Guid houseId)
        {
            _logger.LogInformation("Getting base load for house ID: {HouseId}", houseId);
    
            var baseLoad = await _baseLoadService.GetBaseLoadsByHouseIdAsync(houseId);
                _logger.LogInformation("Getting base load with ID: {Id}", houseId);
            
            if (baseLoad == null)
            {
                _logger.LogWarning("No base load found with ID: {Id}", houseId);
                return NotFound();
            }
            
            var baseLoadDto = _mapper.Map<BaseLoadDto>(baseLoad);
            
            return Ok(baseLoadDto);
        }
        
        /// <summary>
        /// Gets a specific base load by ID
        /// </summary>
        [HttpGet("{id}")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<ActionResult<BaseLoadDto>> GetBaseLoadById(Guid id)
        {
            _logger.LogInformation("Getting base load with ID: {Id}", id);
            
            var baseLoad = await _baseLoadService.GetBaseLoadByIdAsync(id);
            
            if (baseLoad == null)
            {
                _logger.LogWarning("No base load found with ID: {Id}", id);
                return NotFound();
            }
            
            var baseLoadDto = _mapper.Map<BaseLoadDto>(baseLoad);
            
            return Ok(baseLoadDto);
        }
        
        /// <summary>
        /// Creates a new base load with Hot2K declarative validation framework
        /// Uses external JSON/YAML rules instead of hardcoded validation
        /// Ensures only one baseload per house - if one exists, returns the existing one
        /// </summary>
        [HttpPost]
        [ProducesResponseType(StatusCodes.Status201Created)]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        public async Task<ActionResult<BaseLoadDto>> CreateBaseLoad([FromBody] JsonElement jsonElement, [FromQuery] string? buildingType = null)
        {
            if (jsonElement.ValueKind == JsonValueKind.Undefined || jsonElement.ValueKind == JsonValueKind.Null)
            {
                return BadRequest(new { error = "BaseLoadDto is required" });
            }

            // Use declarative validation from JSON/YAML rules
            var buildingTypeToUse = buildingType ?? "SingleFamily"; // Default building type

            // Validate the raw JSON against the camelCase validation rules
            var validationResult = await _validationService.ValidateJsonAsync(jsonElement.GetRawText(), "BaseLoadDto", buildingTypeToUse);

            // Debug: Log validation result details
            _logger.LogInformation("DEBUG: Validation IsValid: {IsValid}, Error Count: {ErrorCount}",
                validationResult.IsValid, validationResult.Errors?.Count() ?? 0);

            if (validationResult.Errors?.Any() == true)
            {
                foreach (var error in validationResult.Errors)
                {
                    _logger.LogInformation("DEBUG: Validation Error - Field: {Field}, Message: {Message}, Value: {Value}",
                        error.PropertyName, error.ErrorMessage, error.AttemptedValue);
                }
            }

            if (!validationResult.IsValid)
            {
                _logger.LogWarning("Declarative validation failed for BaseLoad creation. Building Type: {BuildingType}, Errors: {Errors}",
                    buildingTypeToUse, string.Join(", ", validationResult.Errors.Select(e => $"{e.PropertyName}: {e.ErrorMessage}")));

                return BadRequest(new
                {
                    error = "Validation failed",
                    buildingType = buildingTypeToUse,
                    validationErrors = validationResult.Errors.Select(e => new
                    {
                        field = e.PropertyName,
                        message = e.ErrorMessage,
                        attemptedValue = e.AttemptedValue
                    })
                });
            }

            _logger.LogInformation("Declarative validation passed for BaseLoad creation. Building Type: {BuildingType}", buildingTypeToUse);

            // Deserialize the JSON to DTO after validation passes
            var baseLoadDto = JsonSerializer.Deserialize<BaseLoadDto>(jsonElement.GetRawText(), new JsonSerializerOptions
            {
                PropertyNameCaseInsensitive = true,
                PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
                Converters = { new JsonStringEnumConverter() }
            });

            if (baseLoadDto == null)
            {
                return BadRequest(new { error = "Failed to deserialize BaseLoadDto" });
            }

            _logger.LogInformation("Creating new base load for house ID: {HouseId}", baseLoadDto.HouseId);

            // Check if a baseload already exists for this house
            var existingBaseLoad = await _baseLoadService.GetBaseLoadsByHouseIdAsync(baseLoadDto.HouseId);
            if (existingBaseLoad != null)
            {
                _logger.LogInformation("BaseLoad already exists for house ID: {HouseId}, returning existing baseload with ID: {Id}",
                    baseLoadDto.HouseId, existingBaseLoad.Id);

                // Return the existing baseload
                var existingBaseLoadDto = _mapper.Map<BaseLoadDto>(existingBaseLoad);
                return Ok(existingBaseLoadDto);
            }

            // Map DTO to entity and log
            var baseLoad = _mapper.Map<BaseLoad>(baseLoadDto);
            _logger.LogInformation("After mapping Entity Adults Occupants: {Count}",
                baseLoad.Occupancy?.Adults?.Occupants ?? -1);

            // Only generate primary key if not provided
            if (baseLoad.Id == Guid.Empty)
            {
                baseLoad.Id = Guid.NewGuid();
            }

            var createdBaseLoad = await _baseLoadService.CreateBaseLoadAsync(baseLoad);

            // Map back to DTO for response
            var createdBaseLoadDto = _mapper.Map<BaseLoadDto>(createdBaseLoad);

            return CreatedAtAction(nameof(GetBaseLoadById), new { id = createdBaseLoadDto.Id }, createdBaseLoadDto);
        }

        
        /// <summary>
        /// Updates an existing base load with Hot2K declarative validation framework
        /// Uses external JSON/YAML rules instead of hardcoded validation
        /// </summary>
        [HttpPut("{id}")]
        [ProducesResponseType(StatusCodes.Status204NoContent)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> UpdateBaseLoad(Guid id, [FromBody] JsonElement jsonElement, [FromQuery] string? buildingType = null)
        {
            if (jsonElement.ValueKind == JsonValueKind.Undefined || jsonElement.ValueKind == JsonValueKind.Null)
            {
                return BadRequest(new { error = "BaseLoadDto is required" });
            }

            // Use declarative validation from JSON/YAML rules
            var buildingTypeToUse = buildingType ?? "SingleFamily"; // Default building type

            // Validate the raw JSON against the camelCase validation rules
            var validationResult = await _validationService.ValidateJsonAsync(jsonElement.GetRawText(), "BaseLoadDto", buildingTypeToUse);

            // Debug: Log validation result details
            _logger.LogInformation("DEBUG: PUT Validation IsValid: {IsValid}, Error Count: {ErrorCount}",
                validationResult.IsValid, validationResult.Errors?.Count() ?? 0);

            if (validationResult.Errors?.Any() == true)
            {
                foreach (var error in validationResult.Errors)
                {
                    _logger.LogInformation("DEBUG: PUT Validation Error - Field: {Field}, Message: {Message}, Value: {Value}",
                        error.PropertyName, error.ErrorMessage, error.AttemptedValue);
                }
            }

            if (!validationResult.IsValid)
            {
                _logger.LogWarning("Declarative validation failed for BaseLoad update. Building Type: {BuildingType}, Errors: {Errors}",
                    buildingTypeToUse, string.Join(", ", validationResult.Errors.Select(e => $"{e.PropertyName}: {e.ErrorMessage}")));

                return BadRequest(new
                {
                    error = "Validation failed",
                    buildingType = buildingTypeToUse,
                    validationErrors = validationResult.Errors.Select(e => new
                    {
                        field = e.PropertyName,
                        message = e.ErrorMessage,
                        attemptedValue = e.AttemptedValue
                    })
                });
            }

            _logger.LogInformation("Declarative validation passed for BaseLoad update. Building Type: {BuildingType}", buildingTypeToUse);

            // Deserialize the JSON to DTO after validation passes
            var baseLoadDto = JsonSerializer.Deserialize<BaseLoadDto>(jsonElement.GetRawText(), new JsonSerializerOptions
            {
                PropertyNameCaseInsensitive = true,
                PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
                Converters = { new JsonStringEnumConverter() }
            });

            if (baseLoadDto == null)
            {
                return BadRequest(new { error = "Failed to deserialize BaseLoadDto" });
            }

            if (id != baseLoadDto.Id)
            {
                return BadRequest("ID in URL does not match ID in request body");
            }

            try
            {
                // Check if entity exists
                var existingBaseLoad = await _baseLoadService.GetBaseLoadByIdAsync(id);
                if (existingBaseLoad == null)
                {
                    _logger.LogWarning("Base load with ID: {Id} not found for update", id);
                    return NotFound();
                }

                _logger.LogInformation("Updating base load with ID: {Id}", id);

                // Map the DTO to the domain entity
                var baseLoad = _mapper.Map<BaseLoad>(baseLoadDto);
                
                // Make sure to preserve the original ID
                baseLoad.Id = id;
                
                // Ensure IDs are correctly set for nested OccupantsAtHome entities
                if (baseLoad.Occupancy != null)
                {
                    // Preserve existing Occupancy ID
                    if (existingBaseLoad.Occupancy != null)
                    {
                        baseLoad.Occupancy.Id = existingBaseLoad.Occupancy.Id;
                    }
                    
                    // Handle the Adults property
                    if (baseLoad.Occupancy.Adults != null)
                    {
                        if (existingBaseLoad.Occupancy?.Adults != null)
                        {
                            baseLoad.Occupancy.Adults.Id = existingBaseLoad.Occupancy.Adults.Id;
                        }
                        else if (baseLoad.Occupancy.Adults.Id == Guid.Empty)
                        {
                            baseLoad.Occupancy.Adults.Id = Guid.NewGuid();
                        }
                        baseLoad.Occupancy.Adults.OccupancyId = baseLoad.Occupancy.Id;
                        baseLoad.Occupancy.Adults.OccupantType = OccupantType.Adults;
                    }
                    
                    // Handle the Children property
                    if (baseLoad.Occupancy.Children != null)
                    {
                        if (existingBaseLoad.Occupancy?.Children != null)
                        {
                            baseLoad.Occupancy.Children.Id = existingBaseLoad.Occupancy.Children.Id;
                        }
                        else if (baseLoad.Occupancy.Children.Id == Guid.Empty)
                        {
                            baseLoad.Occupancy.Children.Id = Guid.NewGuid();
                        }
                        baseLoad.Occupancy.Children.OccupancyId = baseLoad.Occupancy.Id;
                        baseLoad.Occupancy.Children.OccupantType = OccupantType.Children;
                    }
                    
                    // Handle the Infants property
                    if (baseLoad.Occupancy.Infants != null)
                    {
                        if (existingBaseLoad.Occupancy?.Infants != null)
                        {
                            baseLoad.Occupancy.Infants.Id = existingBaseLoad.Occupancy.Infants.Id;
                        }
                        else if (baseLoad.Occupancy.Infants.Id == Guid.Empty)
                        {
                            baseLoad.Occupancy.Infants.Id = Guid.NewGuid();
                        }
                        baseLoad.Occupancy.Infants.OccupancyId = baseLoad.Occupancy.Id;
                        baseLoad.Occupancy.Infants.OccupantType = OccupantType.Infants;
                    }
                }
                
                // Handle the Summary property
                if (baseLoad.Summary != null)
                {
                    if (existingBaseLoad.Summary != null)
                    {
                        baseLoad.Summary.Id = existingBaseLoad.Summary.Id;
                    }
                    else if (baseLoad.Summary.Id == Guid.Empty)
                    {
                        baseLoad.Summary.Id = Guid.NewGuid();
                    }
                    baseLoad.Summary.BaseLoadId = baseLoad.Id;
                }
                
                // Handle WaterUsage and its child objects
                if (baseLoad.WaterUsage != null)
                {
                    // Preserve existing WaterUsage ID
                    if (existingBaseLoad.WaterUsage != null)
                    {
                        baseLoad.WaterUsage.Id = existingBaseLoad.WaterUsage.Id;
                    }
                    else if (baseLoad.WaterUsage.Id == Guid.Empty)
                    {
                        baseLoad.WaterUsage.Id = Guid.NewGuid();
                    }
                    
                    baseLoad.WaterUsage.BaseLoadId = baseLoad.Id;
                    
                    // Set default values for child objects if they're null
                    if (baseLoad.WaterUsage.BathroomFaucets == null)
                        baseLoad.WaterUsage.BathroomFaucets = new BathroomFaucets();
                    
                    if (baseLoad.WaterUsage.Shower == null)
                        baseLoad.WaterUsage.Shower = new Shower();
                    else
                    {
                        // Initialize ShowerTemperature if it's null
                        if (baseLoad.WaterUsage.Shower.Temperature == null)
                            baseLoad.WaterUsage.Shower.Temperature = new ShowerTemperature();
                        
                        // Initialize ShowerFlowRate if it's null
                        if (baseLoad.WaterUsage.Shower.FlowRate == null)
                            baseLoad.WaterUsage.Shower.FlowRate = new ShowerFlowRate();
                    }
                    
                    // Handle ClothesWasher
                    if (baseLoad.WaterUsage.ClothesWasher == null)
                        baseLoad.WaterUsage.ClothesWasher = new ClothesWasher();
                    else
                    {
                        if (baseLoad.WaterUsage.ClothesWasher.RatedValues == null)
                        {
                            baseLoad.WaterUsage.ClothesWasher.RatedValues = new RatedValue();
                            baseLoad.WaterUsage.ClothesWasher.RatedValues.Text = "Default";
                        }
                        else if (string.IsNullOrEmpty(baseLoad.WaterUsage.ClothesWasher.RatedValues.Text))
                        {       
                            baseLoad.WaterUsage.ClothesWasher.RatedValues.Text = 
                            baseLoad.WaterUsage.ClothesWasher.RatedValues.EnglishText ?? "Default";
                        }
                        
                        if (baseLoad.WaterUsage.ClothesWasher.Temperature == null)
                            baseLoad.WaterUsage.ClothesWasher.Temperature = new ClothesWasherTemperature();
                    }
                    
                    // Handle DishWasher
                    if (baseLoad.WaterUsage.DishWasher == null)
                        baseLoad.WaterUsage.DishWasher = new DishWasher();
                    else
                    {
                        if (baseLoad.WaterUsage.DishWasher.RatedValues == null)
                        {
                            baseLoad.WaterUsage.DishWasher.RatedValues = new RatedValue();
                            baseLoad.WaterUsage.DishWasher.RatedValues.Text = "Default";
                        }
                        else if (string.IsNullOrEmpty(baseLoad.WaterUsage.DishWasher.RatedValues.Text))
                        {
                            baseLoad.WaterUsage.DishWasher.RatedValues.Text = 
                            baseLoad.WaterUsage.DishWasher.RatedValues.EnglishText ?? "Default";
                        }
                    }
                }
                
                // Handle ElectricalUsage and its child objects
                if (baseLoad.ElectricalUsage != null)
                {
                    // Preserve existing ElectricalUsage ID
                    if (existingBaseLoad.ElectricalUsage != null)
                    {
                        baseLoad.ElectricalUsage.Id = existingBaseLoad.ElectricalUsage.Id;
                    }
                    else if (baseLoad.ElectricalUsage.Id == Guid.Empty)
                    {
                        baseLoad.ElectricalUsage.Id = Guid.NewGuid();
                    }
                    
                    baseLoad.ElectricalUsage.BaseLoadId = baseLoad.Id;
                    
                    // Handle ClothesDryer
                    if (baseLoad.ElectricalUsage.ClothesDryer == null)
                    {
                        baseLoad.ElectricalUsage.ClothesDryer = new ClothesDryer();
                    }
                    else
                    {
                        // Initialize ApplianceEnergySources if it's null
                        if (baseLoad.ElectricalUsage.ClothesDryer.EnergySource == null)
                            baseLoad.ElectricalUsage.ClothesDryer.EnergySource = ApplianceEnergySources.Electric();
                        else if (string.IsNullOrEmpty(baseLoad.ElectricalUsage.ClothesDryer.EnergySource.Code))
                            baseLoad.ElectricalUsage.ClothesDryer.EnergySource = ApplianceEnergySources.Electric();
                        
                        // Initialize DryerRatedConsumptions if it's null
                        if (baseLoad.ElectricalUsage.ClothesDryer.RatedValue == null)
                            baseLoad.ElectricalUsage.ClothesDryer.RatedValue = DryerRatedConsumptions.Default();
                        else if (string.IsNullOrEmpty(baseLoad.ElectricalUsage.ClothesDryer.RatedValue.Code))
                            baseLoad.ElectricalUsage.ClothesDryer.RatedValue = DryerRatedConsumptions.Default();
                        
                        // Initialize Location if it's null
                        if (baseLoad.ElectricalUsage.ClothesDryer.Location == null)
                            baseLoad.ElectricalUsage.ClothesDryer.Location = new CodeAndText("1", "Electric", "Électricité");
                    }
                    
                    // Handle Stove
                    if (baseLoad.ElectricalUsage.Stove == null)
                    {
                        baseLoad.ElectricalUsage.Stove = new Stove();
                    }
                    else
                    {
                        // Initialize ApplianceEnergySources if it's null
                        if (baseLoad.ElectricalUsage.Stove.EnergySource == null)
                            baseLoad.ElectricalUsage.Stove.EnergySource = ApplianceEnergySources.Electric();
                        else if (string.IsNullOrEmpty(baseLoad.ElectricalUsage.Stove.EnergySource.Code))
                            baseLoad.ElectricalUsage.Stove.EnergySource = ApplianceEnergySources.Electric();
                        
                        // Initialize StoveRatedConsumptions if it's null
                        if (baseLoad.ElectricalUsage.Stove.RatedValue == null)
                            baseLoad.ElectricalUsage.Stove.RatedValue = StoveRatedConsumptions.Default();
                        else if (string.IsNullOrEmpty(baseLoad.ElectricalUsage.Stove.RatedValue.Code))
                            baseLoad.ElectricalUsage.Stove.RatedValue = StoveRatedConsumptions.Default();
                    }
                    
                    // Handle Refrigerator
                    if (baseLoad.ElectricalUsage.Refrigerator == null)
                        baseLoad.ElectricalUsage.Refrigerator = RefrigeratorRatedConsumptions.Default();
                    else if (string.IsNullOrEmpty(baseLoad.ElectricalUsage.Refrigerator.Code))
                        baseLoad.ElectricalUsage.Refrigerator = RefrigeratorRatedConsumptions.Default();
                    
                    // Handle InteriorLighting
                    if (baseLoad.ElectricalUsage.InteriorLighting == null)
                        baseLoad.ElectricalUsage.InteriorLighting = InteriorLightingTypes.LessThan25Percent();
                    else if (string.IsNullOrEmpty(baseLoad.ElectricalUsage.InteriorLighting.Code))
                        baseLoad.ElectricalUsage.InteriorLighting = InteriorLightingTypes.LessThan25Percent();
                }
                else
                {
                    // Create ElectricalUsage if it's null
                    baseLoad.ElectricalUsage = new ElectricalUsage
                    {
                        Id = Guid.NewGuid(),
                        BaseLoadId = baseLoad.Id
                    };
                    // It will use the defaults from the constructor
                }
                if (baseLoad.AdvancedUserSpecified != null)
                {
            // Preserve existing AdvancedUserSpecified ID
                    if (existingBaseLoad.AdvancedUserSpecified != null)
                    {
                        baseLoad.AdvancedUserSpecified.Id = existingBaseLoad.AdvancedUserSpecified.Id;
                    }
                    else if (baseLoad.AdvancedUserSpecified.Id == Guid.Empty)
                    {
                        baseLoad.AdvancedUserSpecified.Id = Guid.NewGuid();
                    }
            
                    baseLoad.AdvancedUserSpecified.BaseLoadId = baseLoad.Id;
            
            // Ensure DryerLocation has default values if null
                    if (baseLoad.AdvancedUserSpecified.DryerLocation == null)
                    {
                        baseLoad.AdvancedUserSpecified.DryerLocation = new CodeAndText("1", "Main Floor", "Plancher Principal");
                    }
            
            // Ensure GasStove is never null
                    if (baseLoad.AdvancedUserSpecified.GasStove == null)
                    {
                        baseLoad.AdvancedUserSpecified.GasStove = ApplianceEnergySourceSpecified.NaturalGas();
                    }
                    else if (string.IsNullOrEmpty(baseLoad.AdvancedUserSpecified.GasStove.Code))
                    {
                // If GasStove exists but has no code, replace it with the default
                        baseLoad.AdvancedUserSpecified.GasStove = ApplianceEnergySourceSpecified.NaturalGas();
                    }
                    else
                    {
                // Get the correct predefined instance based on code
                        if (baseLoad.AdvancedUserSpecified.GasStove.Code == "2")
                        {
                            var gasStove = ApplianceEnergySourceSpecified.NaturalGas();
                            gasStove.Value = baseLoad.AdvancedUserSpecified.GasStove.Value;
                            baseLoad.AdvancedUserSpecified.GasStove = gasStove;
                        }
                        else if (baseLoad.AdvancedUserSpecified.GasStove.Code == "4")
                        {
                            var gasStove = ApplianceEnergySourceSpecified.Propane();
                            gasStove.Value = baseLoad.AdvancedUserSpecified.GasStove.Value;
                            baseLoad.AdvancedUserSpecified.GasStove = gasStove;
                        }
                    }
            
            // Ensure GasDryer is never null
                    if (baseLoad.AdvancedUserSpecified.GasDryer == null)
                    {
                        baseLoad.AdvancedUserSpecified.GasDryer = ApplianceEnergySourceSpecified.Propane();
                    }
                    else if (string.IsNullOrEmpty(baseLoad.AdvancedUserSpecified.GasDryer.Code))
                    {
                // If GasDryer exists but has no code, replace it with the default
                        baseLoad.AdvancedUserSpecified.GasDryer = ApplianceEnergySourceSpecified.Propane();
                    }
                    else
                    {
                // Get the correct predefined instance based on code
                        if (baseLoad.AdvancedUserSpecified.GasDryer.Code == "2")
                        {
                            var gasDryer = ApplianceEnergySourceSpecified.NaturalGas();
                            gasDryer.Value = baseLoad.AdvancedUserSpecified.GasDryer.Value;
                            baseLoad.AdvancedUserSpecified.GasDryer = gasDryer;
                        }
                        else if (baseLoad.AdvancedUserSpecified.GasDryer.Code == "4")
                        {
                            var gasDryer = ApplianceEnergySourceSpecified.Propane();
                            gasDryer.Value = baseLoad.AdvancedUserSpecified.GasDryer.Value;
                            baseLoad.AdvancedUserSpecified.GasDryer = gasDryer;
                        }
                    }
                }
                else
                {
            // Create AdvancedUserSpecified if it's null
                    baseLoad.AdvancedUserSpecified = new AdvancedUserSpecified
                    {
                        Id = Guid.NewGuid(),
                        BaseLoadId = baseLoad.Id,
                        HotWaterTemperature = 55,
                        GasStove = ApplianceEnergySourceSpecified.NaturalGas(),
                        GasDryer = ApplianceEnergySourceSpecified.Propane(),
                        DryerLocation = new CodeAndText("1", "Main Floor", "Plancher Principal")
                    };
                }

        // Update through service
                await _baseLoadService.UpdateBaseLoadAsync(baseLoad);

                return NoContent();
            }
            catch (KeyNotFoundException ex)
            {
                _logger.LogWarning(ex, "Base load with ID: {Id} not found for update", id);
                return NotFound();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating base load with ID {Id}", id);
                return StatusCode(500, "An error occurred while updating the base load");
            }
        }
        
        /// <summary>
        /// Deletes a base load
        /// </summary>
        [HttpDelete("{id}")]
        [ProducesResponseType(StatusCodes.Status204NoContent)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<IActionResult> DeleteBaseLoad(Guid id)
        {
            var existingBaseLoad = await _baseLoadService.GetBaseLoadByIdAsync(id);
            if (existingBaseLoad == null)
            {
                _logger.LogWarning("Base load with ID: {Id} not found for deletion", id);
                return NotFound();
            }
            
            _logger.LogInformation("Deleting base load with ID: {Id}", id);
            
            await _baseLoadService.DeleteBaseLoadAsync(id);
            
            return NoContent();
        }

        /// <summary>
        /// Gets base load for a specific energy upgrade
        /// </summary>
        [HttpGet("energy-upgrades/{energyUpgradeId}/baseloads")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<ActionResult<BaseLoadDto>> GetBaseLoadByEnergyUpgradeId(Guid energyUpgradeId)
        {
            _logger.LogInformation("Getting base load for energy upgrade ID: {EnergyUpgradeId}", energyUpgradeId);

            var baseLoad = await _baseLoadService.GetBaseLoadByEnergyUpgradeIdAsync(energyUpgradeId);

            if (baseLoad == null)
            {
                _logger.LogWarning("Base load not found for energy upgrade ID: {EnergyUpgradeId}", energyUpgradeId);
                return NotFound($"Base load not found for energy upgrade ID: {energyUpgradeId}");
            }

            var baseLoadDto = _mapper.Map<BaseLoadDto>(baseLoad);
            return Ok(baseLoadDto);
        }

        /// <summary>
        /// Creates a copy of an existing base load for energy upgrade purposes
        /// Only requires energyUpgradeId - automatically copies all values from the base load
        /// </summary>
        [HttpPost("baseloads/{baseBaseLoadId}/duplicate-for-energy-upgrade")]
        [ProducesResponseType(StatusCodes.Status201Created)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        public async Task<ActionResult<BaseLoadDto>> DuplicateBaseLoadForEnergyUpgrade(
            Guid baseBaseLoadId,
            [FromQuery] Guid energyUpgradeId)
        {
            try
            {
                _logger.LogInformation("Duplicating base load {BaseBaseLoadId} for energy upgrade {EnergyUpgradeId}",
                    baseBaseLoadId, energyUpgradeId);

                // Get the base load
                var baseBaseLoad = await _baseLoadService.GetBaseLoadByIdAsync(baseBaseLoadId);
                if (baseBaseLoad == null)
                {
                    _logger.LogWarning("Base load with ID: {BaseBaseLoadId} not found", baseBaseLoadId);
                    return NotFound($"Base load with ID {baseBaseLoadId} not found");
                }

                // Create a duplicate with new ID but same values
                var duplicatedBaseLoad = await _baseLoadService.DuplicateBaseLoadForEnergyUpgradeAsync(baseBaseLoad, energyUpgradeId);

                var duplicatedBaseLoadDto = _mapper.Map<BaseLoadDto>(duplicatedBaseLoad);

                _logger.LogInformation("Successfully duplicated base load {BaseBaseLoadId} as {NewBaseLoadId} for energy upgrade {EnergyUpgradeId}",
                    baseBaseLoadId, duplicatedBaseLoad.Id, energyUpgradeId);

                return CreatedAtAction(nameof(GetBaseLoadsByHouseId),
                    new { houseId = duplicatedBaseLoad.HouseId },
                    duplicatedBaseLoadDto);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error duplicating base load {BaseBaseLoadId} for energy upgrade {EnergyUpgradeId}",
                    baseBaseLoadId, energyUpgradeId);
                return StatusCode(StatusCodes.Status500InternalServerError,
                    "An error occurred while duplicating the base load for energy upgrade");
            }
        }
    }
}