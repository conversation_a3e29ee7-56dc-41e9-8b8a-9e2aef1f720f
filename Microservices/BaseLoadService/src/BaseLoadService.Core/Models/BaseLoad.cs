using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace BaseLoadService.Core.Models
{
    /// <summary>
    /// Represents the base load information for a house
    /// </summary>
    public class BaseLoad
    {
        public Guid Id { get; set; }

        public Guid HouseId { get; set; }

        public Guid? EnergyUpgradeId { get; set; }

        [Column(TypeName = "decimal(18,2)")]
        public decimal BasementFractionOfInternalGains { get; set; } = 0.15m;
        
        [Column(TypeName = "decimal(18,2)")]
        public decimal CommonSpaceElectricalConsumption { get; set; }
        
        // Navigation properties
        public Occupancy Occupancy { get; set; } = null!;
        public Summary Summary { get; set; } = null!;
        public WaterUsage? WaterUsage { get; set; }
        public ElectricalUsage? ElectricalUsage { get; set; }
        public AdvancedUserSpecified? AdvancedUserSpecified { get; set; }
    }
}