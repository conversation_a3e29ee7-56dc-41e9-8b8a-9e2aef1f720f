using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using BaseLoadService.Core.Models;

namespace BaseLoadService.Core.Interfaces
{
    /// <summary>
    /// Repository interface for base load operations
    /// </summary>
    public interface IBaseLoadRepository
    {
        /// <summary>
        /// Gets all base loads with their related entities
        /// </summary>
        Task<IEnumerable<BaseLoad>> GetAllBaseLoadsAsync();
        
        /// <summary>
        /// Gets base loads for a specific house with their related entities
        /// </summary>
        Task<BaseLoad?> GetBaseLoadsByHouseIdAsync(Guid houseId);
        
        /// <summary>
        /// Gets a specific base load by ID with its related entities
        /// </summary>
        Task<BaseLoad?> GetBaseLoadByIdAsync(Guid id);
        
        /// <summary>
        /// Creates a new base load with its related entities
        /// </summary>
        Task<BaseLoad> CreateBaseLoadAsync(BaseLoad baseLoad);
        
        /// <summary>
        /// Updates an existing base load and its related entities
        /// </summary>
        Task UpdateBaseLoadAsync(BaseLoad baseLoad);
        
        /// <summary>
        /// Deletes a base load and its related entities
        /// </summary>
        Task DeleteBaseLoadAsync(Guid id);

        /// <summary>
        /// Gets base load for a specific energy upgrade with its related entities
        /// </summary>
        Task<BaseLoad?> GetBaseLoadByEnergyUpgradeIdAsync(Guid energyUpgradeId);

        /// <summary>
        /// Creates a duplicate of an existing base load for energy upgrade purposes
        /// </summary>
        Task<BaseLoad> DuplicateBaseLoadForEnergyUpgradeAsync(BaseLoad baseBaseLoad, Guid energyUpgradeId);
    }
}