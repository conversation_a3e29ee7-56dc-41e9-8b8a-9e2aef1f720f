using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using BaseLoadService.Core.Models;

namespace BaseLoadService.Core.Interfaces
{
    public interface IBaseLoadService
    {
        /// <summary>
        /// Gets all base loads
        /// </summary>
        Task<IEnumerable<BaseLoad>> GetAllBaseLoadsAsync();
        
        /// <summary>
        /// Gets all base loads for a specific house
        /// </summary>
        Task<BaseLoad?> GetBaseLoadsByHouseIdAsync(Guid houseId);
        
        /// <summary>
        /// Gets a specific base load by ID
        /// </summary>
        Task<BaseLoad?> GetBaseLoadByIdAsync(Guid id);
        
        /// <summary>
        /// Creates a new base load
        /// </summary>
        Task<BaseLoad> CreateBaseLoadAsync(BaseLoad baseLoad);
        
        /// <summary>
        /// Updates an existing base load
        /// </summary>
        Task UpdateBaseLoadAsync(BaseLoad baseLoad);
        
        /// <summary>
        /// Deletes a base load
        /// </summary>
        Task DeleteBaseLoadAsync(Guid id);

        /// <summary>
        /// Gets base load for a specific energy upgrade
        /// </summary>
        Task<BaseLoad?> GetBaseLoadByEnergyUpgradeIdAsync(Guid energyUpgradeId);

        /// <summary>
        /// Creates a duplicate of an existing base load for energy upgrade purposes
        /// </summary>
        Task<BaseLoad> DuplicateBaseLoadForEnergyUpgradeAsync(BaseLoad baseBaseLoad, Guid energyUpgradeId);

 // Calculation methods
        Task<BaseLoad> CalculateBaseLoadsAsync(BaseLoad baseLoad, bool restoreDefaults = false);
        Task<BaseLoad> SetDefaultBaseloadsAsync(BaseLoad baseLoad, bool pageOnly = false, bool skipCalculateBaseloads = false);
        Task<BaseLoad> SetDefaultsForMURBAsync(BaseLoad baseLoad, int unitCount = 1);
        Task<BaseLoad> ConvertLegacyGasAppliancesAsync(BaseLoad baseLoad);
    }
}