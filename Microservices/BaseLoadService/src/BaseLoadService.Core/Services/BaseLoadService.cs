using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using BaseLoadService.Core.Interfaces;
using BaseLoadService.Core.Models;
using Microsoft.Extensions.Logging;


namespace BaseLoadService.Core.Services
{
    public class BaseLoadService : IBaseLoadService
    {
        private readonly IBaseLoadRepository _repository;
        private readonly ILogger<BaseLoadService> _logger;
        private readonly IDataLookupService _lookupService;
        private readonly ISimulationServiceClient _simulationClient;
        
        public BaseLoadService(
            IBaseLoadRepository repository,
            ILogger<BaseLoadService> logger,
            IDataLookupService lookupService,
             ISimulationServiceClient simulationClient)
        {
            _repository = repository;
            _logger = logger;
            _lookupService = lookupService;
            _simulationClient = simulationClient;
        }
        
        public async Task<IEnumerable<BaseLoad>> GetAllBaseLoadsAsync()
        {
            _logger.LogInformation("Getting all base loads from service");
            return await _repository.GetAllBaseLoadsAsync();
        }
        
        public async Task<BaseLoad?> GetBaseLoadsByHouseIdAsync(Guid houseId)
        {
            _logger.LogInformation("Getting base loads for house ID: {HouseId} from service", houseId);
            return await _repository.GetBaseLoadsByHouseIdAsync(houseId);
        }
        
        public async Task<BaseLoad?> GetBaseLoadByIdAsync(Guid id)
        {
            _logger.LogInformation("Getting base load with ID: {Id} from service", id);
            return await _repository.GetBaseLoadByIdAsync(id);
        }

        public async Task<BaseLoad> CreateBaseLoadAsync(BaseLoad baseLoad)
        {
            _logger.LogInformation("Creating new base load for house ID: {HouseId} from service", baseLoad.HouseId);
    
    // Log the incoming entity to debug
            _logger.LogDebug("Incoming baseLoad entity: {Entity}");
    
    // Only establish relationships and generate IDs where necessary
    // IMPORTANT: We don't want to override any values from the client

    // Set up Occupancy relationships
            if (baseLoad.Occupancy != null)
            {
                if (baseLoad.Occupancy.Id == Guid.Empty)
                {
                    baseLoad.Occupancy.Id = Guid.NewGuid();
                }
                baseLoad.Occupancy.BaseLoadId = baseLoad.Id;
        
        // Set up nested occupant relationships
                if (baseLoad.Occupancy.Adults != null)
                {
                    if (baseLoad.Occupancy.Adults.Id == Guid.Empty)
                        baseLoad.Occupancy.Adults.Id = Guid.NewGuid();
                    baseLoad.Occupancy.Adults.OccupancyId = baseLoad.Occupancy.Id;
            // Only set OccupantType if it's not set already
                    if (baseLoad.Occupancy.Adults.OccupantType != OccupantType.Adults)
                        baseLoad.Occupancy.Adults.OccupantType = OccupantType.Adults;
                }
                else
                {
            // Create minimal Adult structure if none exists
                    baseLoad.Occupancy.Adults = new OccupantsAtHome
                    {
                        Id = Guid.NewGuid(),
                        OccupancyId = baseLoad.Occupancy.Id,
                        OccupantType = OccupantType.Adults,
                        Occupants = 2, // Default value
                        AtHome = 50m   // Default value
                    };
                }
        
                if (baseLoad.Occupancy.Children != null)
                {
                    if (baseLoad.Occupancy.Children.Id == Guid.Empty)
                        baseLoad.Occupancy.Children.Id = Guid.NewGuid();
                    baseLoad.Occupancy.Children.OccupancyId = baseLoad.Occupancy.Id;
            // Only set OccupantType if it's not set already
                    if (baseLoad.Occupancy.Children.OccupantType != OccupantType.Children)
                        baseLoad.Occupancy.Children.OccupantType = OccupantType.Children;
                }
                else
                {
            // Create minimal Children structure if none exists
                    baseLoad.Occupancy.Children = new OccupantsAtHome
                    {
                        Id = Guid.NewGuid(),
                        OccupancyId = baseLoad.Occupancy.Id,
                        OccupantType = OccupantType.Children,
                        Occupants = 1, // Default value
                        AtHome = 50m   // Default value
                    };
                }
        
                if (baseLoad.Occupancy.Infants != null)
                {
                    if (baseLoad.Occupancy.Infants.Id == Guid.Empty)
                        baseLoad.Occupancy.Infants.Id = Guid.NewGuid();
                    baseLoad.Occupancy.Infants.OccupancyId = baseLoad.Occupancy.Id;
            // Only set OccupantType if it's not set already
                    if (baseLoad.Occupancy.Infants.OccupantType != OccupantType.Infants)
                        baseLoad.Occupancy.Infants.OccupantType = OccupantType.Infants;
                }
                else
                {
            // Create minimal Infants structure if none exists
                    baseLoad.Occupancy.Infants = new OccupantsAtHome
                    {
                        Id = Guid.NewGuid(),
                        OccupancyId = baseLoad.Occupancy.Id,
                        OccupantType = OccupantType.Infants,
                        Occupants = 0, // Default value
                        AtHome = 0m    // Default value
                    };
                }
            }
            else
            {
        // Only create a default occupancy if none was provided
                baseLoad.Occupancy = new Occupancy
                {
                    Id = Guid.NewGuid(),
                    BaseLoadId = baseLoad.Id,
                    Adults = new OccupantsAtHome
                    {       
                        Id = Guid.NewGuid(),
                        OccupantType = OccupantType.Adults,
                        Occupants = 2,
                        AtHome = 50m
                    },
                    Children = new OccupantsAtHome
                    {
                        Id = Guid.NewGuid(),
                        OccupantType = OccupantType.Children,
                        Occupants = 1,
                        AtHome = 50m
                    },
                    Infants = new OccupantsAtHome
                    {
                        Id = Guid.NewGuid(),
                        OccupantType = OccupantType.Infants,
                        Occupants = 0,
                        AtHome = 0m
                    }
                };
        
        // Set OccupancyId for each occupant type
                baseLoad.Occupancy.Adults.OccupancyId = baseLoad.Occupancy.Id;
                baseLoad.Occupancy.Children.OccupancyId = baseLoad.Occupancy.Id;
                baseLoad.Occupancy.Infants.OccupancyId = baseLoad.Occupancy.Id;
            }
    
    // Set up Summary relationship
            if (baseLoad.Summary != null)
            {
                if (baseLoad.Summary.Id == Guid.Empty)
                {
                    baseLoad.Summary.Id = Guid.NewGuid();
                }
                baseLoad.Summary.BaseLoadId = baseLoad.Id;
            }
            else
            {
        // Only create default if missing
                baseLoad.Summary = new Summary
                {
                    Id = Guid.NewGuid(),
                    BaseLoadId = baseLoad.Id,
                    IsSpecified = false,
                    ElectricalAppliances = 6.30m,
                    Lighting = 2.60m,
                    OtherElectric = 9.70m,
                    ExteriorUse = 0.90m,
                    HotWaterLoad = 187.63m
                };
            }
    
    // Set up WaterUsage relationship
            if (baseLoad.WaterUsage != null)
            {
                if (baseLoad.WaterUsage.Id == Guid.Empty)
                {
                    baseLoad.WaterUsage.Id = Guid.NewGuid();
                }
                baseLoad.WaterUsage.BaseLoadId = baseLoad.Id;
        
        // ONLY create missing objects and relationships
        // Do NOT replace existing values with defaults
        
        // Ensure Shower has required properties
                if (baseLoad.WaterUsage.Shower == null)
                {
                    baseLoad.WaterUsage.Shower = new Shower();
                }
        
                if (baseLoad.WaterUsage.Shower.Temperature == null)
                {
                    baseLoad.WaterUsage.Shower.Temperature = new ShowerTemperature();
                }
        
                if (baseLoad.WaterUsage.Shower.FlowRate == null)
                {
                    baseLoad.WaterUsage.Shower.FlowRate = new ShowerFlowRate();
                }
        
        // Ensure BathroomFaucets exists
                if (baseLoad.WaterUsage.BathroomFaucets == null)
                {
                    baseLoad.WaterUsage.BathroomFaucets = new BathroomFaucets();
                }
        
        // Ensure ClothesWasher has required properties
                if (baseLoad.WaterUsage.ClothesWasher == null)
                {
                    baseLoad.WaterUsage.ClothesWasher = new ClothesWasher();
                }
        
                if (baseLoad.WaterUsage.ClothesWasher.RatedValues == null)
                {
                    baseLoad.WaterUsage.ClothesWasher.RatedValues = new RatedValue
                    {   
                        Text = "Default",
                        EnglishText = "Default",
                        FrenchText = "Par défaut",
                        Code = "1"
                    };
                }
                else
                {
            // Only ensure required fields have some value if missing
                    if (string.IsNullOrEmpty(baseLoad.WaterUsage.ClothesWasher.RatedValues.Code))
                        baseLoad.WaterUsage.ClothesWasher.RatedValues.Code = "1";
                
                    if (string.IsNullOrEmpty(baseLoad.WaterUsage.ClothesWasher.RatedValues.Text))
                        baseLoad.WaterUsage.ClothesWasher.RatedValues.Text = !string.IsNullOrEmpty(baseLoad.WaterUsage.ClothesWasher.RatedValues.EnglishText) 
                            ? baseLoad.WaterUsage.ClothesWasher.RatedValues.EnglishText 
                            : "Default";
                
                    if (string.IsNullOrEmpty(baseLoad.WaterUsage.ClothesWasher.RatedValues.EnglishText))
                        baseLoad.WaterUsage.ClothesWasher.RatedValues.EnglishText = "Default";
                
                    if (string.IsNullOrEmpty(baseLoad.WaterUsage.ClothesWasher.RatedValues.FrenchText))
                        baseLoad.WaterUsage.ClothesWasher.RatedValues.FrenchText = "Par défaut";
                }
        
                if (baseLoad.WaterUsage.ClothesWasher.Temperature == null)
                {
                    baseLoad.WaterUsage.ClothesWasher.Temperature = new ClothesWasherTemperature();
                }
        
        // Ensure DishWasher has required properties
                if (baseLoad.WaterUsage.DishWasher == null)
                {
                    baseLoad.WaterUsage.DishWasher = new DishWasher();
                }
        
                if (baseLoad.WaterUsage.DishWasher.RatedValues == null)
                {
                    baseLoad.WaterUsage.DishWasher.RatedValues = new RatedValue
                    {
                        Text = "Default",
                        EnglishText = "Default",
                        FrenchText = "Par défaut",
                        Code = "1"
                    };
                }
                else
                {
            // Only ensure required fields have some value if missing
                    if (string.IsNullOrEmpty(baseLoad.WaterUsage.DishWasher.RatedValues.Code))
                        baseLoad.WaterUsage.DishWasher.RatedValues.Code = "1";
                
                    if (string.IsNullOrEmpty(baseLoad.WaterUsage.DishWasher.RatedValues.Text))
                        baseLoad.WaterUsage.DishWasher.RatedValues.Text = !string.IsNullOrEmpty(baseLoad.WaterUsage.DishWasher.RatedValues.EnglishText) 
                            ? baseLoad.WaterUsage.DishWasher.RatedValues.EnglishText 
                            : "Default";
                
                    if (string.IsNullOrEmpty(baseLoad.WaterUsage.DishWasher.RatedValues.EnglishText))
                        baseLoad.WaterUsage.DishWasher.RatedValues.EnglishText = "Default";
                
                    if (string.IsNullOrEmpty(baseLoad.WaterUsage.DishWasher.RatedValues.FrenchText))
                        baseLoad.WaterUsage.DishWasher.RatedValues.FrenchText = "Par défaut";
                }
            }
            else
            {
        // Create minimal WaterUsage if missing
                baseLoad.WaterUsage = new WaterUsage
                {
                    Id = Guid.NewGuid(),
                    BaseLoadId = baseLoad.Id,
                    BathroomFaucets = new BathroomFaucets(),
                    Shower = new Shower
                    {
                        Temperature = new ShowerTemperature(),
                        FlowRate = new ShowerFlowRate()
                    },
                    ClothesWasher = new ClothesWasher
                    {
                        RatedValues = new RatedValue
                        {
                            Text = "Default",
                            EnglishText = "Default",
                            FrenchText = "Par défaut",
                            Code = "1"
                        },
                        Temperature = new ClothesWasherTemperature()
                    },
                    DishWasher = new DishWasher
                    {
                        RatedValues = new RatedValue
                        {
                            Text = "Default",
                            EnglishText = "Default",
                            FrenchText = "Par défaut",
                            Code = "1"
                        }
                    }
                };
            }
    
    // Set up ElectricalUsage relationship
            if (baseLoad.ElectricalUsage != null)
            {
                if (baseLoad.ElectricalUsage.Id == Guid.Empty)
                {
                    baseLoad.ElectricalUsage.Id = Guid.NewGuid();
                }
                baseLoad.ElectricalUsage.BaseLoadId = baseLoad.Id;
        
        // ONLY create missing objects and relationships
        // Do NOT replace existing values with defaults
        
        // Ensure ClothesDryer has required properties
                if (baseLoad.ElectricalUsage.ClothesDryer == null)
                {
                    baseLoad.ElectricalUsage.ClothesDryer = new ClothesDryer();
                }
                else
                {
                    if (baseLoad.ElectricalUsage.ClothesDryer.Id == Guid.Empty)
                    {
                        baseLoad.ElectricalUsage.ClothesDryer.Id = Guid.NewGuid();
                    }
                }
        
        // Only create EnergySource if missing
                if (baseLoad.ElectricalUsage.ClothesDryer.EnergySource == null)
                {
                    baseLoad.ElectricalUsage.ClothesDryer.EnergySource = ApplianceEnergySources.Electric();
                }
                else if (string.IsNullOrEmpty(baseLoad.ElectricalUsage.ClothesDryer.EnergySource.Code))
                {
            // If Code is missing but EnergySource exists, use client's other properties
                    var backup = baseLoad.ElectricalUsage.ClothesDryer.EnergySource;
                    baseLoad.ElectricalUsage.ClothesDryer.EnergySource = ApplianceEnergySources.Electric();
            
            // Preserve client values if available
                    if (!string.IsNullOrEmpty(backup.EnglishText))
                        baseLoad.ElectricalUsage.ClothesDryer.EnergySource.EnglishText = backup.EnglishText;
                
                    if (!string.IsNullOrEmpty(backup.FrenchText))
                        baseLoad.ElectricalUsage.ClothesDryer.EnergySource.FrenchText = backup.FrenchText;
                
                    baseLoad.ElectricalUsage.ClothesDryer.EnergySource.IsUserSpecified = backup.IsUserSpecified;
                }
        
        // Only create RatedValue if missing
                if (baseLoad.ElectricalUsage.ClothesDryer.RatedValue == null)
                {
                    baseLoad.ElectricalUsage.ClothesDryer.RatedValue = DryerRatedConsumptions.Default();
                }
                else if (string.IsNullOrEmpty(baseLoad.ElectricalUsage.ClothesDryer.RatedValue.Code))
                {
            // If Code is missing but RatedValue exists, use client's other properties
                    var backup = baseLoad.ElectricalUsage.ClothesDryer.RatedValue;
                    baseLoad.ElectricalUsage.ClothesDryer.RatedValue = DryerRatedConsumptions.Default();
            
            // Preserve client values if available
                    if (!string.IsNullOrEmpty(backup.EnglishText))
                        baseLoad.ElectricalUsage.ClothesDryer.RatedValue.EnglishText = backup.EnglishText;
                
                    if (!string.IsNullOrEmpty(backup.FrenchText))
                        baseLoad.ElectricalUsage.ClothesDryer.RatedValue.FrenchText = backup.FrenchText;
                
                    baseLoad.ElectricalUsage.ClothesDryer.RatedValue.Value = backup.Value;
                    baseLoad.ElectricalUsage.ClothesDryer.RatedValue.IsUserSpecified = backup.IsUserSpecified;
                }
        
        // Only create Location if missing
                if (baseLoad.ElectricalUsage.ClothesDryer.Location == null)
                {
                    baseLoad.ElectricalUsage.ClothesDryer.Location = new CodeAndText("1", "Electric", "Électricité");
                }
        
        // Ensure Stove has required properties
                if (baseLoad.ElectricalUsage.Stove == null)
                {
                    baseLoad.ElectricalUsage.Stove = new Stove();
                }
                else
                {
                    if (baseLoad.ElectricalUsage.Stove.Id == Guid.Empty)
                    {
                        baseLoad.ElectricalUsage.Stove.Id = Guid.NewGuid();
                    }
                }
        
        // Only create EnergySource if missing
                if (baseLoad.ElectricalUsage.Stove.EnergySource == null)
                {
                    baseLoad.ElectricalUsage.Stove.EnergySource = ApplianceEnergySources.Electric();
                }
                else if (string.IsNullOrEmpty(baseLoad.ElectricalUsage.Stove.EnergySource.Code))
                {
            // If Code is missing but EnergySource exists, use client's other properties
                    var backup = baseLoad.ElectricalUsage.Stove.EnergySource;
                    baseLoad.ElectricalUsage.Stove.EnergySource = ApplianceEnergySources.Electric();
            
            // Preserve client values if available
                    if (!string.IsNullOrEmpty(backup.EnglishText))
                        baseLoad.ElectricalUsage.Stove.EnergySource.EnglishText = backup.EnglishText;
                
                    if (!string.IsNullOrEmpty(backup.FrenchText))
                        baseLoad.ElectricalUsage.Stove.EnergySource.FrenchText = backup.FrenchText;
                
                    baseLoad.ElectricalUsage.Stove.EnergySource.IsUserSpecified = backup.IsUserSpecified;
                }
        
        // Only create RatedValue if missing
                if (baseLoad.ElectricalUsage.Stove.RatedValue == null)
                {
                    baseLoad.ElectricalUsage.Stove.RatedValue = StoveRatedConsumptions.Default();
                }
                else if (string.IsNullOrEmpty(baseLoad.ElectricalUsage.Stove.RatedValue.Code))
                {
            // If Code is missing but RatedValue exists, use client's other properties
                    var backup = baseLoad.ElectricalUsage.Stove.RatedValue;
                    baseLoad.ElectricalUsage.Stove.RatedValue = StoveRatedConsumptions.Default();
            
            // Preserve client values if available
                    if (!string.IsNullOrEmpty(backup.EnglishText))
                        baseLoad.ElectricalUsage.Stove.RatedValue.EnglishText = backup.EnglishText;
                
                    if (!string.IsNullOrEmpty(backup.FrenchText))
                        baseLoad.ElectricalUsage.Stove.RatedValue.FrenchText = backup.FrenchText;
                
                    baseLoad.ElectricalUsage.Stove.RatedValue.Value = backup.Value;
                    baseLoad.ElectricalUsage.Stove.RatedValue.IsUserSpecified = backup.IsUserSpecified;
                }
        
        // Only create Refrigerator if missing
                if (baseLoad.ElectricalUsage.Refrigerator == null)
                {
                    baseLoad.ElectricalUsage.Refrigerator = RefrigeratorRatedConsumptions.Default();
                }
                else if (string.IsNullOrEmpty(baseLoad.ElectricalUsage.Refrigerator.Code))
                {
            // If Code is missing but Refrigerator exists, use client's other properties
                    var backup = baseLoad.ElectricalUsage.Refrigerator;
                    baseLoad.ElectricalUsage.Refrigerator = RefrigeratorRatedConsumptions.Default();
            
            // Preserve client values if available
                    if (!string.IsNullOrEmpty(backup.EnglishText))
                        baseLoad.ElectricalUsage.Refrigerator.EnglishText = backup.EnglishText;
                
                    if (!string.IsNullOrEmpty(backup.FrenchText))
                        baseLoad.ElectricalUsage.Refrigerator.FrenchText = backup.FrenchText;
                
                    baseLoad.ElectricalUsage.Refrigerator.Value = backup.Value;
                    baseLoad.ElectricalUsage.Refrigerator.IsUserSpecified = backup.IsUserSpecified;
                }
        
        // Only create InteriorLighting if missing
                if (baseLoad.ElectricalUsage.InteriorLighting == null)
                {
                    baseLoad.ElectricalUsage.InteriorLighting = InteriorLightingTypes.LessThan25Percent();
                }
                else if (string.IsNullOrEmpty(baseLoad.ElectricalUsage.InteriorLighting.Code))
                {
            // If Code is missing but InteriorLighting exists, use client's other properties
                    var backup = baseLoad.ElectricalUsage.InteriorLighting;
                    baseLoad.ElectricalUsage.InteriorLighting = InteriorLightingTypes.LessThan25Percent();
            
            // Preserve client values if available
                    if (!string.IsNullOrEmpty(backup.EnglishText))
                        baseLoad.ElectricalUsage.InteriorLighting.EnglishText = backup.EnglishText;
                
                    if (!string.IsNullOrEmpty(backup.FrenchText))
                        baseLoad.ElectricalUsage.InteriorLighting.FrenchText = backup.FrenchText;
                
                    baseLoad.ElectricalUsage.InteriorLighting.Value = backup.Value;
                    baseLoad.ElectricalUsage.InteriorLighting.IsUserSpecified = backup.IsUserSpecified;
                }
            }
            else
            {
        // Create minimal ElectricalUsage if missing
                baseLoad.ElectricalUsage = new ElectricalUsage
                {
                    Id = Guid.NewGuid(),
                    BaseLoadId = baseLoad.Id,
                    ClothesDryer = new ClothesDryer
                    {
                        Id = Guid.NewGuid(),
                        EnergySource = ApplianceEnergySources.Electric(),
                        RatedValue = DryerRatedConsumptions.Default(),
                        Location = new CodeAndText("1", "Electric", "Électricité")
                    },
                    Stove = new Stove
                    {
                        Id = Guid.NewGuid(),
                        EnergySource = ApplianceEnergySources.Electric(),
                        RatedValue = StoveRatedConsumptions.Default()
                    },
                    Refrigerator = RefrigeratorRatedConsumptions.Default(),
                    InteriorLighting = InteriorLightingTypes.LessThan25Percent()
                };
            }
    
    // Set up AdvancedUserSpecified relationship
           if (baseLoad.AdvancedUserSpecified != null)
            {
                if (baseLoad.AdvancedUserSpecified.Id == Guid.Empty)
                {
                    baseLoad.AdvancedUserSpecified.Id = Guid.NewGuid();
                }
                baseLoad.AdvancedUserSpecified.BaseLoadId = baseLoad.Id;
        
        // Only fill in missing DryerLocation
                if (baseLoad.AdvancedUserSpecified.DryerLocation == null)
                {
                    baseLoad.AdvancedUserSpecified.DryerLocation = new CodeAndText("1", "Main Floor", "Plancher Principal");
                }
        
        // Only create GasStove if missing
                if (baseLoad.AdvancedUserSpecified.GasStove == null)
                {
                    baseLoad.AdvancedUserSpecified.GasStove = ApplianceEnergySourceSpecified.NaturalGas();
                }
                else if (!string.IsNullOrEmpty(baseLoad.AdvancedUserSpecified.GasStove.Code))
                {
            // If Code is present, ensure we have the correct object but preserve client values
                    var originalValue = baseLoad.AdvancedUserSpecified.GasStove.Value;
                    var originalUserSpecified = baseLoad.AdvancedUserSpecified.GasStove.IsUserSpecified;
            
                    if (baseLoad.AdvancedUserSpecified.GasStove.Code == "2")
                    {
                        baseLoad.AdvancedUserSpecified.GasStove = ApplianceEnergySourceSpecified.NaturalGas();
                    }
                    else if (baseLoad.AdvancedUserSpecified.GasStove.Code == "4")
                    {
                        baseLoad.AdvancedUserSpecified.GasStove = ApplianceEnergySourceSpecified.Propane();
                    }
                    else
                    {
                // If code doesn't match known values, use natural gas but preserve client values
                        baseLoad.AdvancedUserSpecified.GasStove = ApplianceEnergySourceSpecified.NaturalGas();
                    }
            
            // Restore client values
                    baseLoad.AdvancedUserSpecified.GasStove.Value = originalValue;
                    baseLoad.AdvancedUserSpecified.GasStove.IsUserSpecified = originalUserSpecified;
                }
        
        // Only create GasDryer if missing
                if (baseLoad.AdvancedUserSpecified.GasDryer == null)
                {
                    baseLoad.AdvancedUserSpecified.GasDryer = ApplianceEnergySourceSpecified.Propane();
                }
                else if (!string.IsNullOrEmpty(baseLoad.AdvancedUserSpecified.GasDryer.Code))
                {
            // If Code is present, ensure we have the correct object but preserve client values
                    var originalValue = baseLoad.AdvancedUserSpecified.GasDryer.Value;
                    var originalUserSpecified = baseLoad.AdvancedUserSpecified.GasDryer.IsUserSpecified;
            
                    if (baseLoad.AdvancedUserSpecified.GasDryer.Code == "2")
                    {   
                        baseLoad.AdvancedUserSpecified.GasDryer = ApplianceEnergySourceSpecified.NaturalGas();
                    }
                    else if (baseLoad.AdvancedUserSpecified.GasDryer.Code == "4")
                    {
                        baseLoad.AdvancedUserSpecified.GasDryer = ApplianceEnergySourceSpecified.Propane();
                    }
                    else
                    {
                // If code doesn't match known values, use propane but preserve client values
                        baseLoad.AdvancedUserSpecified.GasDryer = ApplianceEnergySourceSpecified.Propane();
                    }
            
            // Restore client values
                    baseLoad.AdvancedUserSpecified.GasDryer.Value = originalValue;
                    baseLoad.AdvancedUserSpecified.GasDryer.IsUserSpecified = originalUserSpecified;
                }
            }
            else
            {
        // Create minimal AdvancedUserSpecified if missing
                baseLoad.AdvancedUserSpecified = new AdvancedUserSpecified
                {
                    Id = Guid.NewGuid(),
                    BaseLoadId = baseLoad.Id,
                    HotWaterTemperature = 55,
                    GasStove = ApplianceEnergySourceSpecified.NaturalGas(),
                    GasDryer = ApplianceEnergySourceSpecified.Propane(),
                    DryerLocation = new CodeAndText("1", "Main Floor", "Plancher Principal")
                };
            }
    
    // Log the entity after our changes but before sending to repository
            _logger.LogDebug("Modified baseLoad entity (pre-save): {Entity}");
    
    // Save to repository
            var result = await _repository.CreateBaseLoadAsync(baseLoad);
    
    // Log the final result after save
            _logger.LogDebug("Final saved baseLoad entity: {Entity}");
    
            return result;
        }
        
      
        public async Task UpdateBaseLoadAsync(BaseLoad baseLoad)
        {
            _logger.LogInformation("Updating base load with ID: {Id} from service", baseLoad.Id);
            
            // Ensure relationship IDs are set correctly
            if (baseLoad.Occupancy != null)
            {
                baseLoad.Occupancy.BaseLoadId = baseLoad.Id;
                
                // Ensure each occupant group has an ID and correct OccupancyId
                if (baseLoad.Occupancy.Adults != null)
                {
                    if (baseLoad.Occupancy.Adults.Id == Guid.Empty)
                        baseLoad.Occupancy.Adults.Id = Guid.NewGuid();
                    baseLoad.Occupancy.Adults.OccupancyId = baseLoad.Occupancy.Id;
                    baseLoad.Occupancy.Adults.OccupantType = OccupantType.Adults;
                }
                
                if (baseLoad.Occupancy.Children != null)
                {
                    if (baseLoad.Occupancy.Children.Id == Guid.Empty)
                        baseLoad.Occupancy.Children.Id = Guid.NewGuid();
                    baseLoad.Occupancy.Children.OccupancyId = baseLoad.Occupancy.Id;
                    baseLoad.Occupancy.Children.OccupantType = OccupantType.Children;
                }
                
                if (baseLoad.Occupancy.Infants != null)
                {
                    if (baseLoad.Occupancy.Infants.Id == Guid.Empty)
                        baseLoad.Occupancy.Infants.Id = Guid.NewGuid();
                    baseLoad.Occupancy.Infants.OccupancyId = baseLoad.Occupancy.Id;
                    baseLoad.Occupancy.Infants.OccupantType = OccupantType.Infants;
                }
            }
                
            if (baseLoad.Summary != null)
                baseLoad.Summary.BaseLoadId = baseLoad.Id;
                
            if (baseLoad.WaterUsage != null)
            {
                baseLoad.WaterUsage.BaseLoadId = baseLoad.Id;
                
                // Handle Shower and its nested objects
                if (baseLoad.WaterUsage.Shower == null)
                    baseLoad.WaterUsage.Shower = new Shower();
                else
                {
                    // Initialize ShowerTemperature if it's null
                    if (baseLoad.WaterUsage.Shower.Temperature == null)
                        baseLoad.WaterUsage.Shower.Temperature = new ShowerTemperature();
                    
                    // Initialize ShowerFlowRate if it's null
                    if (baseLoad.WaterUsage.Shower.FlowRate == null)
                        baseLoad.WaterUsage.Shower.FlowRate = new ShowerFlowRate();
                }
                
                // Initialize BathroomFaucets if it's null
                if (baseLoad.WaterUsage.BathroomFaucets == null)
                    baseLoad.WaterUsage.BathroomFaucets = new BathroomFaucets();
                
                // Handle ClothesWasher
                if (baseLoad.WaterUsage.ClothesWasher == null)
                    baseLoad.WaterUsage.ClothesWasher = new ClothesWasher();
                else
                {
                    if (baseLoad.WaterUsage.ClothesWasher.RatedValues == null)
                        baseLoad.WaterUsage.ClothesWasher.RatedValues = new RatedValue();
                    
                    if (baseLoad.WaterUsage.ClothesWasher.Temperature == null)
                        baseLoad.WaterUsage.ClothesWasher.Temperature = new ClothesWasherTemperature();
                }
                
                // Handle DishWasher
                if (baseLoad.WaterUsage.DishWasher == null)
                    baseLoad.WaterUsage.DishWasher = new DishWasher();
                else
                {
                    if (baseLoad.WaterUsage.DishWasher.RatedValues == null)
                        baseLoad.WaterUsage.DishWasher.RatedValues = new RatedValue();
                }
            }
                
            if (baseLoad.ElectricalUsage != null)
            {
                baseLoad.ElectricalUsage.BaseLoadId = baseLoad.Id;
                
                // Handle ClothesDryer and its nested objects
                if (baseLoad.ElectricalUsage.ClothesDryer == null)
                    baseLoad.ElectricalUsage.ClothesDryer = new ClothesDryer();
                else
                {
                    if (baseLoad.ElectricalUsage.ClothesDryer.Id == Guid.Empty)
                        baseLoad.ElectricalUsage.ClothesDryer.Id = Guid.NewGuid();
            
                    // Initialize EnergySource if it's null
                    if (baseLoad.ElectricalUsage.ClothesDryer.EnergySource == null)
                        baseLoad.ElectricalUsage.ClothesDryer.EnergySource = ApplianceEnergySources.Electric();
                    
                    // Initialize RatedValue if it's null
                    if (baseLoad.ElectricalUsage.ClothesDryer.RatedValue == null)
                        baseLoad.ElectricalUsage.ClothesDryer.RatedValue = DryerRatedConsumptions.Default();
                    
                    // Initialize Location if it's null
                    if (baseLoad.ElectricalUsage.ClothesDryer.Location == null)
                        baseLoad.ElectricalUsage.ClothesDryer.Location = new CodeAndText("1", "Electric", "Électricité");
                }
                
                // Handle Stove and its nested objects
                if (baseLoad.ElectricalUsage.Stove == null)
                    baseLoad.ElectricalUsage.Stove = new Stove();
                else
                {
                    if (baseLoad.ElectricalUsage.Stove.Id == Guid.Empty)
                        baseLoad.ElectricalUsage.Stove.Id = Guid.NewGuid();

                    // Initialize EnergySource if it's null
                    if (baseLoad.ElectricalUsage.Stove.EnergySource == null)
                        baseLoad.ElectricalUsage.Stove.EnergySource = ApplianceEnergySources.Electric();
                    
                    // Initialize RatedValue if it's null
                    if (baseLoad.ElectricalUsage.Stove.RatedValue == null)
                        baseLoad.ElectricalUsage.Stove.RatedValue = StoveRatedConsumptions.Default();
                }
                
                // Initialize Refrigerator if it's null
                if (baseLoad.ElectricalUsage.Refrigerator == null)
                    baseLoad.ElectricalUsage.Refrigerator = RefrigeratorRatedConsumptions.Default();
                
                // Initialize InteriorLighting if it's null
                if (baseLoad.ElectricalUsage.InteriorLighting == null)
                    baseLoad.ElectricalUsage.InteriorLighting = InteriorLightingTypes.LessThan25Percent();
            }
                
            if (baseLoad.AdvancedUserSpecified != null)
            {
                baseLoad.AdvancedUserSpecified.BaseLoadId = baseLoad.Id;
                
                // Ensure DryerLocation has default values if null
                if (baseLoad.AdvancedUserSpecified.DryerLocation == null)
                {
                    baseLoad.AdvancedUserSpecified.DryerLocation = new CodeAndText("1", "Main Floor", "Plancher Principal");
                }
                
                // Handle GasStove if it has a code but is not properly initialized
                if (baseLoad.AdvancedUserSpecified.GasStove != null && 
                    !string.IsNullOrEmpty(baseLoad.AdvancedUserSpecified.GasStove.Code))
                {
                    // Get the correct predefined instance based on code
                    if (baseLoad.AdvancedUserSpecified.GasStove.Code == "2")
                    {
                        var gasStove = ApplianceEnergySourceSpecified.NaturalGas();
                        gasStove.Value = baseLoad.AdvancedUserSpecified.GasStove.Value;
                        baseLoad.AdvancedUserSpecified.GasStove = gasStove;
                    }
                    else if (baseLoad.AdvancedUserSpecified.GasStove.Code == "4")
                    {
                        var gasStove = ApplianceEnergySourceSpecified.Propane();
                        gasStove.Value = baseLoad.AdvancedUserSpecified.GasStove.Value;
                        baseLoad.AdvancedUserSpecified.GasStove = gasStove;
                    }
                }
                
                // Handle GasDryer if it has a code but is not properly initialized
                if (baseLoad.AdvancedUserSpecified.GasDryer != null && 
                    !string.IsNullOrEmpty(baseLoad.AdvancedUserSpecified.GasDryer.Code))
                {
                    // Get the correct predefined instance based on code
                    if (baseLoad.AdvancedUserSpecified.GasDryer.Code == "2")
                    {
                        var gasDryer = ApplianceEnergySourceSpecified.NaturalGas();
                        gasDryer.Value = baseLoad.AdvancedUserSpecified.GasDryer.Value;
                        baseLoad.AdvancedUserSpecified.GasDryer = gasDryer;
                    }
                    else if (baseLoad.AdvancedUserSpecified.GasDryer.Code == "4")
                    {
                        var gasDryer = ApplianceEnergySourceSpecified.Propane();
                        gasDryer.Value = baseLoad.AdvancedUserSpecified.GasDryer.Value;
                        baseLoad.AdvancedUserSpecified.GasDryer = gasDryer;
                    }
                }
            }
            
            await _repository.UpdateBaseLoadAsync(baseLoad);
        }
        
        public async Task DeleteBaseLoadAsync(Guid id)
        {
            _logger.LogInformation("Deleting base load with ID: {Id} from service", id);
            await _repository.DeleteBaseLoadAsync(id);
        }

        public async Task<BaseLoad?> GetBaseLoadByEnergyUpgradeIdAsync(Guid energyUpgradeId)
        {
            _logger.LogInformation("Getting base load for energy upgrade ID: {EnergyUpgradeId} from service", energyUpgradeId);
            return await _repository.GetBaseLoadByEnergyUpgradeIdAsync(energyUpgradeId);
        }

        public async Task<BaseLoad> DuplicateBaseLoadForEnergyUpgradeAsync(BaseLoad baseBaseLoad, Guid energyUpgradeId)
        {
            _logger.LogInformation("Duplicating base load {BaseLoadId} for energy upgrade {EnergyUpgradeId} from service",
                baseBaseLoad.Id, energyUpgradeId);
            // TODO: Add validation and business logic
            return await _repository.DuplicateBaseLoadForEnergyUpgradeAsync(baseBaseLoad, energyUpgradeId);
        }
        /// <summary>
        /// Calculates baseloads similar to CalculateBaseloads in original C++ code
        /// </summary>
        // 
        public async Task<BaseLoad> CalculateBaseLoadsAsync(BaseLoad baseLoad, bool restoreDefaults = false)
        {
            _logger.LogInformation("Calculating baseloads for house ID: {HouseId}", baseLoad.HouseId);

            // if (baseLoad.Summary.IsSpecified)
            //     return baseLoad; // Similar to m_specifiedElectricalWaterUsage check

            if (restoreDefaults)
            {
                await SetDefaultBaseloadsAsync(baseLoad);
            }
            
    
    // Get the total number of occupants
            int nOccupants = 0;
            decimal clothesWasherUsage = 0;
    
            if (baseLoad.Occupancy != null)
            {
                if (baseLoad.Occupancy.Adults != null)
                    nOccupants += baseLoad.Occupancy.Adults.Occupants;
                if (baseLoad.Occupancy.Children != null)
                    nOccupants += baseLoad.Occupancy.Children.Occupants;
                if (baseLoad.Occupancy.Infants != null)
                    nOccupants += baseLoad.Occupancy.Infants.Occupants;
            }
    
    // Reset calculated values
            decimal waterLoad = 0;
            decimal electAppl = 0;
            decimal electLight = 0;

            _logger.LogInformation("Check: baseLoad.WaterUsage is {WaterUsageCheck}, baseLoad.WaterUsage?.BathroomFaucets is {BathroomFaucetsCheck}",
                baseLoad.WaterUsage != null ? "not null" : "null",
                baseLoad.WaterUsage?.BathroomFaucets != null ? "not null" : "null");

    // 1: Calculate volume of hot water from bathroom faucets
            if (baseLoad.WaterUsage?.BathroomFaucets != null)
            {

                _logger.LogInformation("Condition TRUE: baseLoad.WaterUsage?.BathroomFaucets != null");
        // Similar to: m_faucet_flow_rate_val = GetAssociatedFloat(ID_LIST_BSLD_FAUCET_FLOWRATE_DATA,ID_LIST_BSLD_FAUCET_FLOWRATE_VALUE, m_faucet_flow_rate_index);
                string faucetFlowRateIndex = baseLoad.WaterUsage.BathroomFaucets.Code ?? "2"; // Default to code 2 if null
                _logger.LogInformation("Looking up faucet flow rate with index: {Index}", faucetFlowRateIndex);

                decimal faucetFlowRateVal = await _lookupService.GetAssociatedFloatAsync(
                    "ID_LIST_BSLD_FAUCET_FLOWRATE_DATA", 
                    "ID_LIST_BSLD_FAUCET_FLOWRATE_VALUE", 
                    faucetFlowRateIndex);

                _logger.LogInformation("Faucet flow rate lookup returned: {Value} L/min", faucetFlowRateVal);

        // Store the looked-up value
                baseLoad.WaterUsage.BathroomFaucets.Value = faucetFlowRateVal;

        // Calculate faucet water usage
                waterLoad += faucetFlowRateVal * 
                            baseLoad.WaterUsage.BathroomFaucets.NumberPerOccupantPerDay * 
                            nOccupants;
                _logger.LogInformation("waterLoad--1 {Value} liters", waterLoad);

            }

    // 2: Calculate shower hot water usage
            if (baseLoad.WaterUsage?.Shower != null)
            {
        // Get shower flow rate value
                string showerFlowRateIndex = baseLoad.WaterUsage.Shower.FlowRate?.Code ?? "2"; // Default to code 2 if null
                decimal showerFlowRateVal = await _lookupService.GetAssociatedFloatAsync(
                    "ID_LIST_BSLD_SHOWER_FLOWRATE_DATA", 
                    "ID_LIST_BSLD_SHOWER_FLOWRATE_VALUE", 
                    showerFlowRateIndex);

        // Store the looked-up value
                baseLoad.WaterUsage.Shower.FlowRate.Value = showerFlowRateVal;

        // Get shower temperature value
                string showerTemperatureIndex = baseLoad.WaterUsage.Shower.Temperature?.Code ?? "1"; // Default to code 1 if null
                decimal showerTemperatureVal = await _lookupService.GetAssociatedFloatAsync(
                    "ID_LIST_BSLD_SHOWER_TEMP_DATA", 
                    "ID_LIST_BSLD_SHOWER_TEMP_VALUE", 
                    showerTemperatureIndex);

        // Store the looked-up value
                baseLoad.WaterUsage.Shower.Temperature.Value = showerTemperatureVal;

        // Calculate daily shower time (similar to m_shower_total_time calculation)
                decimal showerTotalTime = baseLoad.WaterUsage.Shower.AverageDuration * 
                                        baseLoad.WaterUsage.Shower.NumberPerOccupantPerWeek * 
                                        nOccupants / 7.0m;

        // Store the calculated shower time
                baseLoad.WaterUsage.Shower.TotalDurationPerDay = showerTotalTime;

        // Simplified cold water temperature calculation
                // decimal avgColdWaterInletTemp = 10.0m; // Simplified average value in °C 
                // Use:
_logger.LogInformation("About to call simulation service for house ID: {HouseId}", baseLoad.HouseId);
decimal avgColdWaterInletTemp = await _simulationClient.GetAverageColdWaterTemperatureAsync(baseLoad.HouseId);
_logger.LogInformation("Using calculated cold water temperature: {Temp}°C", avgColdWaterInletTemp);        // Calculate shower hot water usage with temperature adjustment
                decimal waterTemp = baseLoad.AdvancedUserSpecified?.HotWaterTemperature ?? 55.0m;
                waterLoad += (showerFlowRateVal * showerTotalTime) * 
                            ((showerTemperatureVal - avgColdWaterInletTemp) / 
                            (waterTemp - avgColdWaterInletTemp));

                _logger.LogInformation("waterLoad--2 {Value} liters", waterLoad);

            }

    // 3: Calculate clothes washer water and electrical usage
            if (baseLoad.WaterUsage?.ClothesWasher != null)
            {
        // Get clothes washer rated values if not user specified
                if (baseLoad.WaterUsage.ClothesWasher.RatedValues?.Code != "0") // 0 is user specified
                {
                    string clothesWasherValueIndex = baseLoad.WaterUsage.ClothesWasher.RatedValues?.Code ?? "1"; // Default to code 1 if null
            
            // Get electrical consumption value
                    decimal clothesWasherAnnEnergyConsumption = await _lookupService.GetAssociatedFloatAsync(
                        "ID_LIST_BSLD_CLOTHESW_RATED_DATA", 
                        "ID_LIST_BSLD_CLOTHESW_RATED_VALUE_ELEC", 
                        clothesWasherValueIndex);

                    _logger.LogInformation("Clothes washer annual energy consumption for index {Index}: {Value} kWh", 
                        clothesWasherValueIndex, clothesWasherAnnEnergyConsumption);
            
            // Store the looked-up value
                    baseLoad.WaterUsage.ClothesWasher.RatedValues.RatedAnnualEnergyConsumption = clothesWasherAnnEnergyConsumption;
            
            // Get water consumption value
                    decimal clothesWasherWaterPerCycle = await _lookupService.GetAssociatedFloatAsync(
                        "ID_LIST_BSLD_CLOTHESW_RATED_DATA", 
                        "ID_LIST_BSLD_CLOTHESW_RATED_VALUE_WATER", 
                        clothesWasherValueIndex);

                    _logger.LogInformation("Clothes washer water consumption per cycle for index {Index}: {Value} liters", 
                        clothesWasherValueIndex, clothesWasherWaterPerCycle);
            
            // Store the looked-up value
                    baseLoad.WaterUsage.ClothesWasher.RatedValues.RatedWaterConsumptionPerCycle = clothesWasherWaterPerCycle;
                }

        // Calculate clothes washer usage rate (cycles per day)
                clothesWasherUsage = baseLoad.WaterUsage.ClothesWasher.NumberPerOccupantPerWeek * nOccupants / 7.0m;

        // Only add hot water usage if on hot temperature setting (like original C++ code)
                if (baseLoad.WaterUsage.ClothesWasher.Temperature?.Code == "0") // Hot/Cold
                {
                    waterLoad += baseLoad.WaterUsage.ClothesWasher.RatedValues.RatedWaterConsumptionPerCycle * 
                                clothesWasherUsage;
                    _logger.LogInformation("waterLoad--3 {Value} liters", waterLoad);
            
                }

        // Update associated electrical consumption - normalized by 392 test cycles per year
                electAppl += (baseLoad.WaterUsage.ClothesWasher.RatedValues.RatedAnnualEnergyConsumption / 392) * 
                            clothesWasherUsage;
            }

    // 4: Calculate dishwasher water and electrical usage
            if (baseLoad.WaterUsage?.DishWasher != null)
            {   
        // Get dishwasher rated values if not user specified
                if (baseLoad.WaterUsage.DishWasher.RatedValues?.Code != "0") // 0 is user specified
                {
                    string dishWasherValueIndex = baseLoad.WaterUsage.DishWasher.RatedValues?.Code ?? "1"; // Default to code 1 if null
            
            // Get electrical consumption value
                    decimal dishWasherAnnEnergyConsumption = await _lookupService.GetAssociatedFloatAsync(
                        "ID_LIST_BSLD_DISHW_RATED_DATA", 
                        "ID_LIST_BSLD_DISHW_RATED_VALUE_ELEC", 
                        dishWasherValueIndex);
            
            // Store the looked-up value
                    baseLoad.WaterUsage.DishWasher.RatedValues.RatedAnnualEnergyConsumption = dishWasherAnnEnergyConsumption;
            
            // Get water consumption value
                    decimal dishWasherWaterPerCycle = await _lookupService.GetAssociatedFloatAsync(
                        "ID_LIST_BSLD_DISHW_RATED_DATA", 
                        "ID_LIST_BSLD_DISHW_RATED_VALUE_WATER", 
                        dishWasherValueIndex);
                _logger.LogInformation("dishWasherWaterPerCycle------- {Value} liters", dishWasherWaterPerCycle);
                    
            
            // Store the looked-up value
                    baseLoad.WaterUsage.DishWasher.RatedValues.RatedWaterConsumptionPerCycle = dishWasherWaterPerCycle;
                }

        // Calculate dishwasher usage and water consumption
                decimal dishwasherCyclesPerDay = baseLoad.WaterUsage.DishWasher.NumberPerOccupantPerWeek * nOccupants / 7.0m;
        
        // Add hot water usage - dishwashers use nearly 100% hot water
                waterLoad += baseLoad.WaterUsage.DishWasher.RatedValues.RatedWaterConsumptionPerCycle * 
                            dishwasherCyclesPerDay;
                _logger.LogInformation("waterLoad--4 {Value} liters", waterLoad);

        
        // Update associated electrical consumption - normalized by 215 test cycles per year
                electAppl += (baseLoad.WaterUsage.DishWasher.RatedValues.RatedAnnualEnergyConsumption / 215) * 
                            dishwasherCyclesPerDay;
            }

    // 5: Add other hot water usage
            if (baseLoad.WaterUsage != null)
            {
                waterLoad += baseLoad.WaterUsage.OtherHotWaterUse * nOccupants;
                _logger.LogInformation("waterLoad--5 {Value} liters", waterLoad);
            }

    // 6: Calculate clothes dryer electrical usage
            if (baseLoad.ElectricalUsage?.ClothesDryer != null && baseLoad.ElectricalUsage.ClothesDryer.Installed)
            {
        // Get dryer rated value if not user specified
                if (baseLoad.ElectricalUsage.ClothesDryer.RatedValue?.Code != "0") // 0 is user specified
                {
                    string dryerValueIndex = baseLoad.ElectricalUsage.ClothesDryer.RatedValue?.Code ?? "1"; // Default to code 1 if null
            
            // Get energy consumption value
                    decimal dryerAnnEnergyConsumption = await _lookupService.GetAssociatedFloatAsync(
                        "ID_LIST_BSLD_DRYER_RATED_DATA", 
                        "ID_LIST_BSLD_DRYER_RATED_VALUE", 
                        dryerValueIndex);

                    _logger.LogInformation("dryerAnnEnergyConsumption------- {Value} liters", dryerAnnEnergyConsumption);
            
            // Store the looked-up value
                    baseLoad.ElectricalUsage.ClothesDryer.RatedValue.Value = dryerAnnEnergyConsumption;
                }

        // Calculate dryer electrical usage - only for electric dryers
                if (baseLoad.ElectricalUsage.ClothesDryer.EnergySource?.Code == "1") // Electric
                {
            // Normalize by 283 test cycles per year and adjust for percentage of washer loads dried
                    electAppl += (baseLoad.ElectricalUsage.ClothesDryer.RatedValue.Value / 283) * 
                                clothesWasherUsage * 
                                (baseLoad.ElectricalUsage.ClothesDryer.PercentageOfWasherLoads / 100m);
                }
            }

    // 7: Calculate stove electrical usage
            if (baseLoad.ElectricalUsage?.Stove != null)
            {
        // Get stove rated value if not user specified
                if (baseLoad.ElectricalUsage.Stove.RatedValue?.Code != "0") // 0 is user specified
                {
                    string stoveValueIndex = baseLoad.ElectricalUsage.Stove.RatedValue?.Code ?? "1"; // Default to code 1 if null
            
            // Get energy consumption value
                    decimal stoveAnnEnergyConsumption = await _lookupService.GetAssociatedFloatAsync(
                        "ID_LIST_BSLD_STOVE_RATED_DATA", 
                        "ID_LIST_BSLD_STOVE_RATED_VALUE", 
                        stoveValueIndex);

                _logger.LogInformation("stoveAnnEnergyConsumption------- {Value} liters", stoveAnnEnergyConsumption);

            
            // Store the looked-up value
                    baseLoad.ElectricalUsage.Stove.RatedValue.Value = stoveAnnEnergyConsumption;
                }

        // Calculate stove electrical usage - only for electric stoves
                if (baseLoad.ElectricalUsage.Stove.EnergySource?.Code == "1") // Electric
                {
            // Normalize to 3 occupants, convert to daily rate, and scale to actual occupants
                    electAppl += (baseLoad.ElectricalUsage.Stove.RatedValue.Value / 3.0m / 365.0m) * 
                          nOccupants;
                }
            }

    // 8: Calculate refrigerator electrical usage
            if (baseLoad.ElectricalUsage?.Refrigerator != null)
            {
        // Get refrigerator rated value if not user specified
                if (baseLoad.ElectricalUsage.Refrigerator.Code != "0") // 0 is user specified
                {
                    string refrigValueIndex = baseLoad.ElectricalUsage.Refrigerator.Code ?? "1"; // Default to code 1 if null
            
            // Get energy consumption value
                    decimal refrigAnnEnergyConsumption = await _lookupService.GetAssociatedFloatAsync(
                        "ID_LIST_BSLD_REFRIG_RATED_DATA", 
                        "ID_LIST_BSLD_REFRIG_RATED_VALUE", 
                        refrigValueIndex);
                                    
                                    
                _logger.LogInformation("refrigAnnEnergyConsumption------- {Value} liters", refrigAnnEnergyConsumption);

            
            // Store the looked-up value
                    baseLoad.ElectricalUsage.Refrigerator.Value = refrigAnnEnergyConsumption;
                }

        // Convert annual to daily usage (not scaled by occupants)
                electAppl += baseLoad.ElectricalUsage.Refrigerator.Value / 365.0m;
            }

    // 9: Calculate interior lighting electrical usage
            if (baseLoad.ElectricalUsage?.InteriorLighting != null)
            {
        // Get interior lighting value if not user specified
                if (baseLoad.ElectricalUsage.InteriorLighting.Code != "0") // 0 is user specified
                {
                    string lightingValueIndex = baseLoad.ElectricalUsage.InteriorLighting.Code ?? "1"; // Default to code 1 if null
            
            // Get energy consumption value
                    decimal lightingDailyEnergyConsumption = await _lookupService.GetAssociatedFloatAsync(
                        "ID_LIST_BSLD_INT_LIGHTING_DATA", 
                        "ID_LIST_BSLD_INT_LIGHTING_VALUE", 
                        lightingValueIndex);
            
            // Store the looked-up value
                    baseLoad.ElectricalUsage.InteriorLighting.Value = lightingDailyEnergyConsumption;
                }

        // Add lighting energy
                electLight += baseLoad.ElectricalUsage.InteriorLighting.Value;
            }

    // 10: Set summary values
            baseLoad.Summary.HotWaterLoad = Math.Round(waterLoad, 2);
            baseLoad.Summary.ElectricalAppliances = Math.Round(electAppl, 2);
            baseLoad.Summary.Lighting = Math.Round(electLight, 2);
            baseLoad.Summary.OtherElectric = Math.Round(baseLoad.ElectricalUsage?.OtherLoad ?? 0m, 2);
            baseLoad.Summary.ExteriorUse = Math.Round(baseLoad.ElectricalUsage?.AverageExteriorUse ?? 0m, 2);
            baseLoad.Summary.IsSpecified = true;
    // Update in database
            await UpdateBaseLoadAsync(baseLoad);
    
            return baseLoad;
        }



        
        /// <summary>
        /// Sets default baseloads similar to SetDefaultBaseloads in original C++ code
        /// </summary>
        public async Task<BaseLoad> SetDefaultBaseloadsAsync(BaseLoad baseLoad, bool pageOnly = false, bool skipCalculateBaseloads = false)
        {
            _logger.LogInformation("Setting default baseloads for house ID: {HouseId}", baseLoad.HouseId);
            
            // Initialize model structure if needed
            // EnsureBaseLoadStructure(baseLoad);
            
            // Set default occupancy
            baseLoad.Occupancy.IsHouseOccupied = true;
            baseLoad.Occupancy.Adults.Occupants = 2;
            baseLoad.Occupancy.Adults.AtHome = 50;
            baseLoad.Occupancy.Children.Occupants = 1;
            baseLoad.Occupancy.Children.AtHome = 50;
            baseLoad.Occupancy.Infants.Occupants = 0;
            baseLoad.Occupancy.Infants.AtHome = 0;
            

            
            // Set default water usage
            baseLoad.WaterUsage.SetDefaults();
            
            // Set default electrical usage
            baseLoad.ElectricalUsage.SetDefaults();
            
            // Set default advanced settings
            baseLoad.AdvancedUserSpecified.SetDefaults();
            
            // Set internal gains fraction
            baseLoad.BasementFractionOfInternalGains = 0.15m; // 30% of internal gains to basement
            baseLoad.WaterUsage.Shower.TotalDurationPerDay = 0.00m;
            baseLoad.WaterUsage.ClothesWasher.RatedValues.RatedWaterConsumptionPerCycle = 0.0m;
            baseLoad.WaterUsage.ClothesWasher.RatedValues.RatedAnnualEnergyConsumption = 0.00m;
            baseLoad.WaterUsage.DishWasher.RatedValues.RatedWaterConsumptionPerCycle = 0.0m;
            baseLoad.WaterUsage.DishWasher.RatedValues.RatedAnnualEnergyConsumption = 0.00m;
            baseLoad.Summary.IsSpecified = false;
            
            // Set default summary values
            // baseLoad.Summary.SetDefaults();
            
            // Update the database with default values (if not page-only)
            if (!pageOnly)
            {
                await UpdateBaseLoadAsync(baseLoad);
                
                // Calculate baseloads with the default values
                if (!skipCalculateBaseloads)
                {
                    await CalculateBaseLoadsAsync(baseLoad, false);
                }
            }
            
            return baseLoad;
        }
        

        
        /// <summary>
        /// Sets default values for a multi-unit residential building (MURB)
        /// </summary>
        public async Task<BaseLoad> SetDefaultsForMURBAsync(BaseLoad baseLoad, int unitCount = 1)
        {
            _logger.LogInformation("Setting MURB defaults for house ID: {HouseId}", baseLoad.HouseId);
            
            // First set standard defaults
            await SetDefaultBaseloadsAsync(baseLoad, true, true);
            
            // Calculate scaling factor based on unit count
            decimal scalingFactor = (decimal)Math.Sqrt(unitCount);
            
            // Modify occupancy for multi-unit building
            baseLoad.Occupancy.Adults.Occupants = 1;
            baseLoad.Occupancy.Adults.AtHome = 60m; // 60% time at home (more than single family)
            baseLoad.Occupancy.Children.Occupants = 0;
            baseLoad.Occupancy.Children.AtHome = 0m;
            baseLoad.Occupancy.Infants.Occupants = 0;
            baseLoad.Occupancy.Infants.AtHome = 0m;
            
            // Scale down per-unit hot water usage for multi-unit buildings
            if (baseLoad.WaterUsage != null)
            {
                // Reduce bathroom faucet usage by 20%
                if (baseLoad.WaterUsage.BathroomFaucets != null)
                {
                    baseLoad.WaterUsage.BathroomFaucets.NumberPerOccupantPerDay *= 0.8m;
                }
                
                // Reduce shower usage by 10%
                if (baseLoad.WaterUsage.Shower != null)
                {
                    baseLoad.WaterUsage.Shower.NumberPerOccupantPerWeek *= 0.9m;
                }
                
                // Reduce clothes washer usage by 15%
                if (baseLoad.WaterUsage.ClothesWasher != null)
                {
                    baseLoad.WaterUsage.ClothesWasher.NumberPerOccupantPerWeek *= 0.85m;
                }
                
                // Reduce dish washer usage by 15%
                if (baseLoad.WaterUsage.DishWasher != null)
                {
                    baseLoad.WaterUsage.DishWasher.NumberPerOccupantPerWeek *= 0.85m;
                }
                
                // Reduce other hot water usage by 20%
                baseLoad.WaterUsage.OtherHotWaterUse *= 0.8m;
            }
            
            // Scale down per-unit electrical usage for multi-unit buildings
            if (baseLoad.ElectricalUsage != null)
            {
                // Reduce dryer usage by 15%
                if (baseLoad.ElectricalUsage.ClothesDryer != null)
                {
                    baseLoad.ElectricalUsage.ClothesDryer.PercentageOfWasherLoads *= 0.85m;
                }
                
                // Reduce other electrical consumption by 10%
                baseLoad.ElectricalUsage.OtherLoad *= 0.9m;
                
                // Exterior lighting gets a major reduction as it's often shared
                baseLoad.ElectricalUsage.AverageExteriorUse *= 0.5m / scalingFactor;
            }
            
            // Set zero internal gains fraction to basement for multi-unit buildings (no basement)
            baseLoad.BasementFractionOfInternalGains = 0m;
            
            // Update the database with the MURB defaults
            await UpdateBaseLoadAsync(baseLoad);
            
            // Calculate baseloads with the MURB defaults
            return await CalculateBaseLoadsAsync(baseLoad, false);
        }
        
        /// <summary>
        /// Converts legacy gas appliance settings to new format
        /// </summary>
        public async Task<BaseLoad> ConvertLegacyGasAppliancesAsync(BaseLoad baseLoad)
        {
            _logger.LogInformation("Converting legacy gas appliances for house ID: {HouseId}", baseLoad.HouseId);
            
            bool updated = false;
            
            // Check for legacy gas stove data
            if (baseLoad.AdvancedUserSpecified?.GasStove != null && 
                baseLoad.ElectricalUsage?.Stove != null)
            {
                // If gas stove has a value and is specified, update the electrical stove settings
                if (baseLoad.AdvancedUserSpecified.GasStove.Value > 0 && 
                    baseLoad.AdvancedUserSpecified.GasStove.IsUserSpecified == true)
                {
                    // Set stove energy source to gas (natural gas or propane)
                    if (baseLoad.AdvancedUserSpecified.GasStove.Code == "2") // Natural gas
                    {
                        baseLoad.ElectricalUsage.Stove.EnergySource.Code = "2";
                        baseLoad.ElectricalUsage.Stove.EnergySource.EnglishText = "Natural Gas";
                        baseLoad.ElectricalUsage.Stove.EnergySource.FrenchText = "Gaz naturel";
                    }
                    else if (baseLoad.AdvancedUserSpecified.GasStove.Code == "4") // Propane
                    {
                        baseLoad.ElectricalUsage.Stove.EnergySource.Code = "4";
                        baseLoad.ElectricalUsage.Stove.EnergySource.EnglishText = "Propane";
                        baseLoad.ElectricalUsage.Stove.EnergySource.FrenchText = "Propane";
                    }
                    
                    updated = true;
                }
            }
            
            // Check for legacy gas dryer data
            if (baseLoad.AdvancedUserSpecified?.GasDryer != null && 
                baseLoad.ElectricalUsage?.ClothesDryer != null)
            {
                // If gas dryer has a value and is specified, update the electrical dryer settings
                if (baseLoad.AdvancedUserSpecified.GasDryer.Value > 0 && 
                    baseLoad.AdvancedUserSpecified.GasDryer.IsUserSpecified == true)
                {
                    // Set dryer energy source to gas (natural gas or propane)
                    if (baseLoad.AdvancedUserSpecified.GasDryer.Code == "2") // Natural gas
                    {
                        baseLoad.ElectricalUsage.ClothesDryer.EnergySource.Code = "2";
                        baseLoad.ElectricalUsage.ClothesDryer.EnergySource.EnglishText = "Natural Gas";
                        baseLoad.ElectricalUsage.ClothesDryer.EnergySource.FrenchText = "Gaz naturel";
                    }
                    else if (baseLoad.AdvancedUserSpecified.GasDryer.Code == "4") // Propane
                    {
                        baseLoad.ElectricalUsage.ClothesDryer.EnergySource.Code = "4";
                        baseLoad.ElectricalUsage.ClothesDryer.EnergySource.EnglishText = "Propane";
                        baseLoad.ElectricalUsage.ClothesDryer.EnergySource.FrenchText = "Propane";
                    }
                    
                    updated = true;
                }
            }
            
            // Update the database if changes were made
            if (updated)
            {
                await UpdateBaseLoadAsync(baseLoad);
                
                // Recalculate baseloads with the updated appliance settings
                return await CalculateBaseLoadsAsync(baseLoad, false);
            }
            
            return baseLoad;
        }
    }
}