FROM mcr.microsoft.com/dotnet/aspnet:8.0 AS base
WORKDIR /app
EXPOSE 80
EXPOSE 443
# Install curl for diagnostic and testing purposes
RUN apt-get update && apt-get install -y curl && rm -rf /var/lib/apt/lists/*

FROM mcr.microsoft.com/dotnet/sdk:8.0 AS build
WORKDIR /src
COPY ["src/HouseFileService.API/HouseFileService.API.csproj", "src/HouseFileService.API/"]
COPY ["src/HouseFileService.Core/HouseFileService.Core.csproj", "src/HouseFileService.Core/"]
COPY ["src/HouseFileService.Infrastructure/HouseFileService.Infrastructure.csproj", "src/HouseFileService.Infrastructure/"]

# Restore dependencies
RUN dotnet restore "src/HouseFileService.API/HouseFileService.API.csproj"

# Add required packages for migrations
RUN dotnet add "src/HouseFileService.Infrastructure/HouseFileService.Infrastructure.csproj" package Microsoft.EntityFrameworkCore.Design --version 8.0.15
RUN dotnet add "src/HouseFileService.Infrastructure/HouseFileService.Infrastructure.csproj" package Microsoft.EntityFrameworkCore.Tools --version 8.0.15

# Install EF Core tools
RUN dotnet tool install --global dotnet-ef
ENV PATH="${PATH}:/root/.dotnet/tools"

# Copy the rest of the source code
COPY . .

# Create migrations directory if it doesn't exist
RUN mkdir -p src/HouseFileService.Infrastructure/Data/Migrations

# Build the solution first to ensure everything compiles
WORKDIR "/src"
RUN dotnet build "src/HouseFileService.API/HouseFileService.API.csproj" -c Release

# Create HouseFileDbContext migration
WORKDIR "/src"
RUN dotnet ef migrations add InitialHouseFile \
    --project src/HouseFileService.Infrastructure \
    --startup-project src/HouseFileService.API \
    --context HouseFileDbContext \
    --output-dir Data/Migrations/HouseFile \
    || echo "Migration creation skipped - will use EnsureCreated instead"

# Build the application
WORKDIR "/src/src/HouseFileService.API"
RUN dotnet build "HouseFileService.API.csproj" -c Release -o /app/build

FROM build AS publish
RUN dotnet publish "HouseFileService.API.csproj" -c Release -o /app/publish

FROM base AS final
WORKDIR /app
COPY --from=publish /app/publish .

ENTRYPOINT ["dotnet", "HouseFileService.API.dll"]