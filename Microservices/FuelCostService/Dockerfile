FROM mcr.microsoft.com/dotnet/aspnet:8.0 AS base
WORKDIR /app
EXPOSE 80
EXPOSE 443
RUN apt-get update && apt-get install -y curl && rm -rf /var/lib/apt/lists/*

FROM mcr.microsoft.com/dotnet/sdk:8.0 AS build
WORKDIR /src
COPY ["FuelCostService/src/FuelCostService.API/FuelCostService.API.csproj", "FuelCostService/src/FuelCostService.API/"]
COPY ["FuelCostService/src/FuelCostService.Core/FuelCostService.Core.csproj", "FuelCostService/src/FuelCostService.Core/"]
COPY ["FuelCostService/src/FuelCostService.Infrastructure/FuelCostService.Infrastructure.csproj", "FuelCostService/src/FuelCostService.Infrastructure/"]

RUN dotnet restore "FuelCostService/src/FuelCostService.API/FuelCostService.API.csproj"

COPY . .
WORKDIR "/src/FuelCostService/src/FuelCostService.API"
RUN dotnet build "FuelCostService.API.csproj" -c Release -o /app/build

FROM build AS publish
RUN dotnet publish "FuelCostService.API.csproj" -c Release -o /app/publish

FROM base AS final
WORKDIR /app
COPY --from=publish /app/publish .
ENTRYPOINT ["dotnet", "FuelCostService.API.dll"]