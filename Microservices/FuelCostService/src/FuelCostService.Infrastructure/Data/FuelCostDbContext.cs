using Microsoft.EntityFrameworkCore;
using FuelCostService.Core.Models;

namespace FuelCostService.Infrastructure.Data
{
    /// <summary>
    /// Entity Framework DbContext for fuel cost data
    /// </summary>
    public class FuelCostDbContext : DbContext
    {
        public FuelCostDbContext(DbContextOptions<FuelCostDbContext> options) : base(options)
        {
        }

        public DbSet<FuelCosts> FuelCosts { get; set; } = null!;
        public DbSet<FuelCostByType> FuelCostByType { get; set; } = null!;
        public DbSet<FuelCostRateBlocks> FuelCostRateBlocks { get; set; } = null!;
        public DbSet<FuelCostRateBlock> FuelCostRateBlock { get; set; } = null!;
        public DbSet<FuelCostMinimum> FuelCostMinimum { get; set; } = null!;
        public DbSet<FuelCostsMonthly> FuelCostsMonthly { get; set; } = null!;
        public DbSet<FuelCostMonthlyData> FuelCostMonthlyData { get; set; } = null!;
        public DbSet<CodeAndText> CodeAndText { get; set; } = null!;

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            base.OnModelCreating(modelBuilder);

            // Set default schema
            modelBuilder.HasDefaultSchema("fuelcost");

            // Configure FuelCosts entity (main aggregate root)
            modelBuilder.Entity<FuelCosts>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.HouseId).IsRequired();
                entity.Property(e => e.EnergyUpgradeId).IsRequired(false);
                entity.Property(e => e.IncludeCostCalculations).IsRequired();
                entity.Property(e => e.LibraryFile).HasMaxLength(500);

                // Configure relationships
                entity.HasMany(e => e.Electricity).WithOne().HasForeignKey(f => f.FuelCostsId);
                entity.HasMany(e => e.NaturalGas).WithOne().HasForeignKey(f => f.FuelCostsId);
                entity.HasMany(e => e.Oil).WithOne().HasForeignKey(f => f.FuelCostsId);
                entity.HasMany(e => e.Propane).WithOne().HasForeignKey(f => f.FuelCostsId);
                entity.HasMany(e => e.Wood).WithOne().HasForeignKey(f => f.FuelCostsId);
                entity.HasOne(e => e.Monthly).WithOne().HasForeignKey<FuelCostsMonthly>(m => m.FuelCostsId);

                // Indexes for performance
                entity.HasIndex(e => e.HouseId);
                entity.HasIndex(e => e.EnergyUpgradeId);
                entity.HasIndex(e => new { e.HouseId, e.EnergyUpgradeId }).IsUnique();
            });

            // Configure FuelCostByType entity
            modelBuilder.Entity<FuelCostByType>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.FuelCostsId).IsRequired();
                entity.Property(e => e.InternalId).IsRequired();
                entity.Property(e => e.Label).IsRequired().HasMaxLength(255);
                entity.Property(e => e.Comment).HasMaxLength(1000);

                // Configure relationships
                entity.HasOne(e => e.Units).WithOne().HasForeignKey<CodeAndText>(c => c.Id);
                entity.HasOne(e => e.Minimum).WithOne().HasForeignKey<FuelCostMinimum>(m => m.FuelCostByTypeId);
                entity.HasOne(e => e.RateBlocks).WithOne().HasForeignKey<FuelCostRateBlocks>(r => r.FuelCostByTypeId);

                // Indexes
                entity.HasIndex(e => e.FuelCostsId);
                entity.HasIndex(e => new { e.FuelCostsId, e.InternalId });
            });

            // Configure other entities with basic configurations
            modelBuilder.Entity<FuelCostRateBlocks>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.FuelCostByTypeId).IsRequired();
            });

            modelBuilder.Entity<FuelCostRateBlock>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.FuelCostByTypeId).IsRequired();
                entity.Property(e => e.Units).HasColumnType("decimal(18,6)");
                entity.Property(e => e.CostPerUnit).HasColumnType("decimal(18,6)");
            });

            modelBuilder.Entity<FuelCostMinimum>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.FuelCostByTypeId).IsRequired();
                entity.Property(e => e.Units).HasColumnType("decimal(18,6)");
                entity.Property(e => e.Charge).HasColumnType("decimal(18,6)");
            });

            modelBuilder.Entity<FuelCostsMonthly>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.FuelCostsId).IsRequired();
            });

            modelBuilder.Entity<FuelCostMonthlyData>(entity =>
            {
                entity.HasKey(e => e.Id);
                // Monthly values are stored as strings like in the original
            });

            modelBuilder.Entity<CodeAndText>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.Code).IsRequired();
                entity.Property(e => e.Text);
            });
        }
    }
}