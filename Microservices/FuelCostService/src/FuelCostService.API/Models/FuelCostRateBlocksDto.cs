using System;

namespace FuelCostService.API.Models
{
    /// <summary>
    /// DTO for fuel cost rate blocks container (matches FuelCostRateBlocks.cs model)
    /// </summary>
    public class FuelCostRateBlocksDto
    {
        public Guid Id { get; set; }
        public Guid FuelCostByTypeId { get; set; }
        
        public FuelCostRateBlockDto Block1 { get; set; } = new FuelCostRateBlockDto();
        public FuelCostRateBlockDto Block2 { get; set; } = new FuelCostRateBlockDto();
        public FuelCostRateBlockDto Block3 { get; set; } = new FuelCostRateBlockDto();
        public FuelCostRateBlockDto Block4 { get; set; } = new FuelCostRateBlockDto();
    }
}
