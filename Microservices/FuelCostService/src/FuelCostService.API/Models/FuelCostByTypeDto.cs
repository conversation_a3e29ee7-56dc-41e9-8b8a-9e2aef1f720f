using System;

namespace FuelCostService.API.Models
{
    /// <summary>
    /// DTO for individual fuel cost by type (matches FuelCostByType.cs model)
    /// </summary>
    public class FuelCostByTypeDto
    {
        public Guid Id { get; set; }
        public Guid FuelCostsId { get; set; }
        public uint InternalId { get; set; }
        public string Label { get; set; } = string.Empty;
        public string Comment { get; set; } = string.Empty;
        
        public CodeAndTextDto Units { get; set; } = new CodeAndTextDto();
        public FuelCostMinimumDto Minimum { get; set; } = new FuelCostMinimumDto();
        public FuelCostRateBlocksDto RateBlocks { get; set; } = new FuelCostRateBlocksDto();
    }
}
