using System;

namespace FuelCostService.API.Models
{
    /// <summary>
    /// DTO for monthly fuel cost data (matches FuelCostMonthlyData.cs model)
    /// </summary>
    public class FuelCostMonthlyDataDto
    {
        public Guid Id { get; set; }

        // String values like the original model
        public string January { get; set; } = string.Empty;
        public string February { get; set; } = string.Empty;
        public string March { get; set; } = string.Empty;
        public string April { get; set; } = string.Empty;
        public string May { get; set; } = string.Empty;
        public string June { get; set; } = string.Empty;
        public string July { get; set; } = string.Empty;
        public string August { get; set; } = string.Empty;
        public string September { get; set; } = string.Empty;
        public string October { get; set; } = string.Empty;
        public string November { get; set; } = string.Empty;
        public string December { get; set; } = string.Empty;

    }
}
