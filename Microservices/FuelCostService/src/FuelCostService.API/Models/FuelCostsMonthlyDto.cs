using System;

namespace FuelCostService.API.Models
{
    /// <summary>
    /// DTO for monthly fuel costs container (matches FuelCostsMonthly.cs model)
    /// </summary>
    public class FuelCostsMonthlyDto
    {
        public Guid Id { get; set; }
        public Guid FuelCostsId { get; set; }
        
        public FuelCostMonthlyDataDto Electricity { get; set; } = new FuelCostMonthlyDataDto();
        public FuelCostMonthlyDataDto NaturalGas { get; set; } = new FuelCostMonthlyDataDto();
        public FuelCostMonthlyDataDto Oil { get; set; } = new FuelCostMonthlyDataDto();
        public FuelCostMonthlyDataDto Propane { get; set; } = new FuelCostMonthlyDataDto();
        public FuelCostMonthlyDataDto Wood { get; set; } = new FuelCostMonthlyDataDto();
    }
}
