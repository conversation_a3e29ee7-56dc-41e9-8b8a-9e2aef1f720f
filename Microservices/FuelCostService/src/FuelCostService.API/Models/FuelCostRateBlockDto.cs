using System;

namespace FuelCostService.API.Models
{
    /// <summary>
    /// DTO for individual fuel cost rate block (matches FuelCostRateBlock.cs model)
    /// </summary>
    public class FuelCostRateBlockDto
    {
        public Guid Id { get; set; }
        public Guid FuelCostByTypeId { get; set; }
        public decimal Units { get; set; }
        public decimal CostPerUnit { get; set; }
    }
}
