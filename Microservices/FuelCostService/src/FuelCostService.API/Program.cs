using System;
using System.Data;
using System.Text.Json.Serialization;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Microsoft.OpenApi.Models;
using FuelCostService.Core.Interfaces;
using FuelCostService.Core.Services;
using FuelCostService.Infrastructure.Data;
using FuelCostService.Infrastructure.Repositories;

var builder = WebApplication.CreateBuilder(args);

// Add services to the container (following BaseLoadService pattern)
builder.Services.AddControllers().AddJsonOptions(options =>
{
    options.JsonSerializerOptions.DefaultIgnoreCondition = JsonIgnoreCondition.WhenWritingNull;
    options.JsonSerializerOptions.PropertyNamingPolicy = System.Text.Json.JsonNamingPolicy.CamelCase;
    options.JsonSerializerOptions.Converters.Add(new JsonStringEnumConverter());
});

// Configure Swagger/OpenAPI (following BaseLoadService pattern)
builder.Services.AddEndpointsApiExplorer();
builder.Services.AddSwaggerGen(c =>
{
    c.SwaggerDoc("v1", new OpenApiInfo
    {
        Title = "Fuel Cost Service API",
        Version = "v1",
        Description = "A microservice for managing fuel costs in the HOT2000 system",
        Contact = new OpenApiContact
        {
            Name = "HOT2000 Team"
        }
    });
});

// Configure DbContext with schema support (following BaseLoadService pattern)
builder.Services.AddDbContext<FuelCostDbContext>(options =>
{
    options.UseSqlServer(
        builder.Configuration.GetConnectionString("DefaultConnection"),
        sqlOptions =>
        {
            sqlOptions.EnableRetryOnFailure(
                maxRetryCount: 5,
                maxRetryDelay: TimeSpan.FromSeconds(30),
                errorNumbersToAdd: null);
            sqlOptions.CommandTimeout(300);
            // Set the migrations history table schema (following BaseLoadService pattern)
            sqlOptions.MigrationsHistoryTable("__EFMigrationsHistory", "fuelcost");
        });
});

// Configure AutoMapper (following BaseLoadService pattern)
builder.Services.AddAutoMapper(typeof(Program).Assembly);

// Configure Repositories and Services (following BaseLoadService pattern)
builder.Services.AddScoped<IFuelCostRepository, FuelCostRepository>();
builder.Services.AddScoped<IFuelCostService, FuelCostService.Core.Services.FuelCostService>();

// Configure CORS (following BaseLoadService pattern)
builder.Services.AddCors(options =>
{
    options.AddPolicy("AllowAll",
        builder => builder
            .AllowAnyOrigin()
            .AllowAnyMethod()
            .AllowAnyHeader());
});

var app = builder.Build();

// Enable Swagger in all environments (following BaseLoadService pattern)
app.UseSwagger();
app.UseSwaggerUI(c =>
{
    c.SwaggerEndpoint("/swagger/v1/swagger.json", "Fuel Cost Service API V1");
    c.RoutePrefix = string.Empty; // Set Swagger UI at the app's root
});

// Database initialization with robust error handling (following BaseLoadService pattern)
using (var scope = app.Services.CreateScope())
{
    var services = scope.ServiceProvider;
    var logger = services.GetRequiredService<ILogger<Program>>();

    try
    {
        logger.LogInformation("Starting database initialization...");
        var dbContext = services.GetRequiredService<FuelCostDbContext>();

        // Test database connection first
        logger.LogInformation("Testing database connection...");
        var canConnect = await dbContext.Database.CanConnectAsync();

        if (!canConnect)
        {
            logger.LogError("Cannot connect to database");
            throw new InvalidOperationException("Database connection failed");
        }

        logger.LogInformation("Database connection successful");

        var connection = dbContext.Database.GetDbConnection();
        if (connection.State != ConnectionState.Open)
            await connection.OpenAsync();

        // Check for existing tables
        bool hasExistingTables = false;
        using (var command = connection.CreateCommand())
        {
            command.CommandText = @"
                SELECT COUNT(*)
                FROM INFORMATION_SCHEMA.TABLES
                WHERE TABLE_SCHEMA = 'fuelcost'";

            var tableCount = Convert.ToInt32(await command.ExecuteScalarAsync());
            hasExistingTables = tableCount > 0;

            logger.LogInformation($"Found {tableCount} existing tables in fuelcost schema");
        }

        if (hasExistingTables)
        {
            logger.LogInformation("Dropping existing fuelcost schema for clean start...");

            try
            {
                // More robust schema dropping (following BaseLoadService pattern)
                using (var command = connection.CreateCommand())
                {
                    // Drop foreign key constraints first
                    command.CommandText = @"
                        DECLARE @sql NVARCHAR(MAX) = ''
                        SELECT @sql = @sql + 'ALTER TABLE [fuelcost].[' + TABLE_NAME + '] DROP CONSTRAINT [' + CONSTRAINT_NAME + '];' + CHAR(13)
                        FROM INFORMATION_SCHEMA.TABLE_CONSTRAINTS
                        WHERE CONSTRAINT_SCHEMA = 'fuelcost' AND CONSTRAINT_TYPE = 'FOREIGN KEY'
                        IF LEN(@sql) > 0 EXEC sp_executesql @sql";
                    await command.ExecuteNonQueryAsync();

                    // Drop tables
                    command.CommandText = @"
                        DECLARE @sql NVARCHAR(MAX) = ''
                        SELECT @sql = @sql + 'DROP TABLE [fuelcost].[' + TABLE_NAME + '];' + CHAR(13)
                        FROM INFORMATION_SCHEMA.TABLES
                        WHERE TABLE_SCHEMA = 'fuelcost'
                        IF LEN(@sql) > 0 EXEC sp_executesql @sql";
                    await command.ExecuteNonQueryAsync();

                    // Drop schema
                    command.CommandText = @"
                        IF EXISTS (SELECT schema_name FROM information_schema.schemata WHERE schema_name = 'fuelcost')
                        BEGIN
                            DROP SCHEMA [fuelcost]
                        END";
                    await command.ExecuteNonQueryAsync();
                }

                logger.LogInformation("FuelCost schema dropped successfully");
            }
            catch (Exception dropEx)
            {
                logger.LogWarning($"Error during schema drop: {dropEx.Message}");
                logger.LogInformation("Continuing with migration...");
            }
        }

        // Create schema (following BaseLoadService pattern)
        using (var command = connection.CreateCommand())
        {
            command.CommandText = "IF NOT EXISTS (SELECT schema_name FROM information_schema.schemata WHERE schema_name = 'fuelcost') BEGIN EXEC('CREATE SCHEMA fuelcost') END";
            await command.ExecuteNonQueryAsync();
        }

        // Apply migrations (following BaseLoadService pattern)
        logger.LogInformation("Applying migrations...");
        await dbContext.Database.MigrateAsync();

        logger.LogInformation("Database initialization completed successfully");
    }
    catch (Exception ex)
    {
        logger.LogError(ex, "Database initialization failed");

        // Don't throw in startup - let the service run without DB for debugging (following BaseLoadService pattern)
        logger.LogWarning("Service will start without database initialization");
    }
}

// Configure middleware pipeline (following BaseLoadService pattern)
if (app.Environment.IsDevelopment())
{
    app.UseDeveloperExceptionPage();
}
else
{
    app.UseExceptionHandler("/error");
    app.UseHsts();
}

app.UseCors("AllowAll");
app.UseRouting();
app.UseAuthorization();
app.MapControllers();

app.Run();