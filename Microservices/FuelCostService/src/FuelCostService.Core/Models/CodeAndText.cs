using System;
using System.ComponentModel.DataAnnotations;

namespace FuelCostService.Core.Models
{
    public class CodeAndText
    {
        public Guid Id { get; set; }
        
        [Required]
        public string Code
        {
            get { return code; }
            set
            {
                if (code != value)
                {
                    Text = string.Empty;
                    code = value;
                }
            }
        }

        public string Text { get; set; } = string.Empty;

        private string code = string.Empty;

        public CodeAndText()
        {
        }

        public CodeAndText(string code, string text)
        {
            Set(code, text);
        }

        public CodeAndText(CodeAndText? toCopy)
        {
            if (toCopy != null)
            {
                Text = toCopy.Text;
                code = toCopy.Code;
            }
        }

        public void Set(string code, string text)
        {
            this.Code = code;
            this.Text = text;
        }

        public void Set(string code)
        {
            this.Code = code;
        }
    }
}
