using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace FuelCostService.Core.Models
{
    public class FuelCostMonthlyData
    {
        public Guid Id { get; set; }
        
        public string January { get; set; } = string.Empty;
        public string February { get; set; } = string.Empty;
        public string March { get; set; } = string.Empty;
        public string April { get; set; } = string.Empty;
        public string May { get; set; } = string.Empty;
        public string June { get; set; } = string.Empty;
        public string July { get; set; } = string.Empty;
        public string August { get; set; } = string.Empty;
        public string September { get; set; } = string.Empty;
        public string October { get; set; } = string.Empty;
        public string November { get; set; } = string.Empty;
        public string December { get; set; } = string.Empty;


        public FuelCostMonthlyData()
        {
        }

        public FuelCostMonthlyData(FuelCostMonthlyData toCopy)
        {
            if (toCopy != null)
            {
                Id = Guid.NewGuid();
                January = toCopy.January;
                February = toCopy.February;
                March = toCopy.March;
                April = toCopy.April;
                May = toCopy.May;
                June = toCopy.June;
                July = toCopy.July;
                August = toCopy.August;
                September = toCopy.September;
                October = toCopy.October;
                November = toCopy.November;
                December = toCopy.December;
            }
        }


    }
}
