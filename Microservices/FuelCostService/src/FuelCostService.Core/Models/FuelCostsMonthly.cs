using System;
using System.ComponentModel.DataAnnotations;

namespace FuelCostService.Core.Models
{
    public class FuelCostsMonthly
    {
        public Guid Id { get; set; }
        
        [Required]
        public Guid FuelCostsId { get; set; }
        
        public virtual FuelCostMonthlyData Electricity { get; set; } = new FuelCostMonthlyData();
        
        public virtual FuelCostMonthlyData NaturalGas { get; set; } = new FuelCostMonthlyData();
        
        public virtual FuelCostMonthlyData Oil { get; set; } = new FuelCostMonthlyData();
        
        public virtual FuelCostMonthlyData Propane { get; set; } = new FuelCostMonthlyData();
        
        public virtual FuelCostMonthlyData Wood { get; set; } = new FuelCostMonthlyData();

        public virtual FuelCosts FuelCosts { get; set; } = null!;

        public FuelCostsMonthly()
        {
        }

        public FuelCostsMonthly(FuelCostsMonthly toCopy)
        {
            if (toCopy != null)
            {
                Id = Guid.NewGuid();
                Electricity = new FuelCostMonthlyData(toCopy.Electricity);
                NaturalGas = new FuelCostMonthlyData(toCopy.NaturalGas);
                Oil = new FuelCostMonthlyData(toCopy.Oil);
                Propane = new FuelCostMonthlyData(toCopy.Propane);
                Wood = new FuelCostMonthlyData(toCopy.Wood);
            }
        }


    }
}
