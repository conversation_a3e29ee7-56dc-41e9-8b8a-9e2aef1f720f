using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace FuelCostService.Core.Models
{
    public class FuelCostMinimum
    {
        public Guid Id { get; set; }

        [Required]
        public Guid FuelCostByTypeId { get; set; }

        [Column(TypeName = "decimal(18,6)")]
        public decimal Units { get; set; }

        [Column(TypeName = "decimal(18,6)")]
        public decimal Charge { get; set; }

        [ForeignKey("FuelCostByTypeId")]
        public virtual FuelCostByType FuelCostByType { get; set; } = null!;

        public FuelCostMinimum()
        {
        }

        public FuelCostMinimum(FuelCostMinimum toCopy)
        {
            if (toCopy != null)
            {
                Units = toCopy.Units;
                Charge = toCopy.Charge;
            }
        }
    }
}