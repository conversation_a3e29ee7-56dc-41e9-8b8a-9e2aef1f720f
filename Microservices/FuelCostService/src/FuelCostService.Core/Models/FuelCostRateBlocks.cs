using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace FuelCostService.Core.Models
{
    public class FuelCostRateBlocks
    {
        public Guid Id { get; set; }
        
        [Required]
        public Guid FuelCostByTypeId { get; set; }

        public virtual FuelCostRateBlock Block1 { get; set; } = new FuelCostRateBlock();
        public virtual FuelCostRateBlock Block2 { get; set; } = new FuelCostRateBlock();
        public virtual FuelCostRateBlock Block3 { get; set; } = new FuelCostRateBlock();
        public virtual FuelCostRateBlock Block4 { get; set; } = new FuelCostRateBlock();

        [ForeignKey("FuelCostByTypeId")]
        public virtual FuelCostByType FuelCostByType { get; set; } = null!;

        public FuelCostRateBlocks()
        {
        }

        public FuelCostRateBlocks(FuelCostRateBlocks toCopy)
        {
            if (toCopy != null)
            {
                Block1 = new FuelCostRateBlock(toCopy.Block1);
                Block2 = new FuelCostRateBlock(toCopy.Block2);
                Block3 = new FuelCostRateBlock(toCopy.Block3);
                Block4 = new FuelCostRateBlock(toCopy.Block4);
            }
        }
    }
}
