using System;

namespace FuelCostService.Core.Models
{
    public class FuelCostByType
    {
        public Guid Id { get; set; }
        public Guid FuelCostsId { get; set; }
        public uint InternalId { get; set; }
        public string Label { get; set; } = string.Empty;
        public string Comment { get; set; } = string.Empty;

        public CodeAndText Units { get; set; } = new CodeAndText();
        public FuelCostMinimum Minimum { get; set; } = new FuelCostMinimum();
        public FuelCostRateBlocks RateBlocks { get; set; } = new FuelCostRateBlocks();

        public FuelCostByType()
        {
        }

        public FuelCostByType(FuelCostByType toCopy)
        {
            if (toCopy != null)
            {
                Id = Guid.NewGuid();
                InternalId = toCopy.InternalId;
                Label = toCopy.Label;
                Comment = toCopy.Comment;
                Units = new CodeAndText(toCopy.Units);
                Minimum = new FuelCostMinimum(toCopy.Minimum);
                RateBlocks = new FuelCostRateBlocks(toCopy.RateBlocks);
            }
        }
    }
}
