version: '3.8'

services:
  importexport-api:
    build:
      context: .
      dockerfile: Dockerfile
    ports:
      - "5012:80"
    environment:
      - ASPNETCORE_ENVIRONMENT=Development
      - ConnectionStrings__DefaultConnection=Host=importexport-db;Database=importexportservice;Username=postgres;Password=postgres
    depends_on:
      - importexport-db
    networks:
      - importexport-network

  importexport-db:
    image: postgres:15-alpine
    environment:
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=postgres
      - POSTGRES_DB=importexportservice
    ports:
      - "5112:5432"
    volumes:
      - importexport-db-data:/var/lib/postgresql/data
    networks:
      - importexport-network

volumes:
  importexport-db-data:

networks:
  importexport-network:
    driver: bridge