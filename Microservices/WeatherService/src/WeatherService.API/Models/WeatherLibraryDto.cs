using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations; 

namespace WeatherService.API.Models
{
    public class WeatherLibraryDto
    {
        public Guid Id { get; set; }
        public Guid HouseId { get; set; }
        public Guid? EnergyUpgradeId { get; set; }
        public decimal DepthOfFrost { get; set; }
        public decimal HeatingDegreeDay { get; set; }
        public string FileName { get; set; }
        public string BinaryFileName { get; set; } 
        public string ParsingError { get; set; }

        public uint NumberOfRegions { get; set; }
        public uint NumberOfLocations { get; set; }
        public List<WeatherRegionDto> Regions { get; set; } = new List<WeatherRegionDto>();
        public List<WeatherLocationDto> Locations { get; set; } = new List<WeatherLocationDto>();
        public List<WeatherDataDto> WeatherData { get; set; } = new List<WeatherDataDto>();
        // New properties for selected region and location
        public Guid? SelectedRegionId { get; set; }
        public WeatherRegionDto SelectedRegion { get; set; }
        public Guid? SelectedLocationId { get; set; }
        public WeatherLocationDto SelectedLocation { get; set; }

    }

// Updated Request Model - this matches your existing model
public class UpdateWeatherLibraryRequest
{
    public Guid Id { get; set; }
    public Guid HouseId { get; set; }
    public decimal DepthOfFrost { get; set; }
    public decimal? HeatingDegreeDay { get; set; }
    public string? FilePath { get; set; }
    
    // Accept both ID and Name, prioritize Name if provided
    public Guid? RegionId { get; set; }
    public string? RegionName { get; set; }
    public Guid? LocationId { get; set; }
    public string? LocationName { get; set; }
}

   public class ParseLibraryFromFileRequest
{
    [Required]
    public string FilePath { get; set; }
    
    public string? FileName { get; set; }
    
    // Make these properties nullable
    public decimal? DepthOfFrost { get; set; }
    
    public decimal? HeatingDegreeDay { get; set; }
    
    [Required]
    public Guid HouseId { get; set; }
    
    // Optional selection properties
    public Guid? SelectedRegionId { get; set; }
    
    public Guid? SelectedLocationId { get; set; }
}
public class ParseLibraryRequest
    {
    public string FileName { get; set; }
    public string Content { get; set; }
    }
}