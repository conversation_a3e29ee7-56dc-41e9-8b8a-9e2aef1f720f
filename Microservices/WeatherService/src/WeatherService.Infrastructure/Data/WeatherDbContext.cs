using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Text.Json;
using System.Text.Json.Serialization;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using WeatherService.Core.Models;
using WeatherService.Infrastructure.Converters;

namespace WeatherService.Infrastructure.Data
{
    public class WeatherDbContext : DbContext
    {
        public WeatherDbContext(DbContextOptions<WeatherDbContext> options) : base(options)
        {
        }
        
        public DbSet<WeatherLibrary> WeatherLibraries { get; set; }
        public DbSet<WeatherRegion> WeatherRegions { get; set; }
        public DbSet<WeatherLocation> WeatherLocations { get; set; }
        public DbSet<WeatherData> WeatherData { get; set; }

        private readonly JsonSerializerOptions _jsonOptions = new JsonSerializerOptions
        {
            WriteIndented = false,
            NumberHandling = JsonNumberHandling.AllowNamedFloatingPointLiterals
        };

       protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder)
        {
            // Only configure if not already configured (to avoid overriding DI configuration)
            if (!optionsBuilder.IsConfigured)
            {
                // Configure with your connection string if needed
                // optionsBuilder.UseSqlServer(connectionString, options =>
                // {
                //     options.CommandTimeout(300);
                // });
            }
            
            base.OnConfiguring(optionsBuilder);
        }

        // Move the helpers to class-level methods
        private string SerializeFloatArray(float[] array)
        {
            if (array == null) return JsonSerializer.Serialize(Array.Empty<float>(), _jsonOptions);
            return JsonSerializer.Serialize(
                array.Select(x => float.IsNaN(x) || float.IsInfinity(x) ? 0.0f : x).ToArray(),
                _jsonOptions);
        }

        private float[] DeserializeFloatArray(string json)
        {
            if (string.IsNullOrEmpty(json)) return Array.Empty<float>();
            var result = JsonSerializer.Deserialize<float[]>(json, _jsonOptions);
            return result ?? Array.Empty<float>();
        }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            // Set default schema for all entities
            modelBuilder.HasDefaultSchema("weather");

            base.OnModelCreating(modelBuilder);

            // Configure WeatherLibrary entity
            modelBuilder.Entity<WeatherLibrary>(entity =>
            {
                entity.ToTable("WeatherLibraries", "weather");
                entity.HasKey(e => e.Id);
                entity.Property(e => e.EnergyUpgradeId).IsRequired(false);
                entity.Property(e => e.BinaryFileName)
                    .HasMaxLength(50);

                entity.HasMany(l => l.Regions)
                    .WithOne(r => r.WeatherLibrary)
                    .HasForeignKey(r => r.WeatherLibraryId)
                    .OnDelete(DeleteBehavior.Cascade);

                entity.HasMany(l => l.WeatherData)
                    .WithOne()
                    .HasForeignKey("WeatherLibraryId")
                    .OnDelete(DeleteBehavior.Cascade);

                entity.HasOne(l => l.SelectedRegion)
                    .WithMany()
                    .HasForeignKey(l => l.SelectedRegionId)
                    .OnDelete(DeleteBehavior.NoAction)
                    .IsRequired(false);

                entity.HasOne(l => l.SelectedLocation)
                    .WithMany()
                    .HasForeignKey(l => l.SelectedLocationId)
                    .OnDelete(DeleteBehavior.NoAction)
                    .IsRequired(false);

                entity.HasIndex(e => e.FileName);
                entity.HasIndex(e => e.HouseId);
            });

            // Configure WeatherRegion entity
            modelBuilder.Entity<WeatherRegion>(entity =>
            {
                entity.ToTable("WeatherRegions", "weather");
                entity.HasKey(e => e.Id);

                entity.HasMany(r => r.Locations)
                    .WithOne(l => l.Region)
                    .HasForeignKey(l => l.RegionId)
                    .OnDelete(DeleteBehavior.Cascade);

                entity.HasIndex(r => new { r.WeatherLibraryId, r.Code })
                    .IsUnique();
            });

            // Configure WeatherLocation entity
            modelBuilder.Entity<WeatherLocation>(entity =>
            {
                entity.ToTable("WeatherLocations", "weather");
                entity.HasKey(e => e.Id);

                entity.HasIndex(l => new { l.RegionId, l.Code })
                    .IsUnique();
            });

            // Configure WeatherData entity
            modelBuilder.Entity<WeatherData>(entity =>
            {
                entity.ToTable("WeatherData", "weather");
                entity.HasKey(e => e.Id);

                // Change LocationId relationship to NoAction to prevent cascade conflicts
                entity.HasOne(e => e.Location)
                    .WithMany()
                    .HasForeignKey(e => e.LocationId)
                    .OnDelete(DeleteBehavior.NoAction);

                // Configure relationship with WeatherLibrary - keep CASCADE as primary delete path
                entity.HasOne(e => e.WeatherLibrary)
                    .WithMany(l => l.WeatherData)
                    .HasForeignKey(e => e.WeatherLibraryId)
                    .OnDelete(DeleteBehavior.Cascade);

                // Change RegionId relationship to NoAction to prevent cascade conflicts
                entity.HasOne(e => e.Region)
                    .WithMany()
                    .HasForeignKey(e => e.RegionId)
                    .OnDelete(DeleteBehavior.NoAction);

                // Configure the array properties as JSON with handling for special values
                entity.Property(e => e.Temperatures)
                    .HasConversion(
                        v => SerializeFloatArray(v),
                        v => DeserializeFloatArray(v)
                    );

                entity.Property(e => e.WetBulbTemperatures)
                    .HasConversion(
                        v => SerializeFloatArray(v),
                        v => DeserializeFloatArray(v)
                    );

                entity.Property(e => e.WindSpeeds)
                    .HasConversion(
                        v => SerializeFloatArray(v),
                        v => DeserializeFloatArray(v)
                    );

                entity.Property(e => e.StandardDevTemperatures)
                    .HasConversion(
                        v => SerializeFloatArray(v),
                        v => DeserializeFloatArray(v)
                    );

                entity.Property(e => e.GlobalRadiation)
                    .HasConversion(
                        v => SerializeFloatArray(v),
                        v => DeserializeFloatArray(v)
                    );

                entity.Property(e => e.DiffuseRadiation)
                    .HasConversion(
                        v => SerializeFloatArray(v),
                        v => DeserializeFloatArray(v)
                    );

                entity.Property(e => e.TempAmplitudes)
                    .HasConversion(
                        v => SerializeFloatArray(v),
                        v => DeserializeFloatArray(v)
                    );

                // Use the custom Float2DArrayConverter for SolarData with proper value comparer
                entity.Property(e => e.SolarData)
                    .HasConversion(new Float2DArrayConverter())
                    .Metadata.SetValueComparer(Float2DArrayConverter.CreateValueComparer());

                // Add WeatherLibraryId property
                entity.Property<Guid>("WeatherLibraryId");
            });
        }
    }
}