using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace WeatherService.Core.Models
{
    /// <summary>
    /// Represents a parsed weather library file
    /// </summary>
    public class WeatherLibrary
    {
        public Guid Id { get; set; }
        public Guid HouseId { get; set; }  // Added HouseId property
        public Guid? EnergyUpgradeId { get; set; }
        
        [Required]
        [StringLength(255)]
        public string FileName { get; set; }
        
        [Column(TypeName = "decimal(18,2)")]
        public decimal DepthOfFrost { get; set; }
        
        [Column(TypeName = "decimal(18,2)")]
        public decimal HeatingDegreeDay { get; set; }
        
        public string BinaryFileName { get; set; } = string.Empty;
            
        public uint NumberOfRegions { get; set; }
        
        public uint NumberOfLocations { get; set; }
         [StringLength(1000)]
        public string ParsingError { get; set; }
        
        public List<WeatherRegion> Regions { get; set; } = new List<WeatherRegion>();
        public List<WeatherLocation> Locations { get; set; } = new List<WeatherLocation>();
        public List<WeatherData> WeatherData { get; set; } = new List<WeatherData>();

        public Guid? SelectedRegionId { get; set; }
        public Guid? SelectedLocationId { get; set; }
    
    // Optional navigation properties
        [ForeignKey("SelectedRegionId")]
        public WeatherRegion SelectedRegion { get; set; }
    
        [ForeignKey("SelectedLocationId")]
        public WeatherLocation SelectedLocation { get; set; }

        
        // Constructor
        public WeatherLibrary()
        {
        }
        
        // Copy constructor
        public WeatherLibrary(WeatherLibrary source)
        {
            FileName = source.FileName;
            HouseId = source.HouseId;
            BinaryFileName = source.BinaryFileName;
            DepthOfFrost = source.DepthOfFrost;
            HeatingDegreeDay = source.HeatingDegreeDay;
            NumberOfRegions = source.NumberOfRegions;
            NumberOfLocations = source.NumberOfLocations;
        }
    }
}