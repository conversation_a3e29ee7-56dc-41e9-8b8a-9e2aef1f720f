using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using WeatherService.Core.Models;

namespace WeatherService.Core.Interfaces
{
    public interface IWeatherLibraryService
    {
        /// <summary>
        /// Gets all weather libraries
        /// </summary>
        Task<IEnumerable<WeatherLibrary>> GetAllWeatherLibrariesAsync();
        
        /// <summary>
        /// Gets a specific weather library by ID
        /// </summary>
        Task<WeatherLibrary?> GetWeatherLibraryByIdAsync(Guid id);
        
        /// <summary>
        /// Gets a weather library by file name
        /// </summary>
        Task<WeatherLibrary?> GetWeatherLibraryByFileNameAsync(string fileName);
        Task<WeatherData?> GetWeatherDataByLocationIdAsync(Guid locationId);

        
        /// <summary>
        /// Parses and creates a new weather library from content
        /// </summary>
        Task<WeatherLibrary?> ParseWeatherLibraryAsync(string content, string fileName);
        Task<WeatherLibrary?> ParseWeatherLibraryWithBinaryAsync(string directoryFilePath, string fileName,Guid houseId, decimal depthOfFrost, decimal heatingDegreeDay);

        
        /// <summary>
        /// Deletes a weather library
        /// </summary>
        Task DeleteWeatherLibraryAsync(Guid id);
        Task<WeatherData?> GetWeatherDataByIdAsync(Guid id);
    Task<WeatherData> UpdateWeatherDataAsync(WeatherData weatherData);
        Task<IEnumerable<WeatherData>> GetWeatherDataByRegionIdAsync(Guid regionId);

        Task UpdateWeatherLibraryBasicInfoAsync(WeatherLibrary weatherLibrary);
        Task UpdateLibrarySelectionsAsync(Guid libraryId, Guid? selectedRegionId, Guid? selectedLocationId);

        
        /// <summary>
        /// Gets a specific region by ID
        /// </summary>
        Task<WeatherRegion?> GetRegionByIdAsync(Guid id);
        
        /// <summary>
        /// Gets a specific region by code within a weather library
        /// </summary>
        Task<WeatherRegion?> GetRegionByCodeAsync(Guid libraryId, uint code);
        
        /// <summary>
        /// Gets all regions for a specific weather library
        /// </summary>
        Task<IEnumerable<WeatherRegion>> GetRegionsByLibraryIdAsync(Guid libraryId);
        
        /// <summary>
        /// Gets a specific location by ID
        /// </summary>
        Task<WeatherLocation?> GetLocationByIdAsync(Guid id);
        
        /// <summary>
        /// Gets a specific location by code within a weather library
        /// </summary>
        Task<WeatherLocation?> GetLocationByCodeAsync(Guid libraryId, uint code);
        
        /// <summary>
        /// Gets all locations for a specific region
        /// </summary>
        Task<IEnumerable<WeatherLocation>> GetLocationsByRegionIdAsync(Guid regionId);
        Task<WeatherLibrary?> GetWeatherLibraryByHouseIdAsync(Guid houseId);
        Task<WeatherLibrary?> ParseWeatherLibraryFromFileAsync(string filePath, string fileName, Guid houseId, decimal depthOfFrost, decimal heatingDegreeDay);
    }
}