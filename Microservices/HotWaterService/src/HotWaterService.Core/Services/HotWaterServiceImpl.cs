using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using HotWaterService.Core.Interfaces;
using HotWaterService.Core.Models;
using Microsoft.Extensions.Logging;

namespace HotWaterService.Core.Services
{
    public class HotWaterServiceImpl : IHotWaterService
    {
        private readonly IHotWaterRepository _repository;
        private readonly ILogger<HotWaterServiceImpl> _logger;

        public HotWaterServiceImpl(
            IHotWaterRepository repository,
            ILogger<HotWaterServiceImpl> logger)
        {
            _repository = repository;
            _logger = logger;
        }

        public async Task<IEnumerable<HotWater>> GetAllHotWaterSystemsAsync()
        {
            _logger.LogInformation("Getting all hot water systems from service");
            return await _repository.GetAllHotWaterSystemsAsync();
        }

        public async Task<HotWater?> GetHotWaterSystemByHouseIdAsync(Guid houseId)
        {
            _logger.LogInformation("Getting hot water system for house ID: {HouseId} from service", houseId);
            return await _repository.GetHotWaterSystemByHouseIdAsync(houseId);
        }

        public async Task<HotWater?> GetHotWaterSystemByIdAsync(Guid id)
        {
            _logger.LogInformation("Getting hot water system with ID: {Id} from service", id);
            return await _repository.GetHotWaterSystemByIdAsync(id);
        }

        public async Task<HotWater> CreateHotWaterSystemAsync(HotWater hotWater)
        {
            _logger.LogInformation("Creating new hot water system for house ID: {HouseId} from service", hotWater.HouseId);

            // Ensure the hot water system has an ID
            if (hotWater.Id == Guid.Empty)
            {
                hotWater.Id = Guid.NewGuid();
            }

            // Process resource codes to populate text fields for Primary component
            if (hotWater.Primary != null)
            {
                ProcessPrimaryResourceCodes(hotWater.Primary);

                // Set up Primary component relationships
                if (hotWater.Primary.Id == Guid.Empty)
                {
                    hotWater.Primary.Id = Guid.NewGuid();
                }
                hotWater.Primary.HotWaterId = hotWater.Id;

                // Set up nested relationships for Primary
                SetupPrimaryComponentRelationships(hotWater.Primary);
            }

            // Process resource codes to populate text fields for Secondary component
            if (hotWater.Secondary != null)
            {
                ProcessSecondaryResourceCodes(hotWater.Secondary);

                // Set up Secondary component relationships
                if (hotWater.Secondary.Id == Guid.Empty)
                {
                    hotWater.Secondary.Id = Guid.NewGuid();
                }
                hotWater.Secondary.HotWaterId = hotWater.Id;

                // Set up nested relationships for Secondary
                SetupSecondaryComponentRelationships(hotWater.Secondary);
            }

            // Set up NumberOfDwhrSystems relationships
            if (hotWater.NumberOfDwhrSystems != null)
            {
                if (hotWater.NumberOfDwhrSystems.Id == Guid.Empty)
                {
                    hotWater.NumberOfDwhrSystems.Id = Guid.NewGuid();
                }
                hotWater.NumberOfDwhrSystems.HotWaterId = hotWater.Id;
            }

            // Set up NumberOfHotWaterSystems relationships
            if (hotWater.NumberOfHotWaterSystems != null)
            {
                if (hotWater.NumberOfHotWaterSystems.Id == Guid.Empty)
                {
                    hotWater.NumberOfHotWaterSystems.Id = Guid.NewGuid();
                }
                hotWater.NumberOfHotWaterSystems.HotWaterId = hotWater.Id;
            }

            return await _repository.CreateHotWaterSystemAsync(hotWater);
        }

        public async Task UpdateHotWaterSystemAsync(HotWater hotWater)
        {
            _logger.LogInformation("Updating hot water system with ID: {Id} from service", hotWater.Id);

            var exists = await _repository.ExistsAsync(hotWater.Id);
            if (!exists)
            {
                _logger.LogWarning("Hot water system with ID: {Id} not found for update", hotWater.Id);
                throw new InvalidOperationException($"Hot water system with ID {hotWater.Id} not found");
            }

            // Process resource codes to populate text fields for Primary component
            if (hotWater.Primary != null)
            {
                ProcessPrimaryResourceCodes(hotWater.Primary);

                // Set up nested relationships for Primary (includes DWHR resource processing)
                SetupPrimaryComponentRelationships(hotWater.Primary);
            }

            // Process resource codes to populate text fields for Secondary component
            if (hotWater.Secondary != null)
            {
                ProcessSecondaryResourceCodes(hotWater.Secondary);

                // Set up nested relationships for Secondary (includes DWHR resource processing)
                SetupSecondaryComponentRelationships(hotWater.Secondary);
            }

            await _repository.UpdateHotWaterSystemAsync(hotWater);
        }

        public async Task DeleteHotWaterSystemAsync(Guid id)
        {
            _logger.LogInformation("Deleting hot water system with ID: {Id} from service", id);
            await _repository.DeleteHotWaterSystemAsync(id);
        }

        public async Task<HotWater> SetDefaultHotWaterSystemAsync(HotWater hotWater)
        {
            _logger.LogInformation("Setting default hot water system for house ID: {HouseId}", hotWater.HouseId);

            // Set default values for the hot water system
            hotWater.SetDefaults();

            // Ensure primary component exists with defaults
            if (hotWater.Primary == null)
            {
                hotWater.Primary = CreateDefaultPrimaryComponent(hotWater.Id);
            }
            else
            {
                hotWater.Primary.SetDefaults();
            }

            // Update the database with the defaults
            await UpdateHotWaterSystemAsync(hotWater);

            return hotWater;
        }

        public async Task<HotWater> CalculateHotWaterSystemAsync(HotWater hotWater, bool restoreDefaults = false)
        {
            _logger.LogInformation("Calculating hot water system for house ID: {HouseId}", hotWater.HouseId);

            if (restoreDefaults)
            {
                await SetDefaultHotWaterSystemAsync(hotWater);
            }

            // TODO: Implement hot water system calculations
            // This would include energy consumption calculations, efficiency calculations, etc.
            // For now, just return the hot water system as-is

            return hotWater;
        }

        private PrimaryHotWaterComponent CreateDefaultPrimaryComponent(Guid hotWaterId)
        {
            return new PrimaryHotWaterComponent
            {
                Id = Guid.NewGuid(),
                HotWaterId = hotWaterId,
                Fraction = 1.0m
            };
        }

        private void SetupPrimaryComponentRelationships(PrimaryHotWaterComponent component)
        {
            if (component.Solar != null)
            {
                if (component.Solar.Id == Guid.Empty)
                    component.Solar.Id = Guid.NewGuid();
                component.Solar.PrimaryHotWaterComponentId = component.Id;
            }

            if (component.DrainWaterHeatRecovery != null)
            {
                if (component.DrainWaterHeatRecovery.Id == Guid.Empty)
                    component.DrainWaterHeatRecovery.Id = Guid.NewGuid();
                component.DrainWaterHeatRecovery.PrimaryHotWaterComponentId = component.Id;

                // Process resource codes for DWHR
                ProcessDrainWaterHeatRecoveryResourceCodes(component.DrainWaterHeatRecovery);
            }
        }

        private void SetupSecondaryComponentRelationships(SecondaryHotWaterComponent component)
        {
            if (component.Solar != null)
            {
                if (component.Solar.Id == Guid.Empty)
                    component.Solar.Id = Guid.NewGuid();
                component.Solar.SecondaryHotWaterComponentId = component.Id;
            }

            if (component.DrainWaterHeatRecovery != null)
            {
                if (component.DrainWaterHeatRecovery.Id == Guid.Empty)
                    component.DrainWaterHeatRecovery.Id = Guid.NewGuid();
                component.DrainWaterHeatRecovery.SecondaryHotWaterComponentId = component.Id;

                // Process resource codes for DWHR
                ProcessDrainWaterHeatRecoveryResourceCodes(component.DrainWaterHeatRecovery);
            }
        }

        /// <summary>
        /// Processes resource codes to populate English and French text fields automatically
        /// When only a code is provided, this method finds the matching resource and sets the text fields
        /// </summary>
        private void ProcessPrimaryResourceCodes(PrimaryHotWaterComponent component)
        {
            try
            {
                // Process EnergySource
                if (component.EnergySource != null && !string.IsNullOrEmpty(component.EnergySource.Code))
                {
                    var energySource = DhwEnergySources.All.FirstOrDefault(x => x.Code == component.EnergySource.Code);
                    if (energySource != null)
                    {
                        component.EnergySource.English = energySource.English;
                        component.EnergySource.French = energySource.French;
                        component.EnergySource.IsUserSpecified = energySource.IsUserSpecified;
                    }
                }

                // Process TankVolume
                if (component.TankVolume != null && !string.IsNullOrEmpty(component.TankVolume.Code))
                {
                    var tankVolume = DhwTankVolumes.All.FirstOrDefault(x => x.Code == component.TankVolume.Code);
                    if (tankVolume != null)
                    {
                        component.TankVolume.English = tankVolume.English;
                        component.TankVolume.French = tankVolume.French;
                        component.TankVolume.Value = tankVolume.Value;
                        component.TankVolume.IsUserSpecified = tankVolume.IsUserSpecified;
                    }
                }

                // Process DrawPattern
                if (component.DrawPattern != null && !string.IsNullOrEmpty(component.DrawPattern.Code))
                {
                    var drawPattern = DhwDrawPatterns.All.FirstOrDefault(x => x.Code == component.DrawPattern.Code);
                    if (drawPattern != null)
                    {
                        component.DrawPattern.English = drawPattern.English;
                        component.DrawPattern.French = drawPattern.French;
                        component.DrawPattern.IsUserSpecified = drawPattern.IsUserSpecified;
                    }
                }

                // Process TankLocation
                if (component.TankLocation != null && !string.IsNullOrEmpty(component.TankLocation.Code))
                {
                    var tankLocation = DhwTankLocations.All.FirstOrDefault(x => x.Code == component.TankLocation.Code);
                    if (tankLocation != null)
                    {
                        component.TankLocation.English = tankLocation.English;
                        component.TankLocation.French = tankLocation.French;
                        component.TankLocation.IsUserSpecified = tankLocation.IsUserSpecified;
                    }
                }

                // Process TankTypeData using interface approach (following HvacService pattern)
                if (component.TankTypeData != null && !string.IsNullOrEmpty(component.TankTypeData.Code))
                {
                    component.LoadTankTypeFromData();
                    if (component.TankType != null)
                    {
                        component.SetTankType(component.TankType);
                    }
                }

                _logger.LogDebug("Processed resource codes for PrimaryHotWaterComponent {ComponentId}", component.Id);
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Error processing resource codes for PrimaryHotWaterComponent {ComponentId}", component.Id);
            }
        }

        /// <summary>
        /// Processes resource codes for Secondary hot water component
        /// </summary>
        private void ProcessSecondaryResourceCodes(SecondaryHotWaterComponent component)
        {
            try
            {
                // Process EnergySource
                if (component.EnergySource != null && !string.IsNullOrEmpty(component.EnergySource.Code))
                {
                    var energySource = DhwEnergySources.All.FirstOrDefault(x => x.Code == component.EnergySource.Code);
                    if (energySource != null)
                    {
                        component.EnergySource.English = energySource.English;
                        component.EnergySource.French = energySource.French;
                        component.EnergySource.IsUserSpecified = energySource.IsUserSpecified;
                    }
                }

                // Process TankVolume
                if (component.TankVolume != null && !string.IsNullOrEmpty(component.TankVolume.Code))
                {
                    var tankVolume = DhwTankVolumes.All.FirstOrDefault(x => x.Code == component.TankVolume.Code);
                    if (tankVolume != null)
                    {
                        component.TankVolume.English = tankVolume.English;
                        component.TankVolume.French = tankVolume.French;
                        component.TankVolume.Value = tankVolume.Value;
                        component.TankVolume.IsUserSpecified = tankVolume.IsUserSpecified;
                    }
                }

                // Process DrawPattern
                if (component.DrawPattern != null && !string.IsNullOrEmpty(component.DrawPattern.Code))
                {
                    var drawPattern = DhwDrawPatterns.All.FirstOrDefault(x => x.Code == component.DrawPattern.Code);
                    if (drawPattern != null)
                    {
                        component.DrawPattern.English = drawPattern.English;
                        component.DrawPattern.French = drawPattern.French;
                        component.DrawPattern.IsUserSpecified = drawPattern.IsUserSpecified;
                    }
                }

                // Process TankLocation
                if (component.TankLocation != null && !string.IsNullOrEmpty(component.TankLocation.Code))
                {
                    var tankLocation = DhwTankLocations.All.FirstOrDefault(x => x.Code == component.TankLocation.Code);
                    if (tankLocation != null)
                    {
                        component.TankLocation.English = tankLocation.English;
                        component.TankLocation.French = tankLocation.French;
                        component.TankLocation.IsUserSpecified = tankLocation.IsUserSpecified;
                    }
                }

                // Process TankTypeData using interface approach (following HvacService pattern)
                if (component.TankTypeData != null && !string.IsNullOrEmpty(component.TankTypeData.Code))
                {
                    component.LoadTankTypeFromData();
                    if (component.TankType != null)
                    {
                        component.SetTankType(component.TankType);
                    }
                }

                _logger.LogDebug("Processed resource codes for SecondaryHotWaterComponent {ComponentId}", component.Id);
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Error processing resource codes for SecondaryHotWaterComponent {ComponentId}", component.Id);
            }
        }

        /// <summary>
        /// Processes resource codes for DrainWaterHeatRecovery components
        /// </summary>
        private void ProcessDrainWaterHeatRecoveryResourceCodes(DrainWaterHeatRecovery dwhr)
        {
            try
            {
                // Process ShowerTemperature
                if (dwhr.ShowerTemperature != null && !string.IsNullOrEmpty(dwhr.ShowerTemperature.Code))
                {
                    var showerTemp = ShowerTemperatures.All.FirstOrDefault(x => x.Code == dwhr.ShowerTemperature.Code);
                    if (showerTemp != null)
                    {
                        dwhr.ShowerTemperature.English = showerTemp.English;
                        dwhr.ShowerTemperature.French = showerTemp.French;
                        dwhr.ShowerTemperature.IsUserSpecified = showerTemp.IsUserSpecified;
                    }
                }

                // Process ShowerHead
                if (dwhr.ShowerHead != null && !string.IsNullOrEmpty(dwhr.ShowerHead.Code))
                {
                    var showerHead = ShowerFlowRates.All.FirstOrDefault(x => x.Code == dwhr.ShowerHead.Code);
                    if (showerHead != null)
                    {
                        dwhr.ShowerHead.English = showerHead.English;
                        dwhr.ShowerHead.French = showerHead.French;
                        dwhr.ShowerHead.IsUserSpecified = showerHead.IsUserSpecified;
                    }
                }

                // Process Efficiency - no specific resource class needed, efficiency is just a simple CodeAndText
                // Following the original DrainWaterHeatRecovery.cs pattern where Efficiency is just CodeAndText
                if (dwhr.Efficiency != null && !string.IsNullOrEmpty(dwhr.Efficiency.Code))
                {
                    // Efficiency is user-provided text, no resource lookup needed
                    _logger.LogDebug("DWHR Efficiency code {Code} processed as user-provided value", dwhr.Efficiency.Code);
                }

                _logger.LogDebug("Processed resource codes for DrainWaterHeatRecovery {DwhrId}", dwhr.Id);
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Error processing resource codes for DrainWaterHeatRecovery {DwhrId}", dwhr.Id);
            }
        }

        /// <summary>
        /// Gets hot water system by energy upgrade ID
        /// </summary>
        public async Task<HotWater?> GetHotWaterByEnergyUpgradeIdAsync(Guid energyUpgradeId)
        {
            _logger.LogInformation("Getting hot water system by energy upgrade ID: {EnergyUpgradeId}", energyUpgradeId);
            return await _repository.GetHotWaterByEnergyUpgradeIdAsync(energyUpgradeId);
        }

        /// <summary>
        /// Duplicates a hot water system for energy upgrade
        /// </summary>
        public async Task<HotWater> DuplicateHotWaterForEnergyUpgradeAsync(HotWater baseHotWater, Guid energyUpgradeId)
        {
            _logger.LogInformation("Duplicating hot water system {BaseId} for energy upgrade {EnergyUpgradeId}",
                baseHotWater.Id, energyUpgradeId);

            // TODO: Add business validation logic here

            return await _repository.DuplicateHotWaterForEnergyUpgradeAsync(baseHotWater, energyUpgradeId);
        }
    }
}