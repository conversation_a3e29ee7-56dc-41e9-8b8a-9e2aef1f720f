using System;
using System.ComponentModel.DataAnnotations;

namespace HotWaterService.Core.Models
{
    /// <summary>
    /// Represents the hot water system information for a house
    /// Following the HotWater.cs structure from the old app
    /// </summary>
    public class HotWater
    {
        public Guid Id { get; set; }

        public Guid HouseId { get; set; }

        public Guid? EnergyUpgradeId { get; set; }

        public string Label { get; set; } = "Domestic Hot Water";

        // Navigation properties for separate Primary and Secondary components (like Type2 heat pumps)
        public PrimaryHotWaterComponent? Primary { get; set; }
        public SecondaryHotWaterComponent? Secondary { get; set; }

        public NumberOfDwhrSystems? NumberOfDwhrSystems { get; set; }
        public NumberOfHotWaterSystems? NumberOfHotWaterSystems { get; set; }

        public HotWater()
        {
            SetDefaults();
        }

        public void SetDefaults()
        {
            Label = "Domestic Hot Water";
        }

        public void SetFractionOfPrimary(decimal fractionOfPrimary)
        {
            if (Secondary == null && Primary != null)
                Primary.Fraction = 1.0m;
            else if (Primary != null)
            {
                decimal fraction = (fractionOfPrimary > 1.0m) ? 1.0m : fractionOfPrimary;
                if (fraction < 0.0m) fraction = 0.0m;
                Primary.Fraction = fraction;
                if (Secondary != null)
                    Secondary.Fraction = (1.0m - fraction);
            }
        }
    }
}