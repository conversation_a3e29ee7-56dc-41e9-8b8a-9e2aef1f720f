using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using HotWaterService.Core.Models;

namespace HotWaterService.Core.Interfaces
{
    /// <summary>
    /// Repository interface for hot water operations
    /// Following the BaseLoadService pattern
    /// </summary>
    public interface IHotWaterRepository
    {
        /// <summary>
        /// Gets all hot water systems with their related entities
        /// </summary>
        Task<IEnumerable<HotWater>> GetAllHotWaterSystemsAsync();

        /// <summary>
        /// Gets hot water system for a specific house with its related entities
        /// </summary>
        Task<HotWater?> GetHotWaterSystemByHouseIdAsync(Guid houseId);

        /// <summary>
        /// Gets a specific hot water system by ID with its related entities
        /// </summary>
        Task<HotWater?> GetHotWaterSystemByIdAsync(Guid id);

        /// <summary>
        /// Creates a new hot water system with its related entities
        /// </summary>
        Task<HotWater> CreateHotWaterSystemAsync(HotWater hotWater);

        /// <summary>
        /// Updates an existing hot water system and its related entities
        /// </summary>
        Task UpdateHotWaterSystemAsync(HotWater hotWater);

        /// <summary>
        /// Deletes a hot water system and its related entities
        /// </summary>
        Task DeleteHotWaterSystemAsync(Guid id);

        /// <summary>
        /// Checks if a hot water system exists by ID
        /// </summary>
        Task<bool> ExistsAsync(Guid id);

        /// <summary>
        /// Gets hot water system by energy upgrade ID
        /// </summary>
        Task<HotWater?> GetHotWaterByEnergyUpgradeIdAsync(Guid energyUpgradeId);

        /// <summary>
        /// Duplicates a hot water system for energy upgrade
        /// </summary>
        Task<HotWater> DuplicateHotWaterForEnergyUpgradeAsync(HotWater baseHotWater, Guid energyUpgradeId);
    }
}