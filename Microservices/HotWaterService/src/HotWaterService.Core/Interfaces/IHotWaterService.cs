using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using HotWaterService.Core.Models;

namespace HotWaterService.Core.Interfaces
{
    /// <summary>
    /// Service interface for hot water operations
    /// Following the BaseLoadService pattern
    /// </summary>
    public interface IHotWaterService
    {
        /// <summary>
        /// Gets all hot water systems
        /// </summary>
        Task<IEnumerable<HotWater>> GetAllHotWaterSystemsAsync();

        /// <summary>
        /// Gets hot water system for a specific house
        /// </summary>
        Task<HotWater?> GetHotWaterSystemByHouseIdAsync(Guid houseId);

        /// <summary>
        /// Gets a specific hot water system by ID
        /// </summary>
        Task<HotWater?> GetHotWaterSystemByIdAsync(Guid id);

        /// <summary>
        /// Creates a new hot water system
        /// Ensures only one hot water system per house
        /// </summary>
        Task<HotWater> CreateHotWaterSystemAsync(HotWater hotWater);

        /// <summary>
        /// Updates an existing hot water system
        /// </summary>
        Task UpdateHotWaterSystemAsync(HotWater hotWater);

        /// <summary>
        /// Deletes a hot water system
        /// </summary>
        Task DeleteHotWaterSystemAsync(Guid id);

        /// <summary>
        /// Sets default hot water system configuration
        /// </summary>
        Task<HotWater> SetDefaultHotWaterSystemAsync(HotWater hotWater);

        /// <summary>
        /// Calculates hot water system performance
        /// </summary>
        Task<HotWater> CalculateHotWaterSystemAsync(HotWater hotWater, bool restoreDefaults = false);

        /// <summary>
        /// Gets hot water system by energy upgrade ID
        /// </summary>
        Task<HotWater?> GetHotWaterByEnergyUpgradeIdAsync(Guid energyUpgradeId);

        /// <summary>
        /// Duplicates a hot water system for energy upgrade
        /// </summary>
        Task<HotWater> DuplicateHotWaterForEnergyUpgradeAsync(HotWater baseHotWater, Guid energyUpgradeId);
    }
}