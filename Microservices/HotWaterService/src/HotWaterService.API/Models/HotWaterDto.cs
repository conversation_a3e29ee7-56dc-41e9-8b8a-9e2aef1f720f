using System;

namespace HotWaterService.API.Models
{
    /// <summary>
    /// Data Transfer Object for HotWater
    /// Following the BaseLoadService DTO pattern
    /// </summary>
    public class HotWaterDto
    {
        public Guid Id { get; set; }

        public Guid HouseId { get; set; }

        public Guid? EnergyUpgradeId { get; set; }

        public string Label { get; set; } = "Domestic Hot Water";
        
        // Navigation properties as DTOs
        public PrimaryHotWaterComponentDto? Primary { get; set; }
        public SecondaryHotWaterComponentDto? Secondary { get; set; }
        public NumberOfDwhrSystemsDto? NumberOfDwhrSystems { get; set; }
        public NumberOfHotWaterSystemsDto? NumberOfHotWaterSystems { get; set; }
    }
}
