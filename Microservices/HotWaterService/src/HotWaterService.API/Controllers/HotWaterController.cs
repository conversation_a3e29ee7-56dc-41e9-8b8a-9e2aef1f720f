using System;
using System.Collections.Generic;
using System.Linq;
using System.Text.Json;
using System.Text.Json.Serialization;
using System.Threading.Tasks;
using AutoMapper;
using HotWaterService.API.Models;
using HotWaterService.Core.Interfaces;
using HotWaterService.Core.Models;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;

namespace HotWaterService.API.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class HotWaterController : ControllerBase
    {
        private readonly IHotWaterService _hotWaterService;
        private readonly IMapper _mapper;
        private readonly ILogger<HotWaterController> _logger;

        public HotWaterController(
            IHotWaterService hotWaterService,
            IMapper mapper,
            ILogger<HotWaterController> logger)
        {
            _hotWaterService = hotWaterService;
            _mapper = mapper;
            _logger = logger;
        }

        /// <summary>
        /// Gets all hot water systems
        /// </summary>
        [HttpGet]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<ActionResult<IEnumerable<HotWaterDto>>> GetAllHotWaterSystems()
        {
            _logger.LogInformation("Getting all hot water systems");

            var hotWaterSystems = await _hotWaterService.GetAllHotWaterSystemsAsync();

            if (hotWaterSystems == null)
            {
                _logger.LogWarning("No hot water systems found");
                return NotFound();
            }

            var hotWaterDtos = _mapper.Map<IEnumerable<HotWaterDto>>(hotWaterSystems);

            return Ok(hotWaterDtos);
        }

        /// <summary>
        /// Gets hot water system for a specific house
        /// </summary>
        [HttpGet("house/{houseId}")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<ActionResult<HotWaterDto>> GetHotWaterSystemByHouseId(Guid houseId)
        {
            _logger.LogInformation("Getting hot water system for house ID: {HouseId}", houseId);

            var hotWater = await _hotWaterService.GetHotWaterSystemByHouseIdAsync(houseId);

            if (hotWater == null)
            {
                _logger.LogWarning("No hot water system found for house ID: {HouseId}", houseId);
                return NotFound();
            }

            var hotWaterDto = _mapper.Map<HotWaterDto>(hotWater);

            return Ok(hotWaterDto);
        }

        /// <summary>
        /// Gets a specific hot water system by ID
        /// </summary>
        [HttpGet("{id}")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<ActionResult<HotWaterDto>> GetHotWaterSystemById(Guid id)
        {
            _logger.LogInformation("Getting hot water system with ID: {Id}", id);

            var hotWater = await _hotWaterService.GetHotWaterSystemByIdAsync(id);

            if (hotWater == null)
            {
                _logger.LogWarning("No hot water system found with ID: {Id}", id);
                return NotFound();
            }

            var hotWaterDto = _mapper.Map<HotWaterDto>(hotWater);

            return Ok(hotWaterDto);
        }

        /// <summary>
        /// Creates a new hot water system
        /// </summary>
        [HttpPost]
        [ProducesResponseType(StatusCodes.Status201Created)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        public async Task<ActionResult<HotWaterDto>> CreateHotWaterSystem([FromBody] HotWaterDto hotWaterDto)
        {
            if (hotWaterDto == null)
            {
                _logger.LogWarning("Hot water system data is null");
                return BadRequest("Hot water system data is required");
            }

            _logger.LogInformation("Creating new hot water system for house ID: {HouseId}", hotWaterDto.HouseId);

            var hotWater = _mapper.Map<HotWater>(hotWaterDto);

            // Populate tank type text from resource classes
            PopulateTankTypeText(hotWater);

            var createdHotWater = await _hotWaterService.CreateHotWaterSystemAsync(hotWater);
            var createdHotWaterDto = _mapper.Map<HotWaterDto>(createdHotWater);

            return CreatedAtAction(
                nameof(GetHotWaterSystemById),
                new { id = createdHotWaterDto.Id },
                createdHotWaterDto);
        }

        /// <summary>
        /// Updates an existing hot water system
        /// </summary>
        [HttpPut("{id}")]
        [ProducesResponseType(StatusCodes.Status204NoContent)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<IActionResult> UpdateHotWaterSystem(Guid id, [FromBody] HotWaterDto hotWaterDto)
        {
            if (hotWaterDto == null)
            {
                _logger.LogWarning("Hot water system data is null");
                return BadRequest("Hot water system data is required");
            }

            if (id != hotWaterDto.Id)
            {
                _logger.LogWarning("ID mismatch: {Id} != {DtoId}", id, hotWaterDto.Id);
                return BadRequest("ID mismatch");
            }

            _logger.LogInformation("Updating hot water system with ID: {Id}", id);

            var hotWater = _mapper.Map<HotWater>(hotWaterDto);

            // Populate tank type text from resource classes
            PopulateTankTypeText(hotWater);

            try
            {
                await _hotWaterService.UpdateHotWaterSystemAsync(hotWater);
            }
            catch (InvalidOperationException ex) when (ex.Message.Contains("not found"))
            {
                _logger.LogWarning("Hot water system not found with ID: {Id}", id);
                return NotFound($"Hot water system not found with ID: {id}");
            }

            return NoContent();
        }

        /// <summary>
        /// Deletes a hot water system
        /// </summary>
        [HttpDelete("{id}")]
        [ProducesResponseType(StatusCodes.Status204NoContent)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<IActionResult> DeleteHotWaterSystem(Guid id)
        {
            var existingHotWater = await _hotWaterService.GetHotWaterSystemByIdAsync(id);
            if (existingHotWater == null)
            {
                _logger.LogWarning("Hot water system with ID: {Id} not found for deletion", id);
                return NotFound();
            }

            _logger.LogInformation("Deleting hot water system with ID: {Id}", id);

            await _hotWaterService.DeleteHotWaterSystemAsync(id);

            return NoContent();
        }

        /// <summary>
        /// Populates tank type text from resource classes based on energy source
        /// </summary>
        private void PopulateTankTypeText(HotWater hotWater)
        {
            // Populate Primary component tank types
            if (hotWater.Primary != null)
            {
                PopulatePrimaryComponentTankType(hotWater.Primary);
            }

            // Populate Secondary component tank types
            if (hotWater.Secondary != null)
            {
                PopulateSecondaryComponentTankType(hotWater.Secondary);
            }
        }

        /// <summary>
        /// Helper method to populate tank type for Primary component
        /// </summary>
        private void PopulatePrimaryComponentTankType(PrimaryHotWaterComponent component)
        {
            // Tank type logic is handled in the service layer
            // This method is kept for future tank type processing if needed
        }

        /// <summary>
        /// Helper method to populate tank type for Secondary component
        /// </summary>
        private void PopulateSecondaryComponentTankType(SecondaryHotWaterComponent component)
        {
            // Tank type logic is handled in the service layer
            // This method is kept for future tank type processing if needed
        }

        /// <summary>
        /// Gets hot water system by energy upgrade ID
        /// </summary>
        [HttpGet("energy-upgrades/{energyUpgradeId}/hotwater")]
        [ProducesResponseType(typeof(HotWaterDto), StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<IActionResult> GetHotWaterByEnergyUpgradeId(Guid energyUpgradeId)
        {
            _logger.LogInformation("Getting hot water system by energy upgrade ID: {EnergyUpgradeId}", energyUpgradeId);

            var hotWater = await _hotWaterService.GetHotWaterByEnergyUpgradeIdAsync(energyUpgradeId);
            if (hotWater == null)
            {
                _logger.LogWarning("Hot water system not found for energy upgrade ID: {EnergyUpgradeId}", energyUpgradeId);
                return NotFound($"Hot water system not found for energy upgrade ID: {energyUpgradeId}");
            }

            var hotWaterDto = _mapper.Map<HotWaterDto>(hotWater);
            return Ok(hotWaterDto);
        }

        /// <summary>
        /// Duplicates a hot water system for energy upgrade
        /// </summary>
        [HttpPost("hotwater/{baseHotWaterId}/duplicate-for-energy-upgrade")]
        [ProducesResponseType(typeof(HotWaterDto), StatusCodes.Status201Created)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<IActionResult> DuplicateHotWaterForEnergyUpgrade(
            Guid baseHotWaterId,
            [FromQuery] Guid energyUpgradeId)
        {
            if (energyUpgradeId == Guid.Empty)
            {
                _logger.LogWarning("Energy upgrade ID is required");
                return BadRequest("Energy upgrade ID is required");
            }

            _logger.LogInformation("Duplicating hot water system {BaseHotWaterId} for energy upgrade {EnergyUpgradeId}",
                baseHotWaterId, energyUpgradeId);

            var baseHotWater = await _hotWaterService.GetHotWaterSystemByIdAsync(baseHotWaterId);
            if (baseHotWater == null)
            {
                _logger.LogWarning("Base hot water system not found: {BaseHotWaterId}", baseHotWaterId);
                return NotFound($"Base hot water system not found: {baseHotWaterId}");
            }

            var duplicatedHotWater = await _hotWaterService.DuplicateHotWaterForEnergyUpgradeAsync(
                baseHotWater, energyUpgradeId);

            var duplicatedHotWaterDto = _mapper.Map<HotWaterDto>(duplicatedHotWater);

            return CreatedAtAction(
                nameof(GetHotWaterSystemById),
                new { id = duplicatedHotWaterDto.Id },
                duplicatedHotWaterDto);
        }
    }
}