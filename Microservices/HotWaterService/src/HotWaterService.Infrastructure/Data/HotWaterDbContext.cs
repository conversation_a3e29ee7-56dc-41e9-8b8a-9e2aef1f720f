using Microsoft.EntityFrameworkCore;
using HotWaterService.Core.Models;

namespace HotWaterService.Infrastructure.Data
{
    public class HotWaterDbContext : DbContext
    {
        public HotWaterDbContext(DbContextOptions<HotWaterDbContext> options)
            : base(options)
        {
        }

        // Main entities
        public DbSet<HotWater> HotWaters { get; set; }
        public DbSet<PrimaryHotWaterComponent> PrimaryHotWaterComponents { get; set; }
        public DbSet<SecondaryHotWaterComponent> SecondaryHotWaterComponents { get; set; }
        public DbSet<Solar> Solars { get; set; }
        public DbSet<DrainWaterHeatRecovery> DrainWaterHeatRecoveries { get; set; }
        public DbSet<NumberOfDwhrSystems> NumberOfDwhrSystems { get; set; }
        public DbSet<NumberOfHotWaterSystems> NumberOfHotWaterSystems { get; set; }

        // Resource entities are now owned entities, not separate tables

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            base.OnModelCreating(modelBuilder);

            // Configure schema (following BaseLoadService pattern)
            modelBuilder.HasDefaultSchema("hotwater");

            ConfigureEntities(modelBuilder);
        }

        private void ConfigureEntities(ModelBuilder modelBuilder)
        {
            // HotWater configuration
            modelBuilder.Entity<HotWater>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.EnergyUpgradeId).IsRequired(false);
                entity.Property(e => e.Label).HasMaxLength(100);

                // Configure Primary and Secondary components (like Type2 heat pumps)
                entity.HasOne(e => e.Primary)
                      .WithOne(p => p.HotWater)
                      .HasForeignKey<PrimaryHotWaterComponent>(p => p.HotWaterId)
                      .OnDelete(DeleteBehavior.Cascade);

                entity.HasOne(e => e.Secondary)
                      .WithOne(s => s.HotWater)
                      .HasForeignKey<SecondaryHotWaterComponent>(s => s.HotWaterId)
                      .OnDelete(DeleteBehavior.Cascade);

                entity.HasOne(e => e.NumberOfDwhrSystems)
                      .WithOne(n => n.HotWater)
                      .HasForeignKey<NumberOfDwhrSystems>(n => n.HotWaterId)
                      .OnDelete(DeleteBehavior.Cascade);

                entity.HasOne(e => e.NumberOfHotWaterSystems)
                      .WithOne(n => n.HotWater)
                      .HasForeignKey<NumberOfHotWaterSystems>(n => n.HotWaterId)
                      .OnDelete(DeleteBehavior.Cascade);
            });

            // PrimaryHotWaterComponent configuration
            modelBuilder.Entity<PrimaryHotWaterComponent>(entity =>
            {
                entity.HasKey(e => e.Id);

                // Ignore interface properties - Entity Framework cannot handle interfaces
                entity.Ignore(e => e.TankType);

                // Owned entities
                entity.OwnsOne(e => e.EquipmentInformation);
                entity.OwnsOne(e => e.TankTypeData);
                entity.OwnsOne(e => e.EnergyFactor);

                // Resource entities as owned entities (following HvacService pattern)
                entity.OwnsOne(e => e.EnergySource, es =>
                {
                    es.Property(e => e.Code).HasMaxLength(10);
                    es.Property(e => e.English).HasMaxLength(100);
                    es.Property(e => e.French).HasMaxLength(100);
                    es.Property(e => e.IsUserSpecified);
                });

                entity.OwnsOne(e => e.TankVolume, tv =>
                {
                    tv.Property(t => t.Code).HasMaxLength(10);
                    tv.Property(t => t.English).HasMaxLength(100);
                    tv.Property(t => t.French).HasMaxLength(100);
                    tv.Property(t => t.IsUserSpecified);
                    tv.Property(t => t.Value).HasPrecision(18, 2);
                });

                entity.OwnsOne(e => e.DrawPattern, dp =>
                {
                    dp.Property(d => d.Code).HasMaxLength(10);
                    dp.Property(d => d.English).HasMaxLength(100);
                    dp.Property(d => d.French).HasMaxLength(100);
                    dp.Property(d => d.IsUserSpecified);
                });

                entity.OwnsOne(e => e.TankLocation, tl =>
                {
                    tl.Property(t => t.Code).HasMaxLength(10);
                    tl.Property(t => t.English).HasMaxLength(100);
                    tl.Property(t => t.French).HasMaxLength(100);
                    tl.Property(t => t.IsUserSpecified);
                });

                // Navigation properties
                entity.HasOne(e => e.Solar)
                      .WithOne(s => s.PrimaryHotWaterComponent)
                      .HasForeignKey<Solar>(s => s.PrimaryHotWaterComponentId)
                      .OnDelete(DeleteBehavior.Cascade);

                entity.HasOne(e => e.DrainWaterHeatRecovery)
                      .WithOne(d => d.PrimaryHotWaterComponent)
                      .HasForeignKey<DrainWaterHeatRecovery>(d => d.PrimaryHotWaterComponentId)
                      .OnDelete(DeleteBehavior.NoAction);
            });

            // SecondaryHotWaterComponent configuration
            modelBuilder.Entity<SecondaryHotWaterComponent>(entity =>
            {
                entity.HasKey(e => e.Id);

                // Ignore interface properties - Entity Framework cannot handle interfaces
                entity.Ignore(e => e.TankType);

                // Owned entities
                entity.OwnsOne(e => e.EquipmentInformation);
                entity.OwnsOne(e => e.TankTypeData);
                entity.OwnsOne(e => e.EnergyFactor);

                // Resource entities as owned entities (following HvacService pattern)
                entity.OwnsOne(e => e.EnergySource, es =>
                {
                    es.Property(e => e.Code).HasMaxLength(10);
                    es.Property(e => e.English).HasMaxLength(100);
                    es.Property(e => e.French).HasMaxLength(100);
                    es.Property(e => e.IsUserSpecified);
                });

                entity.OwnsOne(e => e.TankVolume, tv =>
                {
                    tv.Property(t => t.Code).HasMaxLength(10);
                    tv.Property(t => t.English).HasMaxLength(100);
                    tv.Property(t => t.French).HasMaxLength(100);
                    tv.Property(t => t.IsUserSpecified);
                    tv.Property(t => t.Value).HasPrecision(18, 2);
                });

                entity.OwnsOne(e => e.DrawPattern, dp =>
                {
                    dp.Property(d => d.Code).HasMaxLength(10);
                    dp.Property(d => d.English).HasMaxLength(100);
                    dp.Property(d => d.French).HasMaxLength(100);
                    dp.Property(d => d.IsUserSpecified);
                });

                entity.OwnsOne(e => e.TankLocation, tl =>
                {
                    tl.Property(t => t.Code).HasMaxLength(10);
                    tl.Property(t => t.English).HasMaxLength(100);
                    tl.Property(t => t.French).HasMaxLength(100);
                    tl.Property(t => t.IsUserSpecified);
                });

                // Navigation properties
                entity.HasOne(e => e.Solar)
                      .WithOne(s => s.SecondaryHotWaterComponent)
                      .HasForeignKey<Solar>(s => s.SecondaryHotWaterComponentId)
                      .OnDelete(DeleteBehavior.NoAction);

                entity.HasOne(e => e.DrainWaterHeatRecovery)
                      .WithOne(d => d.SecondaryHotWaterComponent)
                      .HasForeignKey<DrainWaterHeatRecovery>(d => d.SecondaryHotWaterComponentId)
                      .OnDelete(DeleteBehavior.NoAction);
            });

            // Solar configuration
            modelBuilder.Entity<Solar>(entity =>
            {
                entity.HasKey(e => e.Id);
            });

            // DrainWaterHeatRecovery configuration
            modelBuilder.Entity<DrainWaterHeatRecovery>(entity =>
            {
                entity.HasKey(e => e.Id);

                // Owned entities (following HvacService pattern)
                entity.OwnsOne(e => e.EquipmentInformation);
                entity.OwnsOne(e => e.Efficiency);

                // Resource entities as owned entities (no foreign key relationships)
                entity.OwnsOne(e => e.ShowerTemperature, st =>
                {
                    st.Property(s => s.Code).HasMaxLength(10);
                    st.Property(s => s.English).HasMaxLength(100);
                    st.Property(s => s.French).HasMaxLength(100);
                    st.Property(s => s.IsUserSpecified);
                });

                entity.OwnsOne(e => e.ShowerHead, sh =>
                {
                    sh.Property(s => s.Code).HasMaxLength(10);
                    sh.Property(s => s.English).HasMaxLength(100);
                    sh.Property(s => s.French).HasMaxLength(100);
                    sh.Property(s => s.IsUserSpecified);
                });
            });

            // NumberOfDwhrSystems configuration
            modelBuilder.Entity<NumberOfDwhrSystems>(entity =>
            {
                entity.HasKey(e => e.Id);
            });

            // NumberOfHotWaterSystems configuration
            modelBuilder.Entity<NumberOfHotWaterSystems>(entity =>
            {
                entity.HasKey(e => e.Id);
            });
        }
    }
}