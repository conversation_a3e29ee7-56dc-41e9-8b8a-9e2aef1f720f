using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using HotWaterService.Core.Interfaces;
using HotWaterService.Core.Models;
using HotWaterService.Infrastructure.Data;
using Microsoft.Extensions.Logging;

namespace HotWaterService.Infrastructure.Repositories
{
    /// <summary>
    /// Repository implementation for hot water operations
    /// Following the Wall repository approach (simple property updates, direct SaveChanges)
    /// </summary>
    public class HotWaterRepository : IHotWaterRepository
    {
        private readonly HotWaterDbContext _context;
        private readonly ILogger<HotWaterRepository> _logger;

        public HotWaterRepository(HotWaterDbContext context, ILogger<HotWaterRepository> logger)
        {
            _context = context;
            _logger = logger;
        }

        public async Task<IEnumerable<HotWater>> GetAllHotWaterSystemsAsync()
        {
            _logger.LogInformation("Getting all hot water systems from repository");

            return await _context.HotWaters
                .Include(h => h.Primary)
                    .ThenInclude(p => p.EquipmentInformation)
                .Include(h => h.Primary)
                    .ThenInclude(p => p.EnergySource)
                .Include(h => h.Primary)
                    .ThenInclude(p => p.TankVolume)
                .Include(h => h.Primary)
                    .ThenInclude(p => p.DrawPattern)
                .Include(h => h.Primary)
                    .ThenInclude(p => p.TankLocation)
                .Include(h => h.Primary)
                    .ThenInclude(p => p.Solar)
                .Include(h => h.Primary)
                    .ThenInclude(p => p.DrainWaterHeatRecovery)
                        .ThenInclude(d => d.EquipmentInformation)
                .Include(h => h.Primary)
                    .ThenInclude(p => p.DrainWaterHeatRecovery)
                        .ThenInclude(d => d.ShowerTemperature)
                .Include(h => h.Primary)
                    .ThenInclude(p => p.DrainWaterHeatRecovery)
                        .ThenInclude(d => d.ShowerHead)
                .Include(h => h.Secondary)
                    .ThenInclude(s => s.EquipmentInformation)
                .Include(h => h.Secondary)
                    .ThenInclude(s => s.EnergySource)
                .Include(h => h.Secondary)
                    .ThenInclude(s => s.TankVolume)
                .Include(h => h.Secondary)
                    .ThenInclude(s => s.DrawPattern)
                .Include(h => h.Secondary)
                    .ThenInclude(s => s.TankLocation)
                .Include(h => h.Secondary)
                    .ThenInclude(s => s.Solar)
                .Include(h => h.Secondary)
                    .ThenInclude(s => s.DrainWaterHeatRecovery)
                        .ThenInclude(d => d.EquipmentInformation)
                .Include(h => h.Secondary)
                    .ThenInclude(s => s.DrainWaterHeatRecovery)
                        .ThenInclude(d => d.ShowerTemperature)
                .Include(h => h.Secondary)
                    .ThenInclude(s => s.DrainWaterHeatRecovery)
                        .ThenInclude(d => d.ShowerHead)
                .Include(h => h.NumberOfDwhrSystems)
                .Include(h => h.NumberOfHotWaterSystems)
                .ToListAsync();
        }

        public async Task<HotWater?> GetHotWaterSystemByIdAsync(Guid id)
        {
            _logger.LogInformation("Getting hot water system with ID: {Id} from repository", id);

            return await _context.HotWaters
                .AsNoTracking()
                .Include(h => h.Primary)
                    .ThenInclude(p => p.EquipmentInformation)
                .Include(h => h.Primary)
                    .ThenInclude(p => p.EnergySource)
                .Include(h => h.Primary)
                    .ThenInclude(p => p.TankVolume)
                .Include(h => h.Primary)
                    .ThenInclude(p => p.DrawPattern)
                .Include(h => h.Primary)
                    .ThenInclude(p => p.TankLocation)
                .Include(h => h.Primary)
                    .ThenInclude(p => p.Solar)
                .Include(h => h.Primary)
                    .ThenInclude(p => p.DrainWaterHeatRecovery)
                        .ThenInclude(d => d.EquipmentInformation)
                .Include(h => h.Primary)
                    .ThenInclude(p => p.DrainWaterHeatRecovery)
                        .ThenInclude(d => d.ShowerTemperature)
                .Include(h => h.Primary)
                    .ThenInclude(p => p.DrainWaterHeatRecovery)
                        .ThenInclude(d => d.ShowerHead)
                .Include(h => h.Secondary)
                    .ThenInclude(s => s.EquipmentInformation)
                .Include(h => h.Secondary)
                    .ThenInclude(s => s.EnergySource)
                .Include(h => h.Secondary)
                    .ThenInclude(s => s.TankVolume)
                .Include(h => h.Secondary)
                    .ThenInclude(s => s.DrawPattern)
                .Include(h => h.Secondary)
                    .ThenInclude(s => s.TankLocation)
                .Include(h => h.Secondary)
                    .ThenInclude(s => s.Solar)
                .Include(h => h.Secondary)
                    .ThenInclude(s => s.DrainWaterHeatRecovery)
                        .ThenInclude(d => d.EquipmentInformation)
                .Include(h => h.Secondary)
                    .ThenInclude(s => s.DrainWaterHeatRecovery)
                        .ThenInclude(d => d.ShowerTemperature)
                .Include(h => h.Secondary)
                    .ThenInclude(s => s.DrainWaterHeatRecovery)
                        .ThenInclude(d => d.ShowerHead)
                .Include(h => h.NumberOfDwhrSystems)
                .Include(h => h.NumberOfHotWaterSystems)
                .FirstOrDefaultAsync(h => h.Id == id);
        }

        public async Task<HotWater?> GetHotWaterSystemByHouseIdAsync(Guid houseId)
        {
            _logger.LogInformation("Getting hot water system for house ID: {HouseId} from repository", houseId);

            return await _context.HotWaters
                .Include(h => h.Primary)
                    .ThenInclude(p => p.EquipmentInformation)
                .Include(h => h.Primary)
                    .ThenInclude(p => p.EnergySource)
                .Include(h => h.Primary)
                    .ThenInclude(p => p.TankVolume)
                .Include(h => h.Primary)
                    .ThenInclude(p => p.DrawPattern)
                .Include(h => h.Primary)
                    .ThenInclude(p => p.TankLocation)
                .Include(h => h.Primary)
                    .ThenInclude(p => p.Solar)
                .Include(h => h.Primary)
                    .ThenInclude(p => p.DrainWaterHeatRecovery)
                        .ThenInclude(d => d.EquipmentInformation)
                .Include(h => h.Primary)
                    .ThenInclude(p => p.DrainWaterHeatRecovery)
                        .ThenInclude(d => d.ShowerTemperature)
                .Include(h => h.Primary)
                    .ThenInclude(p => p.DrainWaterHeatRecovery)
                        .ThenInclude(d => d.ShowerHead)
                .Include(h => h.Secondary)
                    .ThenInclude(s => s.EquipmentInformation)
                .Include(h => h.Secondary)
                    .ThenInclude(s => s.EnergySource)
                .Include(h => h.Secondary)
                    .ThenInclude(s => s.TankVolume)
                .Include(h => h.Secondary)
                    .ThenInclude(s => s.DrawPattern)
                .Include(h => h.Secondary)
                    .ThenInclude(s => s.TankLocation)
                .Include(h => h.Secondary)
                    .ThenInclude(s => s.Solar)
                .Include(h => h.Secondary)
                    .ThenInclude(s => s.DrainWaterHeatRecovery)
                        .ThenInclude(d => d.EquipmentInformation)
                .Include(h => h.Secondary)
                    .ThenInclude(s => s.DrainWaterHeatRecovery)
                        .ThenInclude(d => d.ShowerTemperature)
                .Include(h => h.Secondary)
                    .ThenInclude(s => s.DrainWaterHeatRecovery)
                        .ThenInclude(d => d.ShowerHead)
                .Include(h => h.NumberOfDwhrSystems)
                .Include(h => h.NumberOfHotWaterSystems)
                .FirstOrDefaultAsync(h => h.HouseId == houseId);
        }

        public async Task<HotWater> CreateHotWaterSystemAsync(HotWater hotWater)
        {
            _logger.LogInformation("Creating hot water system for house ID: {HouseId} in repository", hotWater.HouseId);

            // Clear any existing tracking to prevent conflicts (following HvacService pattern)
            _context.ChangeTracker.Clear();

            // Apply selective creation logic - ensure all entities have unique IDs before tracking
            await ApplySelectiveCreationLogicAsync(hotWater);

            _context.HotWaters.Add(hotWater);
            await _context.SaveChangesAsync();

            _logger.LogInformation("Successfully created hot water system with ID: {Id}", hotWater.Id);
            return hotWater;
        }

        public async Task UpdateHotWaterSystemAsync(HotWater hotWater)
        {
            _logger.LogInformation("Updating hot water system with ID: {Id} in repository", hotWater.Id);

            var existingHotWater = await GetHotWaterSystemByIdAsync(hotWater.Id);
            if (existingHotWater == null)
            {
                throw new KeyNotFoundException($"Hot water system with ID {hotWater.Id} not found");
            }

            // Update main properties
            existingHotWater.Label = hotWater.Label;
            existingHotWater.HouseId = hotWater.HouseId;

            // Update Primary component
            UpdatePrimaryComponent(existingHotWater.Primary, hotWater.Primary);

            // Update Secondary component
            UpdateSecondaryComponent(existingHotWater.Secondary, hotWater.Secondary);

            // Update NumberOfDwhrSystems
            if (hotWater.NumberOfDwhrSystems != null && existingHotWater.NumberOfDwhrSystems != null)
            {
                existingHotWater.NumberOfDwhrSystems.LowEfficiency = hotWater.NumberOfDwhrSystems.LowEfficiency;
                existingHotWater.NumberOfDwhrSystems.HighEfficiency = hotWater.NumberOfDwhrSystems.HighEfficiency;
            }
            else if (hotWater.NumberOfDwhrSystems != null)
            {
                hotWater.NumberOfDwhrSystems.HotWaterId = hotWater.Id;
                existingHotWater.NumberOfDwhrSystems = hotWater.NumberOfDwhrSystems;
            }

            // Update NumberOfHotWaterSystems
            if (hotWater.NumberOfHotWaterSystems != null && existingHotWater.NumberOfHotWaterSystems != null)
            {
                existingHotWater.NumberOfHotWaterSystems.EnergyStarInstantaneousCondensing = hotWater.NumberOfHotWaterSystems.EnergyStarInstantaneousCondensing;
                existingHotWater.NumberOfHotWaterSystems.EnergyStarInstantaneous = hotWater.NumberOfHotWaterSystems.EnergyStarInstantaneous;
                existingHotWater.NumberOfHotWaterSystems.Condensing = hotWater.NumberOfHotWaterSystems.Condensing;
                existingHotWater.NumberOfHotWaterSystems.Instantaneous = hotWater.NumberOfHotWaterSystems.Instantaneous;
                existingHotWater.NumberOfHotWaterSystems.HeatPumpWaterHeater = hotWater.NumberOfHotWaterSystems.HeatPumpWaterHeater;
            }
            else if (hotWater.NumberOfHotWaterSystems != null)
            {
                hotWater.NumberOfHotWaterSystems.HotWaterId = hotWater.Id;
                existingHotWater.NumberOfHotWaterSystems = hotWater.NumberOfHotWaterSystems;
            }

            await _context.SaveChangesAsync();
        }

        public async Task DeleteHotWaterSystemAsync(Guid id)
        {
            _logger.LogInformation("Deleting hot water system with ID: {Id} from repository", id);

            var hotWater = await _context.HotWaters.FindAsync(id);
            if (hotWater == null)
            {
                throw new KeyNotFoundException($"Hot water system with ID {id} not found");
            }

            _context.HotWaters.Remove(hotWater);
            await _context.SaveChangesAsync();
        }

        public async Task<bool> ExistsAsync(Guid id)
        {
            return await _context.HotWaters.AnyAsync(h => h.Id == id);
        }

        /// <summary>
        /// Helper method to set up Primary component relationships
        /// </summary>
        private void SetupPrimaryComponentRelationships(PrimaryHotWaterComponent component)
        {
            if (component.Solar != null)
            {
                if (component.Solar.Id == Guid.Empty)
                    component.Solar.Id = Guid.NewGuid();
                component.Solar.PrimaryHotWaterComponentId = component.Id;
            }

            if (component.DrainWaterHeatRecovery != null)
            {
                if (component.DrainWaterHeatRecovery.Id == Guid.Empty)
                    component.DrainWaterHeatRecovery.Id = Guid.NewGuid();
                component.DrainWaterHeatRecovery.PrimaryHotWaterComponentId = component.Id;
            }
        }

        /// <summary>
        /// Helper method to set up Secondary component relationships
        /// </summary>
        private void SetupSecondaryComponentRelationships(SecondaryHotWaterComponent component)
        {
            if (component.Solar != null)
            {
                if (component.Solar.Id == Guid.Empty)
                    component.Solar.Id = Guid.NewGuid();
                component.Solar.SecondaryHotWaterComponentId = component.Id;
            }

            if (component.DrainWaterHeatRecovery != null)
            {
                if (component.DrainWaterHeatRecovery.Id == Guid.Empty)
                    component.DrainWaterHeatRecovery.Id = Guid.NewGuid();
                component.DrainWaterHeatRecovery.SecondaryHotWaterComponentId = component.Id;
            }
        }



        /// <summary>
        /// Apply selective creation logic - ensure all entities have unique IDs before tracking
        /// Following the HvacService pattern to prevent Entity Framework tracking conflicts
        /// </summary>
        private async Task ApplySelectiveCreationLogicAsync(HotWater hotWater)
        {
            _logger.LogInformation("Applying selective creation logic for house {HouseId}", hotWater.HouseId);

            // Set IDs for the hot water system and related entities
            if (hotWater.Id == Guid.Empty)
            {
                hotWater.Id = Guid.NewGuid();
            }

            // Handle Primary component
            if (hotWater.Primary != null)
            {
                if (hotWater.Primary.Id == Guid.Empty)
                {
                    hotWater.Primary.Id = Guid.NewGuid();
                }
                hotWater.Primary.HotWaterId = hotWater.Id;

                // Set up nested relationships for Primary
                SetupPrimaryComponentRelationships(hotWater.Primary);
            }

            // Handle Secondary component
            if (hotWater.Secondary != null)
            {
                if (hotWater.Secondary.Id == Guid.Empty)
                {
                    hotWater.Secondary.Id = Guid.NewGuid();
                }
                hotWater.Secondary.HotWaterId = hotWater.Id;

                // Set up nested relationships for Secondary
                SetupSecondaryComponentRelationships(hotWater.Secondary);
            }

            // Handle NumberOfDwhrSystems
            if (hotWater.NumberOfDwhrSystems != null)
            {
                if (hotWater.NumberOfDwhrSystems.Id == Guid.Empty)
                {
                    hotWater.NumberOfDwhrSystems.Id = Guid.NewGuid();
                }
                hotWater.NumberOfDwhrSystems.HotWaterId = hotWater.Id;
            }

            // Handle NumberOfHotWaterSystems
            if (hotWater.NumberOfHotWaterSystems != null)
            {
                if (hotWater.NumberOfHotWaterSystems.Id == Guid.Empty)
                {
                    hotWater.NumberOfHotWaterSystems.Id = Guid.NewGuid();
                }
                hotWater.NumberOfHotWaterSystems.HotWaterId = hotWater.Id;
            }

            _logger.LogInformation("Selective creation logic applied for house {HouseId}", hotWater.HouseId);
        }

        /// <summary>
        /// Helper method to update PrimaryHotWaterComponent properties
        /// Following the Wall repository approach - simple property updates
        /// </summary>
        private void UpdatePrimaryComponent(PrimaryHotWaterComponent? existing, PrimaryHotWaterComponent? updated)
        {
            if (existing == null || updated == null) return;

            // Update basic properties
            existing.HasDrainWaterHeatRecovery = updated.HasDrainWaterHeatRecovery;
            existing.InsulatingBlanket = updated.InsulatingBlanket;
            existing.PilotEnergy = updated.PilotEnergy;
            existing.HeatPumpCoefficient = updated.HeatPumpCoefficient;
            existing.CombinedFlue = updated.CombinedFlue;
            existing.FlueDiameter = updated.FlueDiameter;
            existing.EnergyStar = updated.EnergyStar;
            existing.EcoEnergy = updated.EcoEnergy;
            existing.UserDefinedPilot = updated.UserDefinedPilot;
            existing.Fraction = updated.Fraction;
            existing.ConnectedUnitsDwhr = updated.ConnectedUnitsDwhr;
            existing.HasSolar = updated.HasSolar;

            // Update EquipmentInformation
            if (existing.EquipmentInformation != null && updated.EquipmentInformation != null)
            {
                existing.EquipmentInformation.Manufacturer = updated.EquipmentInformation.Manufacturer;
                existing.EquipmentInformation.Model = updated.EquipmentInformation.Model;
                existing.EquipmentInformation.Efficiency = updated.EquipmentInformation.Efficiency;
                existing.EquipmentInformation.EfficiencyUnits = updated.EquipmentInformation.EfficiencyUnits;
                existing.EquipmentInformation.SteadyStateEfficiency = updated.EquipmentInformation.SteadyStateEfficiency;
                existing.EquipmentInformation.MotorEfficiency = updated.EquipmentInformation.MotorEfficiency;
                existing.EquipmentInformation.FanPower = updated.EquipmentInformation.FanPower;
                existing.EquipmentInformation.LowSpeedFanPower = updated.EquipmentInformation.LowSpeedFanPower;
                existing.EquipmentInformation.HighSpeedFanPower = updated.EquipmentInformation.HighSpeedFanPower;
                existing.EquipmentInformation.LowSpeedAirFlow = updated.EquipmentInformation.LowSpeedAirFlow;
                existing.EquipmentInformation.HighSpeedAirFlow = updated.EquipmentInformation.HighSpeedAirFlow;
                existing.EquipmentInformation.ElectricalInputPower = updated.EquipmentInformation.ElectricalInputPower;
                existing.EquipmentInformation.OutputCapacity = updated.EquipmentInformation.OutputCapacity;
                existing.EquipmentInformation.EnergyInputRatio = updated.EquipmentInformation.EnergyInputRatio;
                existing.EquipmentInformation.CoolingCapacity = updated.EquipmentInformation.CoolingCapacity;
                existing.EquipmentInformation.CoolingEfficiency = updated.EquipmentInformation.CoolingEfficiency;
                existing.EquipmentInformation.CoolingElectricalInputPower = updated.EquipmentInformation.CoolingElectricalInputPower;
            }

            // Update resource properties
            if (updated.EnergySource != null)
            {
                existing.EnergySource = updated.EnergySource;
            }

            if (updated.TankVolume != null)
            {
                existing.TankVolume = updated.TankVolume;
            }

            existing.DrawPattern = updated.DrawPattern;

            if (updated.TankLocation != null)
            {
                existing.TankLocation = updated.TankLocation;
            }

            // Update EnergyFactor
            if (existing.EnergyFactor != null && updated.EnergyFactor != null)
            {
                existing.EnergyFactor.Code = updated.EnergyFactor.Code;
                existing.EnergyFactor.EnglishText = updated.EnergyFactor.EnglishText;
                existing.EnergyFactor.FrenchText = updated.EnergyFactor.FrenchText;
                existing.EnergyFactor.Value = updated.EnergyFactor.Value;
                existing.EnergyFactor.IsUniform = updated.EnergyFactor.IsUniform;
                existing.EnergyFactor.IsUserSpecified = updated.EnergyFactor.IsUserSpecified;
            }

            // Update TankTypeData
            if (existing.TankTypeData != null && updated.TankTypeData != null)
            {
                existing.TankTypeData.Code = updated.TankTypeData.Code;
                existing.TankTypeData.EnglishText = updated.TankTypeData.EnglishText;
                existing.TankTypeData.FrenchText = updated.TankTypeData.FrenchText;
            }

            // Update Solar component
            if (existing.Solar != null && updated.Solar != null)
            {
                existing.Solar.Rating = updated.Solar.Rating;
                existing.Solar.Slope = updated.Solar.Slope;
                existing.Solar.Azimuth = updated.Solar.Azimuth;
            }
            else if (updated.Solar != null)
            {
                existing.Solar = updated.Solar;
                existing.Solar.PrimaryHotWaterComponentId = existing.Id;
            }

            // Update DrainWaterHeatRecovery component
            if (existing.DrainWaterHeatRecovery != null && updated.DrainWaterHeatRecovery != null)
            {
                UpdateDrainWaterHeatRecovery(existing.DrainWaterHeatRecovery, updated.DrainWaterHeatRecovery);
            }
            else if (updated.DrainWaterHeatRecovery != null)
            {
                existing.DrainWaterHeatRecovery = updated.DrainWaterHeatRecovery;
                existing.DrainWaterHeatRecovery.PrimaryHotWaterComponentId = existing.Id;
            }
        }

        /// <summary>
        /// Helper method to update SecondaryHotWaterComponent properties
        /// Following the Wall repository approach - simple property updates
        /// </summary>
        private void UpdateSecondaryComponent(SecondaryHotWaterComponent? existing, SecondaryHotWaterComponent? updated)
        {
            if (existing == null || updated == null) return;

            // Update basic properties
            existing.HasDrainWaterHeatRecovery = updated.HasDrainWaterHeatRecovery;
            existing.InsulatingBlanket = updated.InsulatingBlanket;
            existing.PilotEnergy = updated.PilotEnergy;
            existing.HeatPumpCoefficient = updated.HeatPumpCoefficient;
            existing.CombinedFlue = updated.CombinedFlue;
            existing.Fraction = updated.Fraction;
            existing.HasSolar = updated.HasSolar;

            // Update EquipmentInformation
            if (existing.EquipmentInformation != null && updated.EquipmentInformation != null)
            {
                existing.EquipmentInformation.Manufacturer = updated.EquipmentInformation.Manufacturer;
                existing.EquipmentInformation.Model = updated.EquipmentInformation.Model;
                existing.EquipmentInformation.Efficiency = updated.EquipmentInformation.Efficiency;
                existing.EquipmentInformation.EfficiencyUnits = updated.EquipmentInformation.EfficiencyUnits;
                existing.EquipmentInformation.SteadyStateEfficiency = updated.EquipmentInformation.SteadyStateEfficiency;
                existing.EquipmentInformation.MotorEfficiency = updated.EquipmentInformation.MotorEfficiency;
                existing.EquipmentInformation.FanPower = updated.EquipmentInformation.FanPower;
                existing.EquipmentInformation.LowSpeedFanPower = updated.EquipmentInformation.LowSpeedFanPower;
                existing.EquipmentInformation.HighSpeedFanPower = updated.EquipmentInformation.HighSpeedFanPower;
                existing.EquipmentInformation.LowSpeedAirFlow = updated.EquipmentInformation.LowSpeedAirFlow;
                existing.EquipmentInformation.HighSpeedAirFlow = updated.EquipmentInformation.HighSpeedAirFlow;
                existing.EquipmentInformation.ElectricalInputPower = updated.EquipmentInformation.ElectricalInputPower;
                existing.EquipmentInformation.OutputCapacity = updated.EquipmentInformation.OutputCapacity;
                existing.EquipmentInformation.EnergyInputRatio = updated.EquipmentInformation.EnergyInputRatio;
                existing.EquipmentInformation.CoolingCapacity = updated.EquipmentInformation.CoolingCapacity;
                existing.EquipmentInformation.CoolingEfficiency = updated.EquipmentInformation.CoolingEfficiency;
                existing.EquipmentInformation.CoolingElectricalInputPower = updated.EquipmentInformation.CoolingElectricalInputPower;
            }

            // Update resource properties
            if (updated.EnergySource != null)
            {
                existing.EnergySource = updated.EnergySource;
            }

            if (updated.TankVolume != null)
            {
                existing.TankVolume = updated.TankVolume;
            }

            existing.DrawPattern = updated.DrawPattern;

            if (updated.TankLocation != null)
            {
                existing.TankLocation = updated.TankLocation;
            }

            // Update EnergyFactor
            if (existing.EnergyFactor != null && updated.EnergyFactor != null)
            {
                existing.EnergyFactor.Code = updated.EnergyFactor.Code;
                existing.EnergyFactor.EnglishText = updated.EnergyFactor.EnglishText;
                existing.EnergyFactor.FrenchText = updated.EnergyFactor.FrenchText;
                existing.EnergyFactor.Value = updated.EnergyFactor.Value;
                existing.EnergyFactor.IsUniform = updated.EnergyFactor.IsUniform;
                existing.EnergyFactor.IsUserSpecified = updated.EnergyFactor.IsUserSpecified;
            }

            // Update TankTypeData
            if (existing.TankTypeData != null && updated.TankTypeData != null)
            {
                existing.TankTypeData.Code = updated.TankTypeData.Code;
                existing.TankTypeData.EnglishText = updated.TankTypeData.EnglishText;
                existing.TankTypeData.FrenchText = updated.TankTypeData.FrenchText;
            }

            // Update Solar component
            if (existing.Solar != null && updated.Solar != null)
            {
                existing.Solar.Rating = updated.Solar.Rating;
                existing.Solar.Slope = updated.Solar.Slope;
                existing.Solar.Azimuth = updated.Solar.Azimuth;
            }
            else if (updated.Solar != null)
            {
                existing.Solar = updated.Solar;
                existing.Solar.SecondaryHotWaterComponentId = existing.Id;
            }

            // Update DrainWaterHeatRecovery component
            if (existing.DrainWaterHeatRecovery != null && updated.DrainWaterHeatRecovery != null)
            {
                UpdateDrainWaterHeatRecovery(existing.DrainWaterHeatRecovery, updated.DrainWaterHeatRecovery);
            }
            else if (updated.DrainWaterHeatRecovery != null)
            {
                existing.DrainWaterHeatRecovery = updated.DrainWaterHeatRecovery;
                existing.DrainWaterHeatRecovery.SecondaryHotWaterComponentId = existing.Id;
            }
        }

        /// <summary>
        /// Helper method to update DrainWaterHeatRecovery properties
        /// </summary>
        private void UpdateDrainWaterHeatRecovery(DrainWaterHeatRecovery existing, DrainWaterHeatRecovery updated)
        {
            // Update basic properties
            existing.ShowerLength = updated.ShowerLength;
            existing.DailyShowers = updated.DailyShowers;
            existing.PreheatShowerTank = updated.PreheatShowerTank;
            existing.Effectiveness = updated.Effectiveness;

            // Update EquipmentInformation
            if (existing.EquipmentInformation != null && updated.EquipmentInformation != null)
            {
                existing.EquipmentInformation.Manufacturer = updated.EquipmentInformation.Manufacturer;
                existing.EquipmentInformation.Model = updated.EquipmentInformation.Model;
            }

            // Update Efficiency
            if (existing.Efficiency != null && updated.Efficiency != null)
            {
                existing.Efficiency.Code = updated.Efficiency.Code;
                existing.Efficiency.EnglishText = updated.Efficiency.EnglishText;
                existing.Efficiency.FrenchText = updated.Efficiency.FrenchText;
            }

            // Update resource properties
            if (updated.ShowerTemperature != null)
            {
                existing.ShowerTemperature = updated.ShowerTemperature;
            }

            if (updated.ShowerHead != null)
            {
                existing.ShowerHead = updated.ShowerHead;
            }
        }

        /// <summary>
        /// Gets hot water system by energy upgrade ID
        /// </summary>
        public async Task<HotWater?> GetHotWaterByEnergyUpgradeIdAsync(Guid energyUpgradeId)
        {
            _logger.LogInformation("Getting hot water system by energy upgrade ID: {EnergyUpgradeId}", energyUpgradeId);

            return await _context.HotWaters
                .Include(h => h.Primary)
                    .ThenInclude(p => p.EquipmentInformation)
                .Include(h => h.Primary)
                    .ThenInclude(p => p.EnergySource)
                .Include(h => h.Primary)
                    .ThenInclude(p => p.TankVolume)
                .Include(h => h.Primary)
                    .ThenInclude(p => p.DrawPattern)
                .Include(h => h.Primary)
                    .ThenInclude(p => p.TankLocation)
                .Include(h => h.Primary)
                    .ThenInclude(p => p.Solar)
                .Include(h => h.Primary)
                    .ThenInclude(p => p.DrainWaterHeatRecovery)
                        .ThenInclude(d => d.EquipmentInformation)
                .Include(h => h.Primary)
                    .ThenInclude(p => p.DrainWaterHeatRecovery)
                        .ThenInclude(d => d.ShowerTemperature)
                .Include(h => h.Primary)
                    .ThenInclude(p => p.DrainWaterHeatRecovery)
                        .ThenInclude(d => d.ShowerHead)
                .Include(h => h.Secondary)
                    .ThenInclude(s => s.EquipmentInformation)
                .Include(h => h.Secondary)
                    .ThenInclude(s => s.EnergySource)
                .Include(h => h.Secondary)
                    .ThenInclude(s => s.TankVolume)
                .Include(h => h.Secondary)
                    .ThenInclude(s => s.DrawPattern)
                .Include(h => h.Secondary)
                    .ThenInclude(s => s.TankLocation)
                .Include(h => h.Secondary)
                    .ThenInclude(s => s.Solar)
                .Include(h => h.Secondary)
                    .ThenInclude(s => s.DrainWaterHeatRecovery)
                        .ThenInclude(d => d.EquipmentInformation)
                .Include(h => h.Secondary)
                    .ThenInclude(s => s.DrainWaterHeatRecovery)
                        .ThenInclude(d => d.ShowerTemperature)
                .Include(h => h.Secondary)
                    .ThenInclude(s => s.DrainWaterHeatRecovery)
                        .ThenInclude(d => d.ShowerHead)
                .Include(h => h.NumberOfDwhrSystems)
                .Include(h => h.NumberOfHotWaterSystems)
                .FirstOrDefaultAsync(hw => hw.EnergyUpgradeId == energyUpgradeId);
        }

        /// <summary>
        /// Duplicates a hot water system for energy upgrade
        /// </summary>
        public async Task<HotWater> DuplicateHotWaterForEnergyUpgradeAsync(HotWater baseHotWater, Guid energyUpgradeId)
        {
            _logger.LogInformation("Duplicating hot water system {BaseId} for energy upgrade {EnergyUpgradeId}",
                baseHotWater.Id, energyUpgradeId);

            // Create a deep copy of the hot water system
            var duplicatedHotWater = new HotWater
            {
                Id = Guid.NewGuid(),
                HouseId = baseHotWater.HouseId,
                EnergyUpgradeId = energyUpgradeId,
                Label = baseHotWater.Label
            };

            // Deep copy Primary component
            if (baseHotWater.Primary != null)
            {
                duplicatedHotWater.Primary = DuplicatePrimaryComponent(baseHotWater.Primary, duplicatedHotWater.Id);
            }

            // Deep copy Secondary component
            if (baseHotWater.Secondary != null)
            {
                duplicatedHotWater.Secondary = DuplicateSecondaryComponent(baseHotWater.Secondary, duplicatedHotWater.Id);
            }

            // Deep copy NumberOfDwhrSystems
            if (baseHotWater.NumberOfDwhrSystems != null)
            {
                duplicatedHotWater.NumberOfDwhrSystems = new NumberOfDwhrSystems
                {
                    Id = Guid.NewGuid(),
                    HotWaterId = duplicatedHotWater.Id,
                    LowEfficiency = baseHotWater.NumberOfDwhrSystems.LowEfficiency,
                    HighEfficiency = baseHotWater.NumberOfDwhrSystems.HighEfficiency
                };
            }

            // Deep copy NumberOfHotWaterSystems
            if (baseHotWater.NumberOfHotWaterSystems != null)
            {
                duplicatedHotWater.NumberOfHotWaterSystems = new NumberOfHotWaterSystems
                {
                    Id = Guid.NewGuid(),
                    HotWaterId = duplicatedHotWater.Id,
                    EnergyStarInstantaneousCondensing = baseHotWater.NumberOfHotWaterSystems.EnergyStarInstantaneousCondensing,
                    EnergyStarInstantaneous = baseHotWater.NumberOfHotWaterSystems.EnergyStarInstantaneous,
                    Condensing = baseHotWater.NumberOfHotWaterSystems.Condensing,
                    Instantaneous = baseHotWater.NumberOfHotWaterSystems.Instantaneous,
                    HeatPumpWaterHeater = baseHotWater.NumberOfHotWaterSystems.HeatPumpWaterHeater
                };
            }

            // Add to context and save
            _context.HotWaters.Add(duplicatedHotWater);
            await _context.SaveChangesAsync();

            _logger.LogInformation("Successfully duplicated hot water system with new ID: {Id}", duplicatedHotWater.Id);
            return duplicatedHotWater;
        }

        private PrimaryHotWaterComponent DuplicatePrimaryComponent(PrimaryHotWaterComponent source, Guid newHotWaterId)
        {
            var duplicated = new PrimaryHotWaterComponent
            {
                Id = Guid.NewGuid(),
                HotWaterId = newHotWaterId,
                HasDrainWaterHeatRecovery = source.HasDrainWaterHeatRecovery,
                InsulatingBlanket = source.InsulatingBlanket,
                PilotEnergy = source.PilotEnergy,
                HeatPumpCoefficient = source.HeatPumpCoefficient,
                CombinedFlue = source.CombinedFlue,
                FlueDiameter = source.FlueDiameter,
                EnergyStar = source.EnergyStar,
                EcoEnergy = source.EcoEnergy,
                UserDefinedPilot = source.UserDefinedPilot,
                Fraction = source.Fraction,
                ConnectedUnitsDwhr = source.ConnectedUnitsDwhr,
                HasSolar = source.HasSolar
            };

            // Copy owned entities
            if (source.EquipmentInformation != null)
            {
                duplicated.EquipmentInformation = new EquipmentInformation
                {
                    Manufacturer = source.EquipmentInformation.Manufacturer,
                    Model = source.EquipmentInformation.Model,
                    Description = source.EquipmentInformation.Description
                };
            }

            if (source.TankTypeData != null)
            {
                duplicated.TankTypeData = new CodeAndText
                {
                    Code = source.TankTypeData.Code,
                    Text = source.TankTypeData.Text
                };
            }

            if (source.EnergyFactor != null)
            {
                duplicated.EnergyFactor = new EnergyFactor(source.EnergyFactor);
            }

            // Copy resource properties
            duplicated.EnergySource = source.EnergySource;
            duplicated.TankVolume = source.TankVolume;
            duplicated.DrawPattern = source.DrawPattern;
            duplicated.TankLocation = source.TankLocation;

            return duplicated;
        }

        private SecondaryHotWaterComponent DuplicateSecondaryComponent(SecondaryHotWaterComponent source, Guid newHotWaterId)
        {
            var duplicated = new SecondaryHotWaterComponent
            {
                Id = Guid.NewGuid(),
                HotWaterId = newHotWaterId,
                HasDrainWaterHeatRecovery = source.HasDrainWaterHeatRecovery,
                InsulatingBlanket = source.InsulatingBlanket,
                PilotEnergy = source.PilotEnergy,
                HeatPumpCoefficient = source.HeatPumpCoefficient,
                CombinedFlue = source.CombinedFlue,
                Fraction = source.Fraction,
                HasSolar = source.HasSolar
            };

            // Copy owned entities
            if (source.EquipmentInformation != null)
            {
                duplicated.EquipmentInformation = new EquipmentInformation
                {
                    Manufacturer = source.EquipmentInformation.Manufacturer,
                    Model = source.EquipmentInformation.Model,
                    Description = source.EquipmentInformation.Description
                };
            }

            if (source.TankTypeData != null)
            {
                duplicated.TankTypeData = new CodeAndText
                {
                    Code = source.TankTypeData.Code,
                    Text = source.TankTypeData.Text
                };
            }

            if (source.EnergyFactor != null)
            {
                duplicated.EnergyFactor = new EnergyFactor(source.EnergyFactor);
            }

            // Copy resource properties
            duplicated.EnergySource = source.EnergySource;
            duplicated.TankVolume = source.TankVolume;
            duplicated.DrawPattern = source.DrawPattern;
            duplicated.TankLocation = source.TankLocation;

            return duplicated;
        }
    }
}