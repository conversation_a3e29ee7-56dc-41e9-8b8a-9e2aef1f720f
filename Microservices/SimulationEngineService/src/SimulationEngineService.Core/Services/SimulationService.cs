using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using SimulationEngineService.Core.Interfaces;
using SimulationEngineService.Core.Models;
using SimulationEngineService.Core.Exceptions;
using Hangfire;
using System.Text.Json;

namespace SimulationEngineService.Core.Services
{
    public class SimulationService : ISimulationService
    {
        private readonly ISimulationRepository _repository;
        private readonly IHouseFileClient _houseFileClient;
        private readonly IWeatherServiceClient _weatherServiceClient;
        private readonly IBaseLoadServiceClient _baseloadServiceClient;
        private readonly IEnvelopeServiceClient _envelopeServiceClient;
        private readonly IHvacServiceClient _hvacServiceClient;
        private readonly IHotWaterServiceClient _hotWaterServiceClient;
        private readonly ITemperatureServiceClient _temperatureServiceClient;
        private readonly IAirInfiltrationServiceClient _airInfiltrationServiceClient;
        private readonly IGenerationServiceClient _generationServiceClient;
        private readonly ISimulationEngine _simulationEngine;
        private readonly ILogger<SimulationService> _logger;

        public SimulationService(
            ISimulationRepository repository,
            IHouseFileClient houseFileClient,
            IWeatherServiceClient weatherServiceClient,
            IBaseLoadServiceClient baseloadServiceClient,
            IEnvelopeServiceClient envelopeServiceClient,
            IHvacServiceClient hvacServiceClient,
            IHotWaterServiceClient hotWaterServiceClient,
            ITemperatureServiceClient temperatureServiceClient,
            IAirInfiltrationServiceClient airInfiltrationServiceClient,
            IGenerationServiceClient generationServiceClient,
            ISimulationEngine simulationEngine,
            ILogger<SimulationService> logger)
        {
            _repository = repository ?? throw new ArgumentNullException(nameof(repository));
            _houseFileClient = houseFileClient ?? throw new ArgumentNullException(nameof(houseFileClient));
            _weatherServiceClient = weatherServiceClient ?? throw new ArgumentNullException(nameof(weatherServiceClient));
            _baseloadServiceClient = baseloadServiceClient ?? throw new ArgumentNullException(nameof(baseloadServiceClient));
            _envelopeServiceClient = envelopeServiceClient ?? throw new ArgumentNullException(nameof(envelopeServiceClient));
            _hvacServiceClient = hvacServiceClient ?? throw new ArgumentNullException(nameof(hvacServiceClient));
            _hotWaterServiceClient = hotWaterServiceClient ?? throw new ArgumentNullException(nameof(hotWaterServiceClient));
            _temperatureServiceClient = temperatureServiceClient ?? throw new ArgumentNullException(nameof(temperatureServiceClient));
            _airInfiltrationServiceClient = airInfiltrationServiceClient ?? throw new ArgumentNullException(nameof(airInfiltrationServiceClient));
            _generationServiceClient = generationServiceClient ?? throw new ArgumentNullException(nameof(generationServiceClient));
            _simulationEngine = simulationEngine ?? throw new ArgumentNullException(nameof(simulationEngine));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        }
        
        public async Task<IEnumerable<Simulation>> GetAllSimulationsAsync()
        {
            return await _repository.GetAllSimulationsAsync();
        }
        
        public async Task<Simulation?> GetSimulationByIdAsync(Guid id)
        {
            return await _repository.GetSimulationByIdAsync(id);
        }
        
        public async Task<IEnumerable<Simulation>> GetSimulationsByHouseIdAsync(Guid houseId)
        {
            return await _repository.GetSimulationsByHouseIdAsync(houseId);
        }
        
        public async Task<bool> DeleteSimulationAsync(Guid id)
        {
            return await _repository.DeleteSimulationAsync(id);
        }

        public async Task<SimulationResult?> GetSimulationResultByHouseIdAsync(Guid houseId)
        {
            _logger.LogInformation("Getting simulation result for house ID: {HouseId}", houseId);
            return await _repository.GetSimulationResultByHouseIdAsync(houseId);
        }
        
        public async Task<SimulationResult?> GetSimulationResultAsync(Guid simulationId)
        {
            return await _repository.GetSimulationResultAsync(simulationId);
        }
        
        public async Task<SimulationResult> CreateAndProcessByHouseIdAsync(Guid houseId, SimulationParameters parameters)
        {
            _logger.LogInformation("Creating and processing simulation for house ID: {HouseId}", houseId);
            
            try
            {
                // Create a simulation record
                var simulation = new Simulation
                {
                    Id = Guid.NewGuid(),
                    Name = $"Simulation_{DateTime.UtcNow:yyyyMMdd_HHmmss}",
                    Status = SimulationStatus.InProgress,
                    CreatedDate = DateTime.UtcNow,
                    StartedDate = DateTime.UtcNow,
                    HouseId = houseId,
                    Parameters = parameters
                };
                
                // Save to database
                await _repository.CreateSimulationAsync(simulation);
                
                // Check if stored data exists for this house
                if (!await _repository.ExistsAsync(houseId))
                {
                    // If not, we need to fetch all the data first
                    var houseData = await _houseFileClient.GetHouseAsync(houseId);
                    var weatherData = await _weatherServiceClient.GetWeatherDataByHouseId(houseId);
                    var baseloadData = await _baseloadServiceClient.GetBaseLoadsByHouseId(houseId);
                    
                    // CHANGED: Now returns List<EnvelopWallData> instead of single EnvelopWallData
                    var wallsData = await _envelopeServiceClient.GetWalls(houseId);
                    var envelopCeilingData = await _envelopeServiceClient.GetCeilings(houseId);
                    var envelopFloorData = await _envelopeServiceClient.GetFloors(houseId);
                    var floorHeadersData = await _envelopeServiceClient.GetFloorHeaders(houseId);
                    var doorsData = await _envelopeServiceClient.GetDoors(houseId);
                    var windowsData = await _envelopeServiceClient.GetWindows(houseId);
                    var hvacData = await _hvacServiceClient.GetHeatingCoolingByHouseIdAsync(houseId);
                    var hotWaterData = await _hotWaterServiceClient.GetHotWaterDataAsync(houseId);
                    var temperatureData = await _temperatureServiceClient.GetTemperatureByHouseIdAsync(houseId);
                    var airInfiltrationData = await _airInfiltrationServiceClient.GetAirInfiltrationByHouseIdAsync(houseId);
                    var generationData = await _generationServiceClient.GetGenerationByHouseIdAsync(houseId);

                    // Validate that we have the required data
                    if (houseData == null)
                    {
                        throw new SimulationException("House data not found");
                    }
                    
                    if (wallsData == null || !wallsData.Any())
                    {
                        _logger.LogWarning("No wall data found for house {HouseId}", houseId);
                        // You can either throw an exception or continue with empty walls
                        // throw new SimulationException("No wall data found for the house");
                    }
                    
                    if (envelopCeilingData == null || !envelopCeilingData.Any())
                    {
                        _logger.LogWarning("No ceiling data found for house {HouseId}", houseId);
                    }
                    
                    if (envelopFloorData == null || !envelopFloorData.Any())
                    {
                        _logger.LogWarning("No floor data found for house {HouseId}", houseId);
                    }

                    if (doorsData == null || !doorsData.Any())
                    {
                        _logger.LogWarning("No door data found for house {HouseId}", houseId);
                    }

                    if (windowsData == null || !windowsData.Any())
                    {
                        _logger.LogWarning("No window data found for house {HouseId}", houseId);
                    }

                    if (hvacData == null)
                    {
                        _logger.LogWarning("No HVAC data found for house {HouseId}", houseId);
                    }

                    // Store the data
                    await _simulationEngine.StoreSimulationDataAsync(
                        houseData,
                        weatherData,
                        baseloadData,
                        wallsData ?? new List<EnvelopWallData>(), // CHANGED: Pass List<EnvelopWallData>
                        envelopCeilingData ?? new List<EnvelopCeilingData>(), // FIXED: Corrected casing
                        envelopFloorData ?? new List<EnvelopFloorData>(),
                        floorHeadersData ?? new List<EnvelopFloorHeaderData>(),
                        doorsData ?? new List<EnvelopDoorData>(),
                        windowsData ?? new List<EnvelopWindowData>(),
                        hvacData,
                        hotWaterData,
                        temperatureData,
                        airInfiltrationData,
                        generationData,
                        parameters
                    );
                    
                    _logger.LogInformation("Successfully stored simulation data for house {HouseId} with {WallCount} walls, {CeilingCount} ceilings, {FloorCount} floors, {DoorCount} doors, {WindowCount} windows, HVAC data: {HasHvac}, HotWater data: {HasHotWater}, Generation data: {HasGeneration}",
                        houseId, wallsData?.Count ?? 0, envelopCeilingData?.Count ?? 0, envelopFloorData?.Count ?? 0, doorsData?.Count ?? 0, windowsData?.Count ?? 0, hvacData != null ? "Yes" : "No", hotWaterData != null ? "Yes" : "No", generationData != null ? "Yes" : "No");
                }
                
                // Run the simulation
                var result = await _simulationEngine.RunSimulationByHouseIdAsync(houseId);
                
                // Update result with the correct simulation ID
                result.SimulationId = simulation.Id;
                result.HouseId = simulation.HouseId;
                
                // Save result
                await _repository.SaveSimulationResultAsync(result);
                
                // Update simulation status
                simulation.Status = SimulationStatus.Completed;
                simulation.CompletedDate = DateTime.UtcNow;
                await _repository.UpdateSimulationAsync(simulation);
                
                _logger.LogInformation("Successfully completed simulation for house {HouseId} with result ID {ResultId}", 
                    houseId, result.Id);
                
                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in create and process by house ID: {Message}", ex.Message);
                
                // Try to update simulation status to failed if we have the simulation record
                try
                {
                    var failedSimulations = await _repository.GetSimulationsByHouseIdAsync(houseId);
                    var latestSimulation = failedSimulations
                        .OrderByDescending(s => s.CreatedDate)
                        .FirstOrDefault();
                    
                    if (latestSimulation != null && latestSimulation.Status == SimulationStatus.InProgress)
                    {
                        latestSimulation.Status = SimulationStatus.Failed;
                        latestSimulation.CompletedDate = DateTime.UtcNow;
                        await _repository.UpdateSimulationAsync(latestSimulation);
                    }
                }
                catch (Exception updateEx)
                {
                    _logger.LogError(updateEx, "Failed to update simulation status to failed for house {HouseId}", houseId);
                }
                
                throw new SimulationException("Failed to create and process simulation", ex);
            }
        }
        
        public async Task<SimulationStatus> GetSimulationStatusAsync(Guid simulationId)
        {
            var simulation = await _repository.GetSimulationByIdAsync(simulationId);
            return simulation?.Status ?? SimulationStatus.Failed;
        }

        public async Task<Simulation?> GetSimulationByEnergyUpgradeIdAsync(Guid energyUpgradeId)
        {
            return await _repository.GetSimulationByEnergyUpgradeIdAsync(energyUpgradeId);
        }

        public async Task<Simulation> DuplicateSimulationForEnergyUpgradeAsync(Simulation baseSimulation, Guid energyUpgradeId)
        {
            return await _repository.DuplicateSimulationForEnergyUpgradeAsync(baseSimulation, energyUpgradeId);
        }
    }
}