using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using SimulationEngineService.Core.Models;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;

namespace SimulationEngineService.Core.Interfaces
{
    public interface ISimulationRepository
    {
        Task<IEnumerable<Simulation>> GetAllSimulationsAsync();
        Task<Simulation?> GetSimulationByIdAsync(Guid id);
        Task<IEnumerable<Simulation>> GetSimulationsByHouseIdAsync(Guid houseId);
        Task<Simulation> CreateSimulationAsync(Simulation simulation);
        Task<Simulation> UpdateSimulationAsync(Simulation simulation);
        Task<bool> DeleteSimulationAsync(Guid id);
        Task<bool> StoreAsync(Guid houseId, SimulationDataPackage dataPackage);
        Task<SimulationDataPackage> GetByHouseIdAsync(Guid houseId);
        Task<bool> ExistsAsync(Guid houseId);
        Task<SimulationResult?> GetSimulationResultAsync(Guid simulationId);
        Task<SimulationResult> SaveSimulationResultAsync(SimulationResult result);
        Task<SimulationResult?> GetSimulationResultByHouseIdAsync(Guid houseId);

        // Energy upgrade operations
        Task<Simulation?> GetSimulationByEnergyUpgradeIdAsync(Guid energyUpgradeId);
        Task<Simulation> DuplicateSimulationForEnergyUpgradeAsync(Simulation baseSimulation, Guid energyUpgradeId);
    }
}