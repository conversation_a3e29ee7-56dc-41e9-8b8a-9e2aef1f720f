using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using SimulationEngineService.Core.Models;

namespace SimulationEngineService.Core.Interfaces
{
    public interface ISimulationService
    {
        Task<IEnumerable<Simulation>> GetAllSimulationsAsync();
        Task<Simulation?> GetSimulationByIdAsync(Guid id);
        Task<IEnumerable<Simulation>> GetSimulationsByHouseIdAsync(Guid houseId);
        Task<bool> DeleteSimulationAsync(Guid id);
        
        Task<SimulationResult?> GetSimulationResultAsync(Guid simulationId);
        Task<SimulationResult?> GetSimulationResultByHouseIdAsync(Guid houseId);
        
        // Main simulation methods
        Task<SimulationStatus> GetSimulationStatusAsync(Guid simulationId);
        
        // Background job processing
        Task<SimulationResult> CreateAndProcessByHouseIdAsync(Guid houseId, SimulationParameters parameters);

        // Energy upgrade operations
        Task<Simulation?> GetSimulationByEnergyUpgradeIdAsync(Guid energyUpgradeId);
        Task<Simulation> DuplicateSimulationForEnergyUpgradeAsync(Simulation baseSimulation, Guid energyUpgradeId);
    }
}