
// 2. Updated EnvelopWallData model to match your exact WallDto - ESSENTIAL
using System;
using System.Collections.Generic;
using System.Text.Json.Serialization;

namespace SimulationEngineService.Core.Models
{
    public class EnvelopWallData
    {
        public Guid Id { get; set; }
        public Guid HouseId { get; set; }
        public Guid? EnergyUpgradeId { get; set; }
        public string Label { get; set; } = string.Empty; // From ComponentDto
        public bool AdjacentEnclosedSpace { get; set; }
        public WallDirectionsData FacingDirection { get; set; } = new WallDirectionsData();

        public WallConstructionData Construction { get; set; } = new WallConstructionData();
        public WallMeasurementsData Measurements { get; set; } = new WallMeasurementsData();
    }

    public class WallDirectionsData
    {
        public string Code { get; set; } = string.Empty;
        public string English { get; set; } = string.Empty;
        public string French { get; set; } = string.Empty;
        public bool IsUserSpecified { get; set; } = false;
    }

    public class WallConstructionData
    {
        public Guid Id { get; set; } // From ConstructionDto
        public Guid WallId { get; set; } // From WallConstructionDto
        public decimal Corners { get; set; } // From WallConstructionDto
        public decimal Intersections { get; set; } // From WallConstructionDto
        
        public CodeReferenceData Type { get; set; } = new CodeReferenceData(); // From ConstructionDto
        public CodeReferenceData LintelType { get; set; } = new CodeReferenceData(); // From WallConstructionDto
    }

    public class CodeReferenceData
    {
        public Guid Id { get; set; }
        public string IdRef { get; set; } = string.Empty;
        public string Code { get; set; } = string.Empty;
        public decimal RValue { get; set; }
        public decimal NominalInsulation { get; set; }
        public string Text { get; set; } = string.Empty;
        public List<UserDefinedLayerData> UserDefinedCodeLayers { get; set; } = new List<UserDefinedLayerData>();
    }

    public class UserDefinedLayerData
    {
        public Guid Id { get; set; }
        public Guid CodeReferenceId { get; set; }
        public int Rank { get; set; }
        public string LayerType { get; set; } = string.Empty;
    }

    public class WallMeasurementsData
    {
        public Guid Id { get; set; } // From WallMeasurementsDto
        public Guid WallId { get; set; } // From WallMeasurementsDto
        public decimal Height { get; set; } // From WallMeasurementsDto
        public decimal Perimeter { get; set; } // From WallMeasurementsDto
        
        // Calculate Area property (not in your API, but needed for simulation)
        public decimal Area => Height * Perimeter;
    }
}
