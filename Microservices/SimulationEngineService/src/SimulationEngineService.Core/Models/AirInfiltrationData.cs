using System;
using System.ComponentModel.DataAnnotations;

namespace SimulationEngineService.Core.Models
{
    /// <summary>
    /// Represents air infiltration system data for simulation - matches AirInfiltrationService.Core.Models.NaturalAirInfiltration
    /// </summary>
    public class AirInfiltrationData
    {
        public Guid Id { get; set; }

        [Required]
        public Guid HouseId { get; set; }

        public Guid? EnergyUpgradeId { get; set; }

        public string Label { get; set; } = "NaturalAirInfiltration";

        // Navigation properties matching AirInfiltrationService structure exactly
        public NaturalAirSpecificationsData? Specifications { get; set; }
        public OtherFactorsData? OtherFactors { get; set; }
        public AirLeakageTestData? AirLeakageTestData { get; set; }
    }

    public class NaturalAirSpecificationsData
    {
        public Guid Id { get; set; }
        public Guid NaturalAirInfiltrationId { get; set; }
        public SpecificationsHouseData? House { get; set; }
        public BlowerTestData? BlowerTest { get; set; }
        public BuildingSiteData? BuildingSite { get; set; }
        public LocalShieldingData? LocalShielding { get; set; }
        public ExhaustDevicesTestData? ExhaustDevicesTest { get; set; }
        public CommonSurfaceAreaData? CommonSurfaceArea { get; set; }
    }

    public class SpecificationsHouseData
    {
        public decimal Volume { get; set; }
        public bool IncludeCrawlspaceVolume { get; set; }
        public AirTightnessTypesData? AirTightnessTest { get; set; }
    }

    public class BlowerTestData
    {
        public bool AirLeakageTestData { get; set; }
        public decimal AirChangeRate { get; set; }
        public bool IsCgsbTest { get; set; }
        public bool IsCalculated { get; set; }
        public decimal LeakageArea { get; set; }
        public bool Guarded { get; set; }
        public BlowerTestPressuresData? Pressure { get; set; }
    }

    public class BuildingSiteData
    {
        public decimal HighestCeiling { get; set; }
        public TerrainsData? Terrain { get; set; }
    }

    public class LocalShieldingData
    {
        public LocalShieldingsData? Walls { get; set; }
        public LocalShieldingsData? Flue { get; set; }
    }

    public class ExhaustDevicesTestData
    {
        public decimal Result { get; set; }
        public TestStatusesData? TestStatus { get; set; }
    }

    public class CommonSurfaceAreaData
    {
        public decimal SurfaceArea { get; set; }
    }

    public class OtherFactorsData
    {
        public Guid Id { get; set; }
        public Guid NaturalAirInfiltrationId { get; set; }
        public WeatherStationData? WeatherStation { get; set; }
        public LeakageFractionsData? LeakageFractions { get; set; }
    }

    public class WeatherStationData
    {
        public decimal AnemometerHeight { get; set; }
        public TerrainsData? Terrain { get; set; }
    }

    public class LeakageFractionsData
    {
        public bool UseDefaults { get; set; }
        public decimal Ceilings { get; set; }
        public decimal Walls { get; set; }
        public decimal Floors { get; set; }
    }

    public class AirLeakageTestData
    {
        public Guid Id { get; set; }
        public Guid NaturalAirInfiltrationId { get; set; }
        public bool HasCgsbConditions { get; set; }
        public decimal OutsideTemperature { get; set; }
        public decimal BarometricPressure { get; set; }
        public AirLeakageTestTypesData? TestType { get; set; }
        public List<TestData>? TestData { get; set; }
    }

    public class TestData
    {
        public Guid Id { get; set; }
        public Guid AirLeakageTestDataId { get; set; }
        public int Rank { get; set; }
        public int Equipment { get; set; }
        public decimal InsideTemperature { get; set; }
        public decimal ZoneHeatedVolume { get; set; }
        public string? Manometer { get; set; }
        public PressureData? Pressure { get; set; }
        public AirInfiltrationCodeAndTextData? FanType { get; set; }
        public List<DataPointData>? Data { get; set; }
    }

    public class PressureData
    {
        public decimal Value { get; set; }
    }

    public class DataPointData
    {
        public Guid Id { get; set; }
        public Guid TestId { get; set; }
        public int Rank { get; set; }
        public decimal HousePressure { get; set; }
        public decimal FanPressure { get; set; }
        public decimal MeasuredFlow { get; set; }
        public decimal Zone1Pressure { get; set; }
        public decimal Zone2Pressure { get; set; }
        public AirInfiltrationCodeAndTextData? FlowRanges { get; set; }
    }

    // Resource type data classes matching AirInfiltrationService resource classes
    // Note: AirInfiltration uses different property names than BaseloadData.CodeAndTextData

    public class AirInfiltrationCodeAndTextData
    {
        public string Code { get; set; } = string.Empty;
        public string English { get; set; } = string.Empty;
        public string French { get; set; } = string.Empty;
        public bool IsUserSpecified { get; set; }
    }

    public class AirTightnessTypesData : AirInfiltrationCodeAndTextData
    {
        public decimal Value { get; set; }
    }

    public class BlowerTestPressuresData : AirInfiltrationCodeAndTextData
    {
    }

    public class TerrainsData : AirInfiltrationCodeAndTextData
    {
    }

    public class LocalShieldingsData : AirInfiltrationCodeAndTextData
    {
    }

    public class TestStatusesData : AirInfiltrationCodeAndTextData
    {
    }

    public class AirLeakageTestTypesData : AirInfiltrationCodeAndTextData
    {
    }
}
