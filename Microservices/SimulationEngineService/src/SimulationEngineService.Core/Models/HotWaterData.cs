using System.ComponentModel.DataAnnotations;

namespace SimulationEngineService.Core.Models
{
    /// <summary>
    /// Represents hot water system data for simulation - matches HotWaterService.Core.Models.HotWater
    /// </summary>
    public class HotWaterData
    {
        public Guid Id { get; set; }

        [Required]
        public Guid HouseId { get; set; }

        public Guid? EnergyUpgradeId { get; set; }

        public string Label { get; set; } = "Domestic Hot Water";

        // Navigation properties for separate Primary and Secondary components (like Type2 heat pumps)
        public PrimaryHotWaterComponent? Primary { get; set; }
        public SecondaryHotWaterComponent? Secondary { get; set; }

        public NumberOfDwhrSystems? NumberOfDwhrSystems { get; set; }
        public NumberOfHotWaterSystems? NumberOfHotWaterSystems { get; set; }
    }

    /// <summary>
    /// Primary hot water component data - matches HotWaterService.Core.Models.PrimaryHotWaterComponent
    /// </summary>
    public class PrimaryHotWaterComponent
    {
        public Guid Id { get; set; }
        public Guid HotWaterId { get; set; }

        public bool HasDrainWaterHeatRecovery { get; set; }
        public decimal InsulatingBlanket { get; set; }
        public decimal PilotEnergy { get; set; }
        public decimal HeatPumpCoefficient { get; set; }
        public bool CombinedFlue { get; set; }
        public decimal FlueDiameter { get; set; }
        public bool EnergyStar { get; set; }
        public bool EcoEnergy { get; set; }
        public bool UserDefinedPilot { get; set; }
        public decimal Fraction { get; set; } = 1.0m;
        public int ConnectedUnitsDwhr { get; set; }
        public bool HasSolar { get; set; }

        // Owned entities for complex types
        public EquipmentInformation? EquipmentInformation { get; set; }
        public CodeAndText? TankTypeData { get; set; }
        public EnergyFactor? EnergyFactor { get; set; }

        // Resource navigation properties
        public DhwEnergySources? EnergySource { get; set; }
        public DhwTankVolumes? TankVolume { get; set; }
        public DhwDrawPatterns? DrawPattern { get; set; }
        public DhwTankLocations? TankLocation { get; set; }

        // Related components
        public Solar? Solar { get; set; }
        public DrainWaterHeatRecovery? DrainWaterHeatRecovery { get; set; }
    }

    /// <summary>
    /// Secondary hot water component data - matches HotWaterService.Core.Models.SecondaryHotWaterComponent
    /// </summary>
    public class SecondaryHotWaterComponent
    {
        public Guid Id { get; set; }
        public Guid HotWaterId { get; set; }

        public bool HasDrainWaterHeatRecovery { get; set; }
        public decimal InsulatingBlanket { get; set; }
        public decimal PilotEnergy { get; set; }
        public decimal HeatPumpCoefficient { get; set; }
        public bool CombinedFlue { get; set; }
        public decimal Fraction { get; set; }
        public bool HasSolar { get; set; }

        // Owned entities for complex types
        public EquipmentInformation? EquipmentInformation { get; set; }
        public CodeAndText? TankTypeData { get; set; }
        public EnergyFactor? EnergyFactor { get; set; }

        // Resource navigation properties
        public DhwEnergySources? EnergySource { get; set; }
        public DhwTankVolumes? TankVolume { get; set; }
        public DhwDrawPatterns? DrawPattern { get; set; }
        public DhwTankLocations? TankLocation { get; set; }

        // Related components
        public Solar? Solar { get; set; }
        public DrainWaterHeatRecovery? DrainWaterHeatRecovery { get; set; }
    }

    /// <summary>
    /// Solar hot water system data - matches HotWaterService.Core.Models.Solar
    /// </summary>
    public class Solar
    {
        public Guid Id { get; set; }

        // Foreign keys for Primary or Secondary components
        public Guid? PrimaryHotWaterComponentId { get; set; }
        public Guid? SecondaryHotWaterComponentId { get; set; }

        public decimal Rating { get; set; }
        public decimal Slope { get; set; }
        public decimal Azimuth { get; set; }
    }

    /// <summary>
    /// Drain water heat recovery system data - matches HotWaterService.Core.Models.DrainWaterHeatRecovery
    /// </summary>
    public class DrainWaterHeatRecovery
    {
        public Guid Id { get; set; }

        // Foreign keys for Primary or Secondary components
        public Guid? PrimaryHotWaterComponentId { get; set; }
        public Guid? SecondaryHotWaterComponentId { get; set; }

        public decimal ShowerLength { get; set; }
        public decimal DailyShowers { get; set; }
        public bool PreheatShowerTank { get; set; }
        public decimal Effectiveness { get; set; }

        // Equipment Information
        public EquipmentInformation? EquipmentInformation { get; set; }

        // Efficiency - using CodeAndText pattern
        public CodeAndText? Efficiency { get; set; }

        // Direct resource objects (no Data properties needed - not interfaces)
        public ShowerTemperatures? ShowerTemperature { get; set; }
        public ShowerFlowRates? ShowerHead { get; set; }
    }

    /// <summary>
    /// Number of DWHR systems for MURB buildings - matches HotWaterService.Core.Models.NumberOfDwhrSystems
    /// </summary>
    public class NumberOfDwhrSystems
    {
        public Guid Id { get; set; }
        public Guid HotWaterId { get; set; }

        public byte LowEfficiency { get; set; }
        public byte HighEfficiency { get; set; }
    }

    /// <summary>
    /// Number of hot water systems for MURB buildings - matches HotWaterService.Core.Models.NumberOfHotWaterSystems
    /// </summary>
    public class NumberOfHotWaterSystems
    {
        public Guid Id { get; set; }
        public Guid HotWaterId { get; set; }

        public byte EnergyStarInstantaneousCondensing { get; set; }
        public byte EnergyStarInstantaneous { get; set; }
        public byte Condensing { get; set; }
        public byte Instantaneous { get; set; }
        public byte HeatPumpWaterHeater { get; set; }
    }

    // Supporting classes that need to be defined to match the HotWaterService models
    // Note: EquipmentInformation and CodeAndText are already defined in HvacData.cs

    /// <summary>
    /// Energy factor information - matches HotWaterService.Core.Models.EnergyFactor
    /// </summary>
    public class EnergyFactor
    {
        public string Code { get; set; } = string.Empty;
        public string EnglishText { get; set; } = string.Empty;
        public string FrenchText { get; set; } = string.Empty;
        public decimal Value { get; set; }
        public decimal InputCapacity { get; set; }
        public decimal ThermalEfficiency { get; set; }
        public decimal StandbyHeatLoss { get; set; }
        public ushort StandbyHeatLossMode { get; set; }
    }

    // Resource classes - simplified versions for simulation data transfer
    public class DhwEnergySources
    {
        public string Code { get; set; } = string.Empty;
        public string EnglishText { get; set; } = string.Empty;
        public string FrenchText { get; set; } = string.Empty;
    }

    public class DhwTankVolumes
    {
        public string Code { get; set; } = string.Empty;
        public string EnglishText { get; set; } = string.Empty;
        public string FrenchText { get; set; } = string.Empty;
        public decimal Value { get; set; }
    }

    public class DhwDrawPatterns
    {
        public string Code { get; set; } = string.Empty;
        public string EnglishText { get; set; } = string.Empty;
        public string FrenchText { get; set; } = string.Empty;
    }

    public class DhwTankLocations
    {
        public string Code { get; set; } = string.Empty;
        public string EnglishText { get; set; } = string.Empty;
        public string FrenchText { get; set; } = string.Empty;
    }

    public class ShowerTemperatures
    {
        public string Code { get; set; } = string.Empty;
        public string EnglishText { get; set; } = string.Empty;
        public string FrenchText { get; set; } = string.Empty;
    }

    public class ShowerFlowRates
    {
        public string Code { get; set; } = string.Empty;
        public string EnglishText { get; set; } = string.Empty;
        public string FrenchText { get; set; } = string.Empty;
    }
}
