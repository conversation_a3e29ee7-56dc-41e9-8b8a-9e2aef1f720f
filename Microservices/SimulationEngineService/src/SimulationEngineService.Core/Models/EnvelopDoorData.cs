using System;

namespace SimulationEngineService.Core.Models
{
    /// <summary>
    /// Door data model for simulation engine - matches DoorDto structure
    /// </summary>
    public class EnvelopDoorData
    {
        public Guid Id { get; set; }
        public Guid HouseId { get; set; }
        public Guid? EnergyUpgradeId { get; set; }
        public string Label { get; set; } = string.Empty;
        public bool AdjacentEnclosedSpace { get; set; }

        // RValue property that gets value from Construction.Type.Value
        public decimal RValue => Construction?.Type?.Value ?? 0m;

        // Navigation properties
        public DoorConstructionData Construction { get; set; } = new DoorConstructionData();
        public DoorMeasurementsData Measurements { get; set; } = new DoorMeasurementsData();
    }

    public class DoorConstructionData
    {
        public bool EnergyStar { get; set; }
        public DoorTypesData Type { get; set; } = new DoorTypesData();
    }

    public class DoorMeasurementsData
    {
        public decimal Height { get; set; }
        public decimal Width { get; set; }
        public decimal Area => Height * Width;
    }

    public class DoorTypesData
    {
        public string Code { get; set; } = string.Empty;
        public string English { get; set; } = string.Empty;
        public string French { get; set; } = string.Empty;
        public decimal Value { get; set; }
        public bool IsUserSpecified { get; set; }
    }
}
