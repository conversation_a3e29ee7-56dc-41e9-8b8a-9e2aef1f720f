using System;
using System.Collections.Generic;

namespace SimulationEngineService.Core.Models
{
    /// <summary>
    /// Floor header data model for simulation engine
    /// Based on FloorHeader from EnvelopeService, following EnvelopFloorData pattern
    /// </summary>
    public class EnvelopFloorHeaderData
    {
        public Guid Id { get; set; }
        public Guid HouseId { get; set; }
        public Guid? EnergyUpgradeId { get; set; }
        public string Label { get; set; } = "Floor Header";
        public bool AdjacentEnclosedSpace { get; set; } = false;

        // Construction properties
        public FloorHeaderConstruction Construction { get; set; } = new FloorHeaderConstruction();

        // Measurement properties
        public FloorHeaderMeasurements Measurements { get; set; } = new FloorHeaderMeasurements();

        // Facing direction properties
        public FloorHeaderDirection FacingDirection { get; set; } = new FloorHeaderDirection();
    }

    public class FloorHeaderConstruction
    {
        public Guid Id { get; set; }
        public Guid FloorHeaderId { get; set; }
        public Guid TypeReferenceId { get; set; }
        public FloorHeaderType Type { get; set; } = new FloorHeaderType();
    }

    public class FloorHeaderType
    {
        public Guid Id { get; set; }
        public string IdRef { get; set; } = string.Empty;
        public string Code { get; set; } = string.Empty;
        public double RValue { get; set; }
        public double NominalInsulation { get; set; }
        public string Text { get; set; } = string.Empty;
        public List<UserDefinedCodeLayer> UserDefinedCodeLayers { get; set; } = new List<UserDefinedCodeLayer>();
    }

    public class FloorHeaderMeasurements
    {
        public Guid Id { get; set; }
        public Guid FloorHeaderId { get; set; }
        public double Height { get; set; } = 0.25;
        public double Perimeter { get; set; } = 10.0;
        public double Area => Perimeter * Height;
    }

    public class FloorHeaderDirection
    {
        public Guid Id { get; set; }
        public string Code { get; set; } = "1";
        public string English { get; set; } = "N/A";
        public string French { get; set; } = "S/O";
    }
}
