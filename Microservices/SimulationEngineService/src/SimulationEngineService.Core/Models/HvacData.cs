using System;
using System.Collections.Generic;

namespace SimulationEngineService.Core.Models
{
    /// <summary>
    /// HVAC data model for simulation engine
    /// Mirrors the structure of HeatingCoolingDto from HVAC service exactly
    /// </summary>
    public class HvacData
    {
        public Guid Id { get; set; }
        public Guid HouseId { get; set; }
        public Guid? EnergyUpgradeId { get; set; }
        public string Label { get; set; } = "HeatingCooling";

        // System selection properties (determines which systems to save)
        public int HeatType { get; set; } = 1; // 1=Baseboards, 2=Furnace, 3=Boiler, 4=Combo, 5=IMS, 6=P9
        public int PumpType { get; set; } = 0; // 0=N/A, 1=ASHP, 2=WSHP, 3=GSHP, 4=AC
        public bool HasRadiantHeating { get; set; } = false;
        public bool HasMultipleSystems { get; set; } = false;
        public int SupplementarySystemsCount { get; set; } = 0;
        public bool HasAdditionalOpenings { get; set; } = false;

        // Navigation properties - exactly matching HvacService structure
        public CoolingSeasonData CoolingSeason { get; set; } = new CoolingSeasonData();
        public Type1Data Type1 { get; set; } = new Type1Data();
        public Type2Data Type2 { get; set; } = new Type2Data();
        public MultipleSystemsData? MultipleSystems { get; set; }
        public RadiantHeatingData? RadiantHeating { get; set; }
        public List<AdditionalOpeningData>? AdditionalOpenings { get; set; }
        public List<SupplementaryHeatData>? SupplementaryHeating { get; set; }
    }

    public class CoolingSeasonData
    {
        public Guid Id { get; set; }
        public Guid HeatingCoolingId { get; set; }
        public string StartCode { get; set; } = string.Empty;
        public string EndCode { get; set; } = string.Empty;
        public string DesignCode { get; set; } = string.Empty;
        public CodeAndTextData Start { get; set; } = new CodeAndTextData();
        public CodeAndTextData End { get; set; } = new CodeAndTextData();
        public CodeAndTextData Design { get; set; } = new CodeAndTextData();
        public CoolingSeasonInformationData CoolingSeasonInformation { get; set; } = new CoolingSeasonInformationData();
    }

    public class CoolingSeasonInformationData
    {
        public decimal CoolingSetPoint { get; set; } = 25.0m;
        public string CoolingSetPointText { get; set; } = "25°C";
    }

    public class Type1Data
    {
        public Guid Id { get; set; }
        public Guid HeatingCoolingId { get; set; }

        // Heating system types - exactly matching HvacService Type1 structure
        public FansAndPumpsHeatingData? FansAndPump { get; set; }
        public BaseboardsData? Baseboards { get; set; }
        public BoilerData? Boiler { get; set; }
        public FurnaceData? Furnace { get; set; }
        public ComboHeatDhwData? ComboHeatDhw { get; set; }
        public P9Data? P9 { get; set; }
    }

    public class Type2Data
    {
        public Guid Id { get; set; }
        public Guid HeatingCoolingId { get; set; }

        // Shading configuration - exactly matching HvacService Type2 model
        public string ShadingInF280Cooling { get; set; } = "AccountedFor";

        // Helper property for boolean access
        public bool ShadingInF280CoolingIsAccountedFor
        {
            get { return ShadingInF280Cooling == "AccountedFor"; }
            set { ShadingInF280Cooling = value ? "AccountedFor" : "Ignored"; }
        }

        // Heat pump types (nullable - only one should be present)
        public AirHeatPumpData? AirHeatPump { get; set; }
        public WaterHeatPumpData? WaterHeatPump { get; set; }
        public GroundHeatPumpData? GroundHeatPump { get; set; }
        public AirConditioningData? AirConditioning { get; set; }
    }

    // Heating system data classes - matching HvacService structure exactly
    public class BaseboardsData
    {
        public Guid Id { get; set; }
        public Guid Type1Id { get; set; }
        public BaseboardsEquipmentInformation EquipmentInformation { get; set; } = new BaseboardsEquipmentInformation();
        public BaseboardsSpecifications Specifications { get; set; } = new BaseboardsSpecifications();
    }

    // Common base class for Boiler, Furnace, ComboHeatDhw (matching HvacService Common class)
    public class CommonData
    {
        public Guid Id { get; set; }
        public Guid Type1Id { get; set; }
        public Type1EquipmentInformation Type1EquipmentInformation { get; set; } = new Type1EquipmentInformation();
        public CommonSpecifications Specifications { get; set; } = new CommonSpecifications();
        public ComboTankAndPumpData? ComboTankAndPump { get; set; }
    }

    public class BoilerData : CommonData
    {
        public BoilerEquipmentData Equipment { get; set; } = new BoilerEquipmentData();
    }

    public class FurnaceData : CommonData
    {
        public FurnaceEquipmentData Equipment { get; set; } = new FurnaceEquipmentData();
    }

    public class ComboHeatDhwData : CommonData
    {
        public ComboHeatDhwEquipmentData Equipment { get; set; } = new ComboHeatDhwEquipmentData();
    }

    public class P9Data
    {
        public Guid Id { get; set; }
        public Guid Type1Id { get; set; }

        // P9 specific properties (matching HvacService P9 model)
        public ushort NumberOfSystems { get; set; } = 1;
        public decimal ThermalPerformanceFactor { get; set; } = 0.0m;
        public decimal AnnualElectricity { get; set; } = 0.0m;
        public decimal SpaceHeatingCapacity { get; set; } = 0.0m;
        public decimal SpaceHeatingEfficiency { get; set; } = 0.0m;
        public decimal WaterHeatingPerformanceFactor { get; set; } = 0.0m;
        public decimal BurnerInput { get; set; } = 0.0m;
        public decimal RecoveryEfficiency { get; set; } = 0.0m;
        public bool IsUserSpecified { get; set; } = false;

        public EquipmentInformation EquipmentInformation { get; set; } = new EquipmentInformation();
        public TestDataData TestData { get; set; } = new TestDataData();
    }

    public class FansAndPumpsHeatingData
    {
        public Guid Id { get; set; }
        public Guid Type1Id { get; set; }

        public bool HasEnergyEfficientMotor { get; set; } = false;
        public HeatingFanModesData Mode { get; set; } = new HeatingFanModesData();
        public FansAndPumpPowerHeatingData Power { get; set; } = new FansAndPumpPowerHeatingData();
    }

    // Heat pump data classes
    public class AirHeatPumpData
    {
        public Guid Id { get; set; }
        public Guid Type2Id { get; set; }
        public HeatPumpEquipmentInformation EquipmentInformation { get; set; } = new HeatPumpEquipmentInformation();
        public HeatPumpSpecifications Specifications { get; set; } = new HeatPumpSpecifications();
    }

    public class WaterHeatPumpData
    {
        public Guid Id { get; set; }
        public Guid Type2Id { get; set; }
        public HeatPumpEquipmentInformation EquipmentInformation { get; set; } = new HeatPumpEquipmentInformation();
        public HeatPumpSpecifications Specifications { get; set; } = new HeatPumpSpecifications();
    }

    public class GroundHeatPumpData
    {
        public Guid Id { get; set; }
        public Guid Type2Id { get; set; }
        public HeatPumpEquipmentInformation EquipmentInformation { get; set; } = new HeatPumpEquipmentInformation();
        public HeatPumpSpecifications Specifications { get; set; } = new HeatPumpSpecifications();
    }

    public class AirConditioningData
    {
        public Guid Id { get; set; }
        public Guid Type2Id { get; set; }
        public EnergyStarEquipmentInformation EquipmentInformation { get; set; } = new EnergyStarEquipmentInformation();
        public AirConditioningSpecifications Specifications { get; set; } = new AirConditioningSpecifications();
    }

    // Equipment information classes - copied from HvacService.Core.Models
    public class EquipmentInformation
    {
        public string Manufacturer { get; set; } = string.Empty;
        public string Model { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;

        public EquipmentInformation()
        {
        }

        public EquipmentInformation(EquipmentInformation toCopy)
        {
            if (toCopy != null)
            {
                Manufacturer = toCopy.Manufacturer;
                Model = toCopy.Model;
                Description = toCopy.Description;
            }
        }
    }

    public class BaseboardsEquipmentInformation : EquipmentInformation
    {
        public uint NumberOfElectronicThermostats { get; set; } = 0;

        public BaseboardsEquipmentInformation()
        {
        }

        public BaseboardsEquipmentInformation(BaseboardsEquipmentInformation toCopy) : base(toCopy)
        {
            if (toCopy != null)
            {
                NumberOfElectronicThermostats = toCopy.NumberOfElectronicThermostats;
            }
        }
    }

    public class Type1EquipmentInformation : EquipmentInformation
    {
        public Type1EquipmentInformation()
        {
        }

        public Type1EquipmentInformation(Type1EquipmentInformation toCopy) : base(toCopy)
        {
        }
    }

    public class HeatPumpEquipmentInformation : EquipmentInformation
    {
        public bool IsEnergyStar { get; set; } = false;

        public HeatPumpEquipmentInformation()
        {
        }

        public HeatPumpEquipmentInformation(HeatPumpEquipmentInformation toCopy) : base(toCopy)
        {
            if (toCopy != null)
            {
                IsEnergyStar = toCopy.IsEnergyStar;
            }
        }
    }

    public class EnergyStarEquipmentInformation : EquipmentInformation
    {
        public bool IsEnergyStar { get; set; } = false;

        public EnergyStarEquipmentInformation()
        {
        }

        public EnergyStarEquipmentInformation(EnergyStarEquipmentInformation toCopy) : base(toCopy)
        {
            if (toCopy != null)
            {
                IsEnergyStar = toCopy.IsEnergyStar;
            }
        }
    }

    // Equipment classes for Common-based systems (matching HvacService structure)
    public class CommonEquipmentData
    {
        public bool IsBiEnergy { get; set; } = false;
        public decimal SwitchoverTemperature { get; set; } = 0.0m;
        public HeatingEnergySourcesData EnergySource { get; set; } = new HeatingEnergySourcesData();
    }

    public class BoilerEquipmentData : CommonEquipmentData
    {
        public CodeAndTextData EquipmentTypeData { get; set; } = new CodeAndTextData();
    }

    public class FurnaceEquipmentData : CommonEquipmentData
    {
        public CodeAndTextData EquipmentTypeData { get; set; } = new CodeAndTextData();
    }

    public class ComboHeatDhwEquipmentData : CommonEquipmentData
    {
        public CodeAndTextData EquipmentTypeData { get; set; } = new CodeAndTextData();
    }

    // Specification classes - copied from HvacService.Core.Models
    public class CommonSpecifications
    {
        public decimal SizingFactor { get; set; } = 1.0m;
        public decimal Efficiency { get; set; } = 80.0m;
        public bool IsSteadyState { get; set; } = false;
        public decimal PilotLight { get; set; } = 0.0m;
        public decimal FlueDiameter { get; set; } = 0.0m;
        public OutputCapacity OutputCapacity { get; set; } = new OutputCapacity();

        public CommonSpecifications()
        {
        }

        public CommonSpecifications(CommonSpecifications toCopy)
        {
            if (toCopy != null)
            {
                SizingFactor = toCopy.SizingFactor;
                Efficiency = toCopy.Efficiency;
                IsSteadyState = toCopy.IsSteadyState;
                PilotLight = toCopy.PilotLight;
                FlueDiameter = toCopy.FlueDiameter;
                OutputCapacity = new OutputCapacity(toCopy.OutputCapacity);
            }
        }
    }

    public class HeatPumpSpecificationsData
    {
        public double HeatingCapacity { get; set; }
        public double CoolingCapacity { get; set; }
        public double HeatingCOP { get; set; }
        public double CoolingSEER { get; set; }
    }

    public class AirConditioningSpecificationsData
    {
        public double CoolingCapacity { get; set; }
        public double CoolingSEER { get; set; }
    }

    public class CopSeerValue
    {
        public bool IsCop { get; set; } = true;
        public int Unit { get; set; } = 0;
        public decimal Value { get; set; } = 0.0m;

        public CopSeerValue()
        {
        }

        public CopSeerValue(bool isCop, decimal newValue)
        {
            Set(isCop, newValue);
        }

        public CopSeerValue(bool isCop, int newUnit, decimal newValue)
        {
            Set(isCop, newUnit, newValue);
        }

        public CopSeerValue(CopSeerValue toCopy)
        {
            if (toCopy != null)
            {
                IsCop = toCopy.IsCop;
                Unit = toCopy.Unit;
                Value = toCopy.Value;
            }
        }

        public void Set(bool isCop, decimal newValue)
        {
            IsCop = isCop;
            Unit = 0;
            Value = newValue;
        }

        public void Set(bool isCop, int newUnit, decimal newValue)
        {
            IsCop = isCop;
            Unit = newUnit;
            Value = newValue;
        }
    }

    public class HeatPumpSpecifications
    {
        public OutputCapacity OutputCapacity { get; set; } = new OutputCapacity();
        public CopSeerValue HeatingEfficiency { get; set; } = new CopSeerValue();
        public CopSeerValue? CoolingEfficiency { get; set; }

        public HeatPumpSpecifications()
        {
        }

        public HeatPumpSpecifications(HeatPumpSpecifications toCopy)
        {
            if (toCopy != null)
            {
                OutputCapacity = new OutputCapacity(toCopy.OutputCapacity);
                HeatingEfficiency = new CopSeerValue(toCopy.HeatingEfficiency);
                CoolingEfficiency = toCopy.CoolingEfficiency != null ? new CopSeerValue(toCopy.CoolingEfficiency) : null;
            }
        }
    }

    public class AirConditioningSpecifications
    {
        public decimal SizingFactor { get; set; } = 1.0m;
        public OutputCapacity RatedCapacity { get; set; } = new OutputCapacity("2", "Calculated", "Calculé", 0.0m, "kW");
        public CopSeerValue Efficiency { get; set; } = new CopSeerValue(true, 0, 3.0m);

        public AirConditioningSpecifications()
        {
        }

        public AirConditioningSpecifications(AirConditioningSpecifications toCopy)
        {
            if (toCopy != null)
            {
                SizingFactor = toCopy.SizingFactor;
                RatedCapacity = new OutputCapacity(toCopy.RatedCapacity);
                Efficiency = new CopSeerValue(toCopy.Efficiency);
            }
        }
    }

    public class BaseboardsSpecifications
    {
        public decimal SizingFactor { get; set; } = 1.0m;
        public decimal Efficiency { get; set; } = 100.0m;
        public OutputCapacity OutputCapacity { get; set; } = new OutputCapacity();

        public BaseboardsSpecifications()
        {
        }

        public BaseboardsSpecifications(BaseboardsSpecifications toCopy)
        {
            if (toCopy != null)
            {
                SizingFactor = toCopy.SizingFactor;
                Efficiency = toCopy.Efficiency;
                OutputCapacity = new OutputCapacity(toCopy.OutputCapacity);
            }
        }
    }

    // Supporting data classes
    public class TestDataData
    {
        public P9LoadPerformanceData LoadPerformance { get; set; } = new P9LoadPerformanceData();
    }

    public class P9LoadPerformanceData
    {
        public decimal Load10 { get; set; } = 0.0m;
        public decimal Load40 { get; set; } = 0.0m;
        public decimal Load100 { get; set; } = 0.0m;
    }

    public class HeatingFanModesData : CodeAndTextData
    {
        // Inherits Code, EnglishText, FrenchText, Text from CodeAndTextData
    }

    public class FansAndPumpPowerHeatingData
    {
        public bool IsCalculated { get; set; } = false;
        public decimal Low { get; set; } = 0.0m;
        public decimal High { get; set; } = 0.0m;
    }

    public class HeatingEnergySourcesData : CodeAndTextData
    {
        // Inherits Code, EnglishText, FrenchText, Text from CodeAndTextData
    }

    public class ComboTankAndPumpData
    {
        public decimal WaterTemperature { get; set; } = 0.0m;
        public CodeTextAndValue TankCapacityXml { get; set; } = new CodeTextAndValue();
    }

    // Base data classes - copied from HvacService.Core.Models
    public class CodeAndText
    {
        private string code = string.Empty;

        public string Code
        {
            get { return code; }
            set
            {
                if (code != value)
                {
                    Text = string.Empty;
                    EnglishText = string.Empty;
                    FrenchText = string.Empty;
                    code = value;
                }
            }
        }

        public string EnglishText { get; set; } = string.Empty;
        public string FrenchText { get; set; } = string.Empty;
        public string Text { get; set; } = string.Empty;

        public CodeAndText(string code, string englishText, string frenchText)
        {
            this.Code = code;
            this.EnglishText = englishText;
            this.FrenchText = frenchText;
        }

        public CodeAndText(CodeAndText toCopy)
        {
            this.Text = toCopy.Text ?? string.Empty;
            this.EnglishText = toCopy.EnglishText ?? string.Empty;
            this.FrenchText = toCopy.FrenchText ?? string.Empty;
            this.code = toCopy.Code ?? string.Empty;
        }

        public CodeAndText()
        {
            // Properties are already initialized with empty strings
        }

        public void Set(string code, string englishText, string frenchText)
        {
            this.Code = code;
            this.EnglishText = englishText;
            this.FrenchText = frenchText;
        }

        public void Set(string code)
        {
            this.Code = code;
        }
    }

    public class CodeTextAndValue : CodeAndText
    {
        public decimal Value { get; set; } = 0.0m;

        public CodeTextAndValue()
        {
        }

        public CodeTextAndValue(string code, string englishText, string frenchText, decimal value)
        {
            Code = code;
            EnglishText = englishText;
            FrenchText = frenchText;
            Value = value;
        }

        public CodeTextAndValue(CodeTextAndValue toCopy) : base(toCopy)
        {
            if (toCopy != null)
            {
                Value = toCopy.Value;
            }
        }
    }

    public class OutputCapacity : CodeTextAndValue
    {
        public string UiUnits { get; set; } = "kW";

        public decimal ValueInUiUnits
        {
            get
            {
                if (UiUnits?.ToLowerInvariant() == "btu/hr")
                {
                    // Convert kW to BTU/hr: 1 kW = 3412.14 BTU/hr
                    return Value * 3412.14m;
                }
                return Value;
            }
            set
            {
                if (UiUnits?.ToLowerInvariant() == "btu/hr")
                {
                    // Convert BTU/hr to kW: 1 BTU/hr = 0.000293071 kW
                    Value = value * 0.000293071m;
                }
                else
                {
                    Value = value;
                }
            }
        }

        public OutputCapacity()
        {
        }

        public OutputCapacity(OutputCapacity toCopy) : base(toCopy)
        {
            if (toCopy != null)
            {
                UiUnits = toCopy.UiUnits;
            }
        }

        public OutputCapacity(string code, string englishText, string frenchText, decimal value, string uiUnits)
            : base(code, englishText, frenchText, value)
        {
            UiUnits = uiUnits;
        }

        public void Set(string code, string englishText, string frenchText, decimal value, string uiUnits)
        {
            Code = code;
            EnglishText = englishText;
            FrenchText = frenchText;
            Value = value;
            UiUnits = uiUnits;
        }
    }

    // Supporting data classes

    public class MultipleSystemsData
    {
        public Guid Id { get; set; }
        public int SystemCount { get; set; }
    }

    public class RadiantHeatingData
    {
        public Guid Id { get; set; }
        public string Type { get; set; } = string.Empty;
    }

    public class AdditionalOpeningData
    {
        public Guid Id { get; set; }
        public string Description { get; set; } = string.Empty;
        public double Area { get; set; }
    }

    public class SupplementaryHeatData
    {
        public Guid Id { get; set; }
        public string Type { get; set; } = string.Empty;
        public double Capacity { get; set; }
    }
}
