using System;
using System.ComponentModel.DataAnnotations;

namespace SimulationEngineService.Core.Models
{
    /// <summary>
    /// Temperature data model for simulation engine
    /// Based on TemperatureService models
    /// </summary>
    public class TemperatureData
    {
        public Guid Id { get; set; }
        public Guid HouseId { get; set; }
        public Guid? EnergyUpgradeId { get; set; }
        public string Label { get; set; } = "Temperatures";

        // Navigation properties - matching TemperatureService structure
        public MainFloorsData MainFloors { get; set; } = new MainFloorsData();
        public VentilationBasementData VentilationBasement { get; set; } = new VentilationBasementData();
        public EquipmentData Equipment { get; set; } = new EquipmentData();
        public CrawlspacetemparatureData Crawlspace { get; set; } = new CrawlspacetemparatureData();
    }

    public class MainFloorsData
    {
        public Guid Id { get; set; }
        public Guid TemperatureId { get; set; }

        public decimal CoolingSetPoint { get; set; } = 24.0m;
        public decimal DaytimeHeatingSetPoint { get; set; } = 21.0m;
        public decimal NighttimeHeatingSetPoint { get; set; } = 18.0m;
        public decimal NighttimeSetbackDuration { get; set; } = 8.0m;

        // AllowableRise as resource property
        public ResourceData AllowableRise { get; set; } = new ResourceData();
    }

    public class VentilationBasementData
    {
        public Guid Id { get; set; }
        public Guid TemperatureId { get; set; }

        public bool Heated { get; set; } = false;
        public bool Cooled { get; set; } = false;
        public bool SeparateThermostat { get; set; } = false;
        public decimal HeatingSetPoint { get; set; } = 18.0m;
        public bool BasementUnit { get; set; } = false;
    }

    public class EquipmentData
    {
        public Guid Id { get; set; }
        public Guid TemperatureId { get; set; }

        public decimal HeatingSetPoint { get; set; } = 21.0m;
        public decimal CoolingSetPoint { get; set; } = 24.0m;
    }

    public class CrawlspacetemparatureData
    {
        public Guid Id { get; set; }
        public Guid TemperatureId { get; set; }

        public bool Heated { get; set; } = false;
        public decimal HeatingSetPoint { get; set; } = 18.0m;
    }
}
