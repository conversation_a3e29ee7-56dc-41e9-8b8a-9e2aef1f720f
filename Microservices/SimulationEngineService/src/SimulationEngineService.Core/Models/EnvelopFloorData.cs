using System;
using System.Collections.Generic;
using System.Text.Json.Serialization;

namespace SimulationEngineService.Core.Models
{
    // FLOOR MODEL
    public class EnvelopFloorData
    {
        public Guid Id { get; set; }
        public Guid HouseId { get; set; }
        public Guid? EnergyUpgradeId { get; set; }
        public string Label { get; set; } = string.Empty;
        public bool AdjacentEnclosedSpace { get; set; }
        
        // Construction properties
        public FloorConstruction Construction { get; set; } = new FloorConstruction();
        
        // Measurement properties
        public FloorMeasurements Measurements { get; set; } = new FloorMeasurements();
    }

    public class FloorConstruction
    {
        public Guid Id { get; set; }
        public Guid FloorId { get; set; }
        public Guid TypeReferenceId { get; set; }
        public FloorType Type { get; set; } = new FloorType();
    }

    public class FloorType
    {
        public Guid Id { get; set; }
        public string IdRef { get; set; } = string.Empty;
        public string Code { get; set; } = string.Empty;
        public double RValue { get; set; }
        public double NominalInsulation { get; set; }
        public string Text { get; set; } = string.Empty;
        public List<UserDefinedCodeLayer> UserDefinedCodeLayers { get; set; } = new List<UserDefinedCodeLayer>();
    }


    public class FloorMeasurements
    {
        public Guid Id { get; set; }
        public Guid FloorId { get; set; }
        public double Area { get; set; }
        public double Length { get; set; }
    }
}