using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace SimulationEngineService.Core.Models
{
    /// <summary>
    /// Foundation data model for simulation engine
    /// Based on FoundationService models
    /// </summary>
    public class FoundationData
    {
        public Guid Id { get; set; }
        public Guid HouseId { get; set; }
        public Guid? EnergyUpgradeId { get; set; }
        public string Label { get; set; } = "Foundation";

        // Foundation components - matching FoundationService structure
        public List<BasementData> Basements { get; set; } = new List<BasementData>();
        public List<CrawlspaceData> Crawlspaces { get; set; } = new List<CrawlspaceData>();
        public List<SlabData> Slabs { get; set; } = new List<SlabData>();
        public List<WalkoutData> Walkouts { get; set; } = new List<WalkoutData>();
    }

    /// <summary>
    /// Basement foundation data for simulation
    /// </summary>
    public class BasementData
    {
        public Guid Id { get; set; }
        public Guid HouseId { get; set; }
        public string Label { get; set; } = "Basement";
        public bool IsExposedSurface { get; set; }
        public decimal ExposedSurfacePerimeter { get; set; }

        // Configuration
        public FoundationConfigurationData? Configuration { get; set; }

        // Floor and Wall components
        public FoundationFloorData? Floor { get; set; }
        public FoundationWallData? Wall { get; set; }

        // Resource properties
        public string? OpeningUpstairsCode { get; set; }
        public string? RoomTypeCode { get; set; }
    }

    /// <summary>
    /// Crawlspace foundation data for simulation
    /// </summary>
    public class CrawlspaceData
    {
        public Guid Id { get; set; }
        public Guid HouseId { get; set; }
        public string Label { get; set; } = "Crawlspace";
        public bool IsExposedSurface { get; set; }
        public decimal ExposedSurfacePerimeter { get; set; }

        // Configuration
        public FoundationConfigurationData? Configuration { get; set; }

        // Floor and Wall components
        public FoundationFloorData? Floor { get; set; }
        public CrawlspaceWallData? Wall { get; set; }

        // Resource properties
        public string? VentilationTypeCode { get; set; }
    }

    /// <summary>
    /// Slab foundation data for simulation
    /// </summary>
    public class SlabData
    {
        public Guid Id { get; set; }
        public Guid HouseId { get; set; }
        public string Label { get; set; } = "Slab";
        public bool IsExposedSurface { get; set; }
        public decimal ExposedSurfacePerimeter { get; set; }

        // Configuration
        public FoundationConfigurationData? Configuration { get; set; }

        // Floor and Wall components
        public FoundationFloorData? Floor { get; set; }
        public SlabWallData? Wall { get; set; }
    }

    /// <summary>
    /// Walkout foundation data for simulation
    /// </summary>
    public class WalkoutData
    {
        public Guid Id { get; set; }
        public Guid HouseId { get; set; }
        public string Label { get; set; } = "Walkout";
        public bool IsExposedSurface { get; set; }
        public decimal ExposedSurfacePerimeter { get; set; }

        // Configuration
        public FoundationConfigurationData? Configuration { get; set; }

        // Components
        public WalkoutMeasurementsData? Measurements { get; set; }
        public WalkoutFloorData? Floor { get; set; }
        public FoundationWallData? Wall { get; set; }
        public ExteriorSurfacesData? ExteriorSurfaces { get; set; }
        public LocationsData? Locations { get; set; }

        // Resource properties
        public string? OpeningUpstairsCode { get; set; }
        public string? RoomTypeCode { get; set; }
    }

    /// <summary>
    /// Foundation configuration data
    /// </summary>
    public class FoundationConfigurationData
    {
        public Guid Id { get; set; }
        public int Type { get; set; }
        public int Subtype { get; set; }
        public decimal Overlap { get; set; }
    }

    /// <summary>
    /// Foundation floor data
    /// </summary>
    public class FoundationFloorData
    {
        public Guid Id { get; set; }
        public FoundationFloorConstructionData? Construction { get; set; }
        public FoundationMeasurementsData? Measurements { get; set; }
    }

    /// <summary>
    /// Foundation floor construction data
    /// </summary>
    public class FoundationFloorConstructionData
    {
        public Guid Id { get; set; }
        public bool IsBelowFrostline { get; set; }
        public bool HasIntegralFooting { get; set; }
        public bool HeatedFloor { get; set; }
        public bool AddedToSlab { get; set; }
        public int FloorsAbove { get; set; }
    }

    /// <summary>
    /// Foundation measurements data
    /// </summary>
    public class FoundationMeasurementsData
    {
        public Guid Id { get; set; }
        public bool IsRectangular { get; set; }
        public decimal Area { get; set; }
        public decimal Width { get; set; }
        public decimal Length { get; set; }
        public decimal Perimeter { get; set; }
    }

    /// <summary>
    /// Foundation wall data
    /// </summary>
    public class FoundationWallData
    {
        public Guid Id { get; set; }
        public bool HasPonyWall { get; set; }
        public FoundationWallConstructionData? Construction { get; set; }
        public FoundationWallMeasurementsData? Measurements { get; set; }
    }

    /// <summary>
    /// Foundation wall construction data
    /// </summary>
    public class FoundationWallConstructionData
    {
        public Guid Id { get; set; }
        public int Corners { get; set; }
        public decimal InteriorAddedInsulation { get; set; }
        public decimal ExteriorAddedInsulation { get; set; }
        public int Lintels { get; set; }
        public int PonyWallType { get; set; }
    }

    /// <summary>
    /// Foundation wall measurements data
    /// </summary>
    public class FoundationWallMeasurementsData
    {
        public Guid Id { get; set; }
        public decimal Height { get; set; }
        public decimal Depth { get; set; }
        public decimal PonyWallHeight { get; set; }
    }

    /// <summary>
    /// Crawlspace wall data
    /// </summary>
    public class CrawlspaceWallData
    {
        public Guid Id { get; set; }
        public CrawlspaceWallConstructionData? Construction { get; set; }
        public CrawlspaceWallMeasurementsData? Measurements { get; set; }
        public WallRValuesData? RValues { get; set; }
    }

    /// <summary>
    /// Crawlspace wall construction data
    /// </summary>
    public class CrawlspaceWallConstructionData
    {
        public Guid Id { get; set; }
        public int Corners { get; set; }
        public int Type { get; set; }
        public int Lintels { get; set; }
    }

    /// <summary>
    /// Crawlspace wall measurements data
    /// </summary>
    public class CrawlspaceWallMeasurementsData
    {
        public Guid Id { get; set; }
        public decimal Height { get; set; }
        public decimal Depth { get; set; }
    }

    /// <summary>
    /// Slab wall data
    /// </summary>
    public class SlabWallData
    {
        public Guid Id { get; set; }
        public WallRValuesData? RValues { get; set; }
    }

    /// <summary>
    /// Wall R-values data
    /// </summary>
    public class WallRValuesData
    {
        public Guid Id { get; set; }
        public decimal Skirt { get; set; }
        public decimal ThermalBreak { get; set; }
    }

    /// <summary>
    /// Walkout measurements data
    /// </summary>
    public class WalkoutMeasurementsData
    {
        public Guid Id { get; set; }
        public bool WithSlab { get; set; }
        public decimal Height { get; set; }
        public decimal D1 { get; set; }
        public decimal D2 { get; set; }
        public decimal D3 { get; set; }
        public decimal D4 { get; set; }
        public decimal D5 { get; set; }
        public decimal L1 { get; set; }
        public decimal L2 { get; set; }
        public decimal L3 { get; set; }
        public decimal L4 { get; set; }
    }

    /// <summary>
    /// Walkout floor data
    /// </summary>
    public class WalkoutFloorData
    {
        public Guid Id { get; set; }
        public FoundationFloorConstructionData? Construction { get; set; }
    }

    /// <summary>
    /// Exterior surfaces data
    /// </summary>
    public class ExteriorSurfacesData
    {
        public Guid Id { get; set; }
        public decimal AboveGradeArea { get; set; }
        public decimal BelowGradeArea { get; set; }
        public decimal PonyWallArea { get; set; }
        public decimal SlabPerimeter { get; set; }
    }

    /// <summary>
    /// Locations data
    /// </summary>
    public class LocationsData
    {
        public Guid Id { get; set; }
        public LocationData? L1_1 { get; set; }
        public LocationData? L1_2 { get; set; }
        public LocationData? L2_1 { get; set; }
        public LocationData? L2_2 { get; set; }
    }

    /// <summary>
    /// Location coordinate data
    /// </summary>
    public class LocationData
    {
        public Guid Id { get; set; }
        public decimal X1 { get; set; }
        public decimal X2 { get; set; }
    }
}
