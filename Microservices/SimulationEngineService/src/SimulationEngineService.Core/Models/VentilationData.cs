using System;
using System.Collections.Generic;

namespace SimulationEngineService.Core.Models
{
    /// <summary>
    /// Ventilation data model for simulation engine
    /// Mirrors the structure of VentilationDto from Ventilation service exactly
    /// </summary>
    public class VentilationData
    {
        public Guid Id { get; set; }
        public Guid HouseId { get; set; }
        public Guid? EnergyUpgradeId { get; set; }
        public string Label { get; set; } = "Ventilation";

        // Navigation properties
        public RoomsData Rooms { get; set; } = new RoomsData();
        public RequirementsData Requirements { get; set; } = new RequirementsData();
        public WholeHouseParametersData SupplyAndExhaust { get; set; } = new WholeHouseParametersData();

        // Separate collections for different ventilator types (following HvacService pattern)
        public List<WholeHouseHrvData> WholeHouseHrvList { get; set; } = new List<WholeHouseHrvData>();
        public List<WholeHouseDryerData> WholeHouseDryerList { get; set; } = new List<WholeHouseDryerData>();
        public List<WholeHouseVentilatorData> WholeHouseVentilatorList { get; set; } = new List<WholeHouseVentilatorData>();
        public List<SupplementalHrvData> SupplementalHrvList { get; set; } = new List<SupplementalHrvData>();
        public List<SupplementalDryerData> SupplementalDryerList { get; set; } = new List<SupplementalDryerData>();
        public List<SupplementalVentilatorData> SupplementalVentilatorList { get; set; } = new List<SupplementalVentilatorData>();

        // Polymorphic collections (computed properties for API compatibility)
        public List<object> WholeHouseVentilatorListCombined
        {
            get
            {
                var result = new List<object>();
                result.AddRange(WholeHouseHrvList);
                result.AddRange(WholeHouseDryerList);
                result.AddRange(WholeHouseVentilatorList);
                return result;
            }
        }

        public List<object> SupplementalVentilatorListCombined
        {
            get
            {
                var result = new List<object>();
                result.AddRange(SupplementalHrvList);
                result.AddRange(SupplementalDryerList);
                result.AddRange(SupplementalVentilatorList);
                return result;
            }
        }

        // Calculated properties (read-only)
        public decimal WholeHouseVentilatorSupplyTotal { get; set; }
        public decimal WholeHouseVentilatorExhaustTotal { get; set; }
        public decimal SupplementalVentilatorSupplyTotal { get; set; }
        public decimal SupplementalVentilatorExhaustTotal { get; set; }
    }

    public class RoomsData
    {
        public Guid Id { get; set; }
        public Guid VentilationId { get; set; }

        public uint Living { get; set; } = 0;
        public uint Bedrooms { get; set; } = 0;
        public uint Bathrooms { get; set; } = 0;
        public uint Utility { get; set; } = 0;
        public uint OtherHabitable { get; set; } = 0;

        // Resource properties
        public ResourceValueData VentilationRate { get; set; } = new ResourceValueData();
        public ResourceValueData DepressurizationLimit { get; set; } = new ResourceValueData();

        // Calculated property (read-only)
        public decimal MinimumVentilationRate { get; set; }
    }

    public class RequirementsData
    {
        public Guid Id { get; set; }
        public Guid VentilationId { get; set; }

        public decimal Ach { get; set; } = 0.0m;
        public decimal Supply { get; set; } = 0.0m;
        public decimal Exhaust { get; set; } = 0.0m;

        // Resource properties
        public ResourceData Use { get; set; } = new ResourceData();
    }

    public class WholeHouseParametersData
    {
        public Guid Id { get; set; }
        public Guid VentilationId { get; set; }

        public decimal TemperatureControlLower { get; set; } = 0.0m;
        public decimal TemperatureControlUpper { get; set; } = 0.0m;
        public decimal HviHrvErvInMurb { get; set; } = 0.0m;

        // Resource properties
        public ResourceData AirDistributionType { get; set; } = new ResourceData();
        public ResourceValueData AirDistributionFanPower { get; set; } = new ResourceValueData();
        public ResourceValueData OperationSchedule { get; set; } = new ResourceValueData();
    }

    // Base class for all ventilator types
    public abstract class VentilatorObjectsData
    {
        public Guid Id { get; set; }
        public Guid VentilationId { get; set; }

        public decimal SupplyFlowrate { get; set; } = 0.0m;
        public decimal ExhaustFlowrate { get; set; } = 0.0m;
        public decimal FanPower1 { get; set; } = 0.0m;

        public bool IsDefaultFanpower { get; set; } = false;
        public bool IsEnergyStar { get; set; } = false;
        public bool IsHomeVentilatingInstituteCertified { get; set; } = false;
        public bool IsSupplemental { get; set; } = false;

        // Resource properties
        public ResourceData VentilatorType { get; set; } = new ResourceData();
        public EquipmentInformation EquipmentInformation { get; set; } = new EquipmentInformation();
        public ResourceData OperationSchedule { get; set; } = new ResourceData();
    }

    // Whole House ventilator types
    public class WholeHouseHrvData : VentilatorObjectsData
    {
        public decimal TemperatureCondition1 { get; set; } = 0.0m;
        public decimal TemperatureCondition2 { get; set; } = 0.0m;
        public decimal FanPower2 { get; set; } = 0.0m;
        public decimal Efficiency1 { get; set; } = 0.0m;
        public decimal Efficiency2 { get; set; } = 0.0m;
        public decimal PreheaterCapacity { get; set; } = 0.0m;
        public decimal LowTempVentReduction { get; set; } = 0.0m;
        public decimal CoolingEfficiency { get; set; } = 0.0m;

        // HRV Ducts
        public HrvDuctsData HrvDucts { get; set; } = new HrvDuctsData();
    }

    public class WholeHouseDryerData : VentilatorObjectsData
    {
        // Resource properties specific to dryer
        public ResourceData Exhaust { get; set; } = new ResourceData();
    }

    public class WholeHouseVentilatorData : VentilatorObjectsData
    {
        // Base ventilator - no additional properties
    }

    // Supplemental ventilator types
    public class SupplementalHrvData : VentilatorObjectsData
    {
        public decimal TemperatureCondition1 { get; set; } = 0.0m;
        public decimal TemperatureCondition2 { get; set; } = 0.0m;
        public decimal FanPower2 { get; set; } = 0.0m;
        public decimal Efficiency1 { get; set; } = 0.0m;
        public decimal Efficiency2 { get; set; } = 0.0m;
        public decimal PreheaterCapacity { get; set; } = 0.0m;
        public decimal LowTempVentReduction { get; set; } = 0.0m;
        public decimal CoolingEfficiency { get; set; } = 0.0m;

        // HRV Ducts
        public HrvDuctsData HrvDucts { get; set; } = new HrvDuctsData();
    }

    public class SupplementalDryerData : VentilatorObjectsData
    {
        // Resource properties specific to dryer
        public ResourceData Exhaust { get; set; } = new ResourceData();
    }

    public class SupplementalVentilatorData : VentilatorObjectsData
    {
        // Base ventilator - no additional properties
    }

    // Resource data classes (reusing from existing models)
    public class ResourceData
    {
        public string Code { get; set; } = string.Empty;
        public string English { get; set; } = string.Empty;
        public string French { get; set; } = string.Empty;
        public bool IsUserSpecified { get; set; } = false;
    }

    public class ResourceValueData : ResourceData
    {
        public decimal Value { get; set; } = 0.0m;
    }



    public class HrvDuctsData
    {
        public DuctData Supply { get; set; } = new DuctData();
        public DuctData Exhaust { get; set; } = new DuctData();
    }

    public class DuctData
    {
        public decimal Diameter { get; set; } = 0.0m;
        public decimal Length { get; set; } = 0.0m;
        public decimal Insulation { get; set; } = 0.0m;

        public ResourceData Type { get; set; } = new ResourceData();
        public ResourceData Location { get; set; } = new ResourceData();
        public ResourceData Sealing { get; set; } = new ResourceData();
    }
}
