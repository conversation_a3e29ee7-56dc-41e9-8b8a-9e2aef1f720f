using System;

namespace SimulationEngineService.Core.Models
{
    /// <summary>
    /// Room data model for simulation engine - matches RoomDto structure
    /// </summary>
    public class EnvelopRoomData
    {
        public Guid Id { get; set; }
        public Guid HouseId { get; set; }
        public Guid? EnergyUpgradeId { get; set; }
        public string Label { get; set; } = string.Empty;

        // Navigation properties
        public RoomConstructionData Construction { get; set; } = new RoomConstructionData();
        public RoomMeasurementsData Measurements { get; set; } = new RoomMeasurementsData();
    }

    public class RoomConstructionData
    {
        public RoomTypesData Type { get; set; } = new RoomTypesData();
        public RoomFloorsData Floor { get; set; } = new RoomFloorsData();
        public CodeAndTextData FoundationBelow { get; set; } = new CodeAndTextData();
    }

    public class RoomMeasurementsData
    {
        public bool IsRectangular { get; set; }
        public decimal Height { get; set; }
        public decimal Width { get; set; }
        public decimal Depth { get; set; }
        public decimal Perimeter { get; set; }
        public decimal Area { get; set; }
    }

    public class RoomTypesData
    {
        public string Code { get; set; } = string.Empty;
        public string English { get; set; } = string.Empty;
        public string French { get; set; } = string.Empty;
        public bool IsUserSpecified { get; set; }
    }

    public class RoomFloorsData
    {
        public string Code { get; set; } = string.Empty;
        public string English { get; set; } = string.Empty;
        public string French { get; set; } = string.Empty;
        public bool IsUserSpecified { get; set; }
    }

}
