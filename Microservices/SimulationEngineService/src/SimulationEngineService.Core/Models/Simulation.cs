using System;
using System.Collections.Generic;

namespace SimulationEngineService.Core.Models
{
    public class Simulation
    {
        public Guid Id { get; set; }
        public string Name { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public SimulationStatus Status { get; set; } = SimulationStatus.Pending;
        public DateTime CreatedDate { get; set; }
        public DateTime? StartedDate { get; set; }
        public DateTime? CompletedDate { get; set; }

        // References to data in other services
        public Guid HouseId { get; set; }
        public Guid? EnergyUpgradeId { get; set; }

        // Simulation parameters
        public SimulationParameters Parameters { get; set; } = new SimulationParameters();

        // Simulation results
        public virtual ICollection<SimulationResult> Results { get; set; } = new List<SimulationResult>();
    }

    public class SimulationParameters
    {
        public Guid Id { get; set; }
        
        // Basic simulation settings
        public bool IsMetric { get; set; } = true;
        public string LanguageCode { get; set; } = "en";
        
        // Simulation period
        public DateTime StartDate { get; set; } = new DateTime(DateTime.Now.Year, 1, 1);
        public DateTime EndDate { get; set; } = new DateTime(DateTime.Now.Year, 12, 31);
        
        // Simulation configuration
        public string SimulationType { get; set; } = "Annual";
        public bool IncludeHeatLoss { get; set; } = true;
        public bool IncludeEnergyConsumption { get; set; } = true;
        public bool IncludeGHGEmissions { get; set; } = true;
        
        // Advanced simulation parameters
        public int TimeStepMinutes { get; set; } = 60;
        public double ConvergenceTolerance { get; set; } = 0.001;
        public int MaxIterations { get; set; } = 100;
        
        // Skip certain calculation components
        public bool SkipHvacCalculation { get; set; } = false;
        
        // Custom parameters as key-value pairs
        public Dictionary<string, string> CustomParameters { get; set; } = new Dictionary<string, string>();
    }
    
    public enum SimulationStatus
    {
        Pending,
        InProgress,
        Completed,
        Failed,
        Cancelled
    }
}