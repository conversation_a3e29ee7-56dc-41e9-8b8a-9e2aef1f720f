using System;
using System.Collections.Generic;
using System.Text.Json.Serialization;

namespace SimulationEngineService.Core.Models
{
    public class EnvelopCeilingData
    {
        public Guid Id { get; set; }
        public Guid HouseId { get; set; }
        public Guid? EnergyUpgradeId { get; set; }
        public string Label { get; set; } = string.Empty;
        
        // Construction properties
        public CeilingConstruction Construction { get; set; } = new CeilingConstruction();
        
        // Measurement properties
        public CeilingMeasurements Measurements { get; set; } = new CeilingMeasurements();
    }

    public class CeilingConstruction
    {
        public Guid Id { get; set; }
        public Guid CeilingId { get; set; }
        public CeilingType Type { get; set; } = new CeilingType();
        public CeilingTypeReference CeilingTypeReference { get; set; } = new CeilingTypeReference();
    }

    public class CeilingType
    {
        public string Code { get; set; } = string.Empty;
        public string EnglishText { get; set; } = string.Empty;
        public string FrenchText { get; set; } = string.Empty;
        public bool IsUserSpecified { get; set; }
    }

    public class CeilingTypeReference
    {
        public Guid Id { get; set; }
        public string IdRef { get; set; } = string.Empty;
        public string Code { get; set; } = string.Empty;
        public double RValue { get; set; }
        public double NominalInsulation { get; set; }
        public string Text { get; set; } = string.Empty;
        public List<UserDefinedCodeLayer> UserDefinedCodeLayers { get; set; } = new List<UserDefinedCodeLayer>();
    }

    public class CeilingMeasurements
    {
        public Guid Id { get; set; }
        public Guid CeilingId { get; set; }
        public double Area { get; set; }
        public double Length { get; set; }
        public double HeelHeight { get; set; }
        public CeilingSlope Slope { get; set; } = new CeilingSlope();
    }

    public class CeilingSlope
    {
        public string Code { get; set; } = string.Empty;
        public string EnglishText { get; set; } = string.Empty;
        public string FrenchText { get; set; } = string.Empty;
        public double Value { get; set; }
        public bool IsUserSpecified { get; set; }
    }

}