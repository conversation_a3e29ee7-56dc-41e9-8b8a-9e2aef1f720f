using System;

namespace SimulationEngineService.Core.Models
{
    /// <summary>
    /// Window data model for simulation engine - matches WindowDto structure
    /// </summary>
    public class EnvelopWindowData
    {
        public Guid Id { get; set; }
        public Guid HouseId { get; set; }
        public Guid? EnergyUpgradeId { get; set; }
        public string Label { get; set; } = string.Empty;

        // Window-specific properties
        public decimal Number { get; set; } = 1m;
        public decimal Er { get; set; }
        public decimal Shgc { get; set; }
        public decimal FrameHeight { get; set; }
        public decimal FrameAreaFraction { get; set; }
        public decimal EdgeOfGlassFraction { get; set; }
        public decimal CentreOfGlassFraction { get; set; }
        public bool AdjacentEnclosedSpace { get; set; }

        // Facing Direction
        public WindowDirectionsData FacingDirection { get; set; } = new WindowDirectionsData();

        // Navigation properties
        public WindowConstructionData Construction { get; set; } = new WindowConstructionData();
        public WindowMeasurementsData Measurements { get; set; } = new WindowMeasurementsData();
        public WindowShadingData Shading { get; set; } = new WindowShadingData();
        public EnergyStarData EnergyStar { get; set; } = new EnergyStarData();
    }

    public class WindowDirectionsData
    {
        public string Code { get; set; } = string.Empty;
        public string English { get; set; } = string.Empty;
        public string French { get; set; } = string.Empty;
        public bool IsUserSpecified { get; set; }
    }

    public class WindowConstructionData
    {
        public bool EnergyStar { get; set; }
        public CodeReferenceData Type { get; set; } = new CodeReferenceData();
    }

    public class WindowMeasurementsData
    {
        public decimal Height { get; set; } = 500m;
        public decimal Width { get; set; } = 500m;
        public decimal HeaderHeight { get; set; }
        public decimal OverhangWidth { get; set; }
        public WindowTiltsData Tilt { get; set; } = new WindowTiltsData();
    }

    public class WindowShadingData
    {
        public decimal Curtain { get; set; } = 1m;
        public decimal ShutterRValue { get; set; }
    }

    public class EnergyStarData
    {
        public string ERText { get; set; } = string.Empty;
        public string UValueMinText { get; set; } = string.Empty;
        public string UValueMaxText { get; set; } = string.Empty;

        // Computed properties
        public int? EnergyRating
        {
            get
            {
                if (int.TryParse(ERText, out int tempER))
                    return tempER;
                else
                    return null;
            }
        }

        public decimal? UValueMin
        {
            get
            {
                if (decimal.TryParse(UValueMinText, out decimal tempMin))
                    return tempMin;
                else
                    return null;
            }
        }

        public decimal? UValueMax
        {
            get
            {
                if (decimal.TryParse(UValueMaxText, out decimal tempMax))
                    return tempMax;
                else
                    return null;
            }
        }
    }

    public class WindowTiltsData
    {
        public string Code { get; set; } = string.Empty;
        public string English { get; set; } = string.Empty;
        public string French { get; set; } = string.Empty;
        public decimal Value { get; set; }
        public bool IsUserSpecified { get; set; }
    }

}
