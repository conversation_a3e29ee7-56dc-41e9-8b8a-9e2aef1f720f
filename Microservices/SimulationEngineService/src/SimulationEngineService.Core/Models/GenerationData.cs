using System;
using System.Collections.Generic;

namespace SimulationEngineService.Core.Models
{
    /// <summary>
    /// Generation data model for simulation engine
    /// Mirrors the structure of GenerationDto from Generation service exactly
    /// </summary>
    public class GenerationData
    {
        public Guid Id { get; set; }
        public Guid HouseId { get; set; }
        public Guid? EnergyUpgradeId { get; set; }
        public string Label { get; set; } = "Generation";

        // Wind energy properties
        public decimal? WindEnergyContribution { get; set; }

        // Solar properties
        public bool SolarReady { get; set; } = false;
        public decimal? PhotovoltaicCapacity { get; set; }
        public bool BatteryStorage { get; set; } = false;

        // Photovoltaic systems collection
        public List<PhotovoltaicData> PhotovoltaicSystems { get; set; } = new List<PhotovoltaicData>();
    }

    /// <summary>
    /// Photovoltaic system data for simulation
    /// </summary>
    public class PhotovoltaicData
    {
        public Guid Id { get; set; }
        public Guid GenerationId { get; set; }
        public ushort Rank { get; set; } = 1;

        // Navigation properties
        public EquipmentInformation EquipmentInformation { get; set; } = new EquipmentInformation();
        public ArrayData Array { get; set; } = new ArrayData();
        public EfficiencyData Efficiency { get; set; } = new EfficiencyData();
        public ModuleData Module { get; set; } = new ModuleData();
    }



    /// <summary>
    /// Array configuration data
    /// </summary>
    public class ArrayData
    {
        public decimal Area { get; set; } = 0.0m;
        public decimal Slope { get; set; } = 0.0m;
        public decimal Azimuth { get; set; } = 0.0m;
    }

    /// <summary>
    /// Efficiency settings data
    /// </summary>
    public class EfficiencyData
    {
        public decimal MiscellaneousLosses { get; set; } = 3.0m;
        public decimal OtherPowerLosses { get; set; } = 1.0m;
        public decimal InverterEfficiency { get; set; } = 90.0m;
        public decimal GridAbsorptionRate { get; set; } = 90.0m;
    }

    /// <summary>
    /// Module configuration data
    /// </summary>
    public class ModuleData
    {
        public decimal Efficiency { get; set; } = 14.2m;
        public decimal CellTemperature { get; set; } = 45.0m;
        public decimal CoefficientOfEfficiency { get; set; } = 0.72m;

        // Resource property for module type
        public PhotovoltaicModuleTypeData Type { get; set; } = new PhotovoltaicModuleTypeData();
    }

    /// <summary>
    /// Resource data for photovoltaic module types
    /// </summary>
    public class PhotovoltaicModuleTypeData
    {
        public string Code { get; set; } = "6"; // UserSpecified default
        public string English { get; set; } = "User specified";
        public string French { get; set; } = "Spécifié par l'utilisateur";
        public bool IsUserSpecified { get; set; } = true;
    }
}
