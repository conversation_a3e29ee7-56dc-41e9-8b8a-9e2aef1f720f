using System;
using System.Collections.Generic;
using System.Linq;
using System.Text.Json.Serialization;

namespace SimulationEngineService.Core.Models
{
    public class BaseloadData
    {
        public Guid Id { get; set; }
        public Guid HouseId { get; set; }
        public Guid? EnergyUpgradeId { get; set; }
        public decimal BasementFractionOfInternalGains { get; set; } = 0.15m;
        public decimal CommonSpaceElectricalConsumption { get; set; }
        // Occupancy properties
        public OccupancyData Occupancy { get; set; } = new OccupancyData();
        
        // Energy usage properties
        public SummaryData Summary { get; set; } = new SummaryData();
        public WaterUsageData WaterUsage { get; set; } = new WaterUsageData();
        public ElectricalUsageData ElectricalUsage { get; set; } = new ElectricalUsageData();
        
        // Advanced user specified properties (for gas appliances, etc.)
        public AdvancedUserSpecifiedData AdvancedUserSpecified { get; set; } = new AdvancedUserSpecifiedData();
    }

    public class OccupancyData
    {
        public bool IsHouseOccupied { get; set; } = true;
        public OccupantsAtHomeData Adults { get; set; } = new OccupantsAtHomeData { OccupantType = OccupantType.Adults, Occupants = 2, AtHome = 65 };
        public OccupantsAtHomeData Children { get; set; } = new OccupantsAtHomeData { OccupantType = OccupantType.Children, Occupants = 1, AtHome = 50 };
        public OccupantsAtHomeData Infants { get; set; } = new OccupantsAtHomeData { OccupantType = OccupantType.Infants, Occupants = 0, AtHome = 0 };
    }

    public class OccupantsAtHomeData
    {
        public OccupantType OccupantType { get; set; }
        public int Occupants { get; set; }
        public decimal AtHome { get; set; }
    }

    public enum OccupantType
    {
        Adults,
        Children,
        Infants
    }

    public class SummaryData
    {
        public bool IsSpecified { get; set; }
        public decimal ElectricalAppliances { get; set; } = 6.30m;
        public decimal Lighting { get; set; } = 2.60m;
        public decimal OtherElectric { get; set; } = 9.70m;
        public decimal ExteriorUse { get; set; } = 0.90m;
        public decimal HotWaterLoad { get; set; } = 187.63m;
    }

    public class WaterUsageData
    {
        public decimal Temperature { get; set; } = 55;
        public int LowFlushToilets { get; set; } = 2;
        public decimal OtherHotWaterUse { get; set; } = 225;
        
        public BathroomFaucetsData BathroomFaucets { get; set; } = new BathroomFaucetsData();
        public ShowerData Shower { get; set; } = new ShowerData();
        public ClothesWasherData ClothesWasher { get; set; } = new ClothesWasherData();
        public DishWasherData DishWasher { get; set; } = new DishWasherData();
    }

    public class BathroomFaucetsData : CodeAndTextData
    {
        public double Value { get; set; }
        public double NumberPerOccupantPerDay { get; set; }
    }

    public class ShowerData
    {
        public double AverageDuration { get; set; }
        public double NumberPerOccupantPerWeek { get; set; }
        public double TotalDurationPerDay { get; set; }
        public ShowerTemperatureData Temperature { get; set; } = new ShowerTemperatureData();
        public ShowerFlowRateData FlowRate { get; set; } = new ShowerFlowRateData();
    }

    public class ShowerTemperatureData : CodeAndTextData
    {
        public double Value { get; set; }
        public bool IsUserSpecified { get; set; }
    }

    public class ShowerFlowRateData : CodeAndTextData
    {
        public double Value { get; set; }
        public bool IsUserSpecified { get; set; }
    }

    public class ClothesWasherData
    {
        public double NumberPerOccupantPerWeek { get; set; }
        public RatedValueData RatedValues { get; set; } = new RatedValueData();
        public ClothesWasherTemperatureData Temperature { get; set; } = new ClothesWasherTemperatureData();
    }

    public class ClothesWasherTemperatureData : CodeAndTextData
    {
        public bool IsUserSpecified { get; set; }
    }

    public class DishWasherData
    {
        public double NumberPerOccupantPerWeek { get; set; }
        public RatedValueData RatedValues { get; set; } = new RatedValueData();
    }

    public class RatedValueData : CodeAndTextData
    {
        public double RatedAnnualEnergyConsumption { get; set; }
        public double RatedWaterConsumptionPerCycle { get; set; }
    }

    public class ElectricalUsageData
    {
        public decimal OtherLoad { get; set; } = 9.7m;
        public decimal AverageExteriorUse { get; set; } = 0.9m;
        public ClothesDryerData ClothesDryer { get; set; } = new ClothesDryerData();
        public StoveData Stove { get; set; } = new StoveData();
        public CodeAndTextData Refrigerator { get; set; } = new CodeAndTextData();
        public CodeAndTextData InteriorLighting { get; set; } = new CodeAndTextData();
    }

    public class ClothesDryerData
    {
        public ApplianceEnergySourceData EnergySource { get; set; } = new ApplianceEnergySourceData();
        public CodeAndTextData RatedValue { get; set; } = new CodeAndTextData();
        public CodeAndTextData Location { get; set; } = new CodeAndTextData();
    }

    public class StoveData
    {
        public ApplianceEnergySourceData EnergySource { get; set; } = new ApplianceEnergySourceData();
        public CodeAndTextData RatedValue { get; set; } = new CodeAndTextData();
    }

    public class ApplianceEnergySourceData : CodeAndTextData
    {
    }

    public class AdvancedUserSpecifiedData
    {
        public decimal HotWaterTemperature { get; set; } = 55m;
        public ApplianceEnergySourceSpecifiedData GasStove { get; set; } = new ApplianceEnergySourceSpecifiedData();
        public ApplianceEnergySourceSpecifiedData GasDryer { get; set; } = new ApplianceEnergySourceSpecifiedData();
        public CodeAndTextData DryerLocation { get; set; } = new CodeAndTextData();
    }

    public class ApplianceEnergySourceSpecifiedData : CodeAndTextData
    {
        public decimal Value { get; set; }
    }

    public class CodeAndTextData
    {
        public string Code { get; set; } = string.Empty;
        public string EnglishText { get; set; } = string.Empty;
        public string FrenchText { get; set; } = string.Empty;
        public string Text { get; set; } = string.Empty;
    }
}