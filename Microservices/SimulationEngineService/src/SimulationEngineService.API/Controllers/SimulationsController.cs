using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using AutoMapper;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using SimulationEngineService.API.Models;
using SimulationEngineService.Core.Interfaces;
using SimulationEngineService.Core.Models;
using SimulationEngineService.Infrastructure.Data;

namespace SimulationEngineService.API.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class SimulationsController : ControllerBase
    {
        private readonly ISimulationService _simulationService;
        private readonly IMapper _mapper;
        private readonly ILogger<SimulationsController> _logger;
        
        public SimulationsController(
            ISimulationService simulationService,
            IMapper mapper,
            ILogger<SimulationsController> logger)
        {
            _simulationService = simulationService ?? throw new ArgumentNullException(nameof(simulationService));
            _mapper = mapper ?? throw new ArgumentNullException(nameof(mapper));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        }
        
        [HttpGet]
        public async Task<ActionResult<IEnumerable<SimulationDto>>> GetAllSimulations()
        {
            var simulations = await _simulationService.GetAllSimulationsAsync();
            return Ok(_mapper.Map<IEnumerable<SimulationDto>>(simulations));
        }
        
        [HttpGet("{id}")]
        public async Task<ActionResult<SimulationDto>> GetSimulationById(Guid id)
        {
            var simulation = await _simulationService.GetSimulationByIdAsync(id);
            if (simulation == null)
            {
                return NotFound();
            }
            
            return Ok(_mapper.Map<SimulationDto>(simulation));
        }
        
        [HttpGet("coldwater/average/{houseId}")]
        public async Task<ActionResult<decimal>> GetAverageColdWaterTemperature(Guid houseId)
        {
            try
            {
                _logger.LogInformation("Getting average cold water temperature for house ID: {HouseId}", houseId);

                // Check if a simulation result already exists
                var existingResult = await _simulationService.GetSimulationResultByHouseIdAsync(houseId);

                // If no result exists or the result is outdated, create a new simulation
                if (existingResult == null || existingResult.CompletedAt < DateTime.UtcNow.AddDays(-1)) // Consider results older than 1 day as outdated
                {
                    _logger.LogInformation("No recent simulation result found. Running a new simulation for house ID: {HouseId}", houseId);

                    // Create default parameters for the simulation
                    var defaultParameters = new SimulationParameters
                    {
                        SimulationType = "EnergySimulation",
                        LanguageCode = "en",
                        IsMetric = true,
                        StartDate = new DateTime(DateTime.UtcNow.Year, 1, 1),
                        EndDate = new DateTime(DateTime.UtcNow.Year, 12, 31),
                        TimeStepMinutes = 60,
                        ConvergenceTolerance = 0.01
                    };

                    // Run the simulation
                    existingResult = await _simulationService.CreateAndProcessByHouseIdAsync(houseId, defaultParameters);

                    _logger.LogInformation("Simulation completed successfully for house ID: {HouseId}", houseId);
                }

                // Now we should have a valid result, return the average cold water temperature
                _logger.LogInformation("Returning average cold water temperature: {Temp}°C",
                    existingResult.AverageColdWaterTemperature);

                return Ok(existingResult.AverageColdWaterTemperature);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting average cold water temperature: {Message}", ex.Message);
                return StatusCode(500, $"Error getting average cold water temperature: {ex.Message}");
            }
        }

        
        [HttpGet("house/{houseId}")]
        public async Task<ActionResult<IEnumerable<SimulationDto>>> GetSimulationsByHouseId(Guid houseId)
        {
            var simulations = await _simulationService.GetSimulationsByHouseIdAsync(houseId);
            return Ok(_mapper.Map<IEnumerable<SimulationDto>>(simulations));
        }
        
        [HttpPost("create-and-process-by-house-id")]
        public async Task<ActionResult<SimulationResultDto>> CreateAndProcessByHouseId(SimulationDto createDto)
        {
            try
            {
                _logger.LogInformation("Creating and processing simulation for house ID: {HouseId}", createDto.HouseId);
                
                // Map DTO to parameters
                var parameters = _mapper.Map<SimulationParameters>(createDto.Parameters);
                
                // Call the service method
                var result = await _simulationService.CreateAndProcessByHouseIdAsync(createDto.HouseId, parameters);
                
                // Map to DTO
                var resultDto = _mapper.Map<SimulationResultDto>(result);
                
                _logger.LogInformation("Simulation created and processed successfully for house {HouseId}", createDto.HouseId);
                
                return Ok(resultDto);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating and processing simulation by house ID: {Message}", ex.Message);
                return StatusCode(500, $"Error creating and processing simulation: {ex.Message}");
            } 
        }

        [HttpGet("results/house/{houseId}")]
        public async Task<ActionResult<SimulationResultDto>> GetSimulationResultByHouseId(Guid houseId)
        {
            _logger.LogInformation("Getting simulation result for house ID: {HouseId}", houseId);
            
            var result = await _simulationService.GetSimulationResultByHouseIdAsync(houseId);
            if (result == null)
            {
                return NotFound();
            }
            
            return Ok(_mapper.Map<SimulationResultDto>(result));
        }
        
        [HttpGet("{id}/status")]
        public async Task<ActionResult<string>> GetSimulationStatus(Guid id)
        {
            try
            {
                _logger.LogInformation("Checking status for simulation ID: {SimulationId}", id);
                Console.WriteLine($"Checking status for simulation ID: {id}");
                
                var status = await _simulationService.GetSimulationStatusAsync(id);
                
                _logger.LogInformation("Simulation {SimulationId} status: {Status}", id, status);
                Console.WriteLine($"Simulation {id} status: {status}");
                
                return Ok(status.ToString());
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error checking simulation status: {Message}", ex.Message);
                Console.WriteLine($"ERROR checking simulation status: {ex.Message}");
                throw;
            }
        }
        
        [HttpDelete("{id}")]
        public async Task<ActionResult> DeleteSimulation(Guid id)
        {
            var result = await _simulationService.DeleteSimulationAsync(id);
            if (!result)
            {
                return NotFound();
            }
            
            return NoContent();
        }

        // GET: api/simulations/energy-upgrades/{energyUpgradeId}/simulations
        [HttpGet("energy-upgrades/{energyUpgradeId}/simulations")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<ActionResult<SimulationDto>> GetSimulationsByEnergyUpgradeId(Guid energyUpgradeId)
        {
            try
            {
                _logger.LogInformation("Getting simulation by energy upgrade ID: {EnergyUpgradeId}", energyUpgradeId);

                var simulation = await _simulationService.GetSimulationByEnergyUpgradeIdAsync(energyUpgradeId);

                if (simulation == null)
                {
                    return NotFound(new { Error = "Simulation not found for energy upgrade", EnergyUpgradeId = energyUpgradeId });
                }

                var simulationDto = _mapper.Map<SimulationDto>(simulation);
                return Ok(simulationDto);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting simulation by energy upgrade ID: {EnergyUpgradeId}", energyUpgradeId);
                return StatusCode(500, new { Error = "Internal server error" });
            }
        }

        // POST: api/simulations/{baseSimulationId}/duplicate-for-energy-upgrade
        [HttpPost("{baseSimulationId}/duplicate-for-energy-upgrade")]
        [ProducesResponseType(StatusCodes.Status201Created)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<ActionResult<SimulationDto>> DuplicateSimulationForEnergyUpgrade(
            Guid baseSimulationId,
            [FromQuery] Guid energyUpgradeId)
        {
            try
            {
                _logger.LogInformation("Duplicating simulation {BaseSimulationId} for energy upgrade {EnergyUpgradeId}",
                    baseSimulationId, energyUpgradeId);

                var baseSimulation = await _simulationService.GetSimulationByIdAsync(baseSimulationId);

                if (baseSimulation == null)
                {
                    return NotFound(new { Error = "Base simulation not found", BaseSimulationId = baseSimulationId });
                }

                var duplicatedSimulation = await _simulationService.DuplicateSimulationForEnergyUpgradeAsync(
                    baseSimulation, energyUpgradeId);

                var simulationDto = _mapper.Map<SimulationDto>(duplicatedSimulation);

                return CreatedAtAction(
                    nameof(GetSimulationById),
                    new { id = simulationDto.Id },
                    simulationDto);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error duplicating simulation {BaseSimulationId} for energy upgrade {EnergyUpgradeId}",
                    baseSimulationId, energyUpgradeId);
                return StatusCode(500, new { Error = "Internal server error" });
            }
        }
    }
}