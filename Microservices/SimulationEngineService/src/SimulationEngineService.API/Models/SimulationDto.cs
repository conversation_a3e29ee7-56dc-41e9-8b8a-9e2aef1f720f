using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using SimulationEngineService.Core.Models;

namespace SimulationEngineService.API.Models
{

    public class SimulationDto
    {
        public Guid Id { get; set; }
        public string Name { get; set; }
        public string Description { get; set; }
        public string Status { get; set; }
        public DateTime CreatedDate { get; set; }
        public DateTime? StartedDate { get; set; }
        public DateTime? CompletedDate { get; set; }

        public Guid HouseId { get; set; }
        public Guid? EnergyUpgradeId { get; set; }

        public SimulationParametersDto Parameters { get; set; }
    }

    public class SimulationParametersDto
    {
        public bool IsMetric { get; set; } = true;
        public string LanguageCode { get; set; } = "en";
        
        public DateTime StartDate { get; set; } = new DateTime(DateTime.Now.Year, 1, 1);
        public DateTime EndDate { get; set; } = new DateTime(DateTime.Now.Year, 12, 31);
        
        public string SimulationType { get; set; } = "Annual";
        public bool IncludeHeatLoss { get; set; } = true;
        public bool IncludeEnergyConsumption { get; set; } = true;
        public bool IncludeGHGEmissions { get; set; } = true;
        
        public int TimeStepMinutes { get; set; } = 60;
        public double ConvergenceTolerance { get; set; } = 0.001;
        public int MaxIterations { get; set; } = 100;
        
        public bool SkipHvacCalculation { get; set; } = false;
        
        public Dictionary<string, string> CustomParameters { get; set; } = new Dictionary<string, string>();
    }
}