using Microsoft.EntityFrameworkCore;
using System.Text.Json;
using SimulationEngineService.Core.Models;

namespace SimulationEngineService.Infrastructure.Data
{
    public class SimulationDbContext : DbContext
    {
        public SimulationDbContext(DbContextOptions<SimulationDbContext> options) : base(options)
        {
        }
        
        public DbSet<Simulation> Simulations { get; set; } = null!;
        public DbSet<SimulationResult> SimulationResults { get; set; } = null!;
        public DbSet<MonthlyResult> MonthlyResults { get; set; } = null!;
        public DbSet<StoredSimulationData> StoredSimulationData { get; set; } = null!;
        
        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            // Set default schema for all entities in this context
            modelBuilder.HasDefaultSchema("simulation");
            
            base.OnModelCreating(modelBuilder);
            
            // Configure Simulation entity
            modelBuilder.Entity<Simulation>(entity =>
            {
                entity.ToTable("Simulations", "simulation"); // Specify schema
                entity.HasKey(e => e.Id);
                entity.Property(e => e.Name).IsRequired().HasMaxLength(255);
                entity.Property(e => e.Description).HasMaxLength(1000);
                entity.Property(e => e.Status).IsRequired();
                entity.Property(e => e.CreatedDate).IsRequired();
                entity.Property(e => e.EnergyUpgradeId).IsRequired(false);
                
                // Store JSON parameters
                entity.Property(e => e.Parameters)
                    .HasConversion(
                        v => JsonSerializer.Serialize(v, new JsonSerializerOptions()),
                        v => JsonSerializer.Deserialize<SimulationParameters>(v, new JsonSerializerOptions()));
            
            });
            
            // Configure SimulationResult entity
            modelBuilder.Entity<SimulationResult>(entity =>
            {
                entity.ToTable("SimulationResults", "simulation"); // Specify schema
                entity.HasKey(e => e.Id);
                entity.Property(e => e.SimulationId).IsRequired();
                entity.Property(e => e.CreatedDate).IsRequired();
                
                // Define relationship with Simulation
                entity.HasOne(e => e.Simulation)
                    .WithMany(s => s.Results)
                    .HasForeignKey(e => e.SimulationId)
                    .OnDelete(DeleteBehavior.Cascade);
            });
            
            // Configure MonthlyResult entity
            modelBuilder.Entity<MonthlyResult>(entity =>
            {
                entity.ToTable("MonthlyResults", "simulation"); // Specify schema
                entity.HasKey(e => e.Id);
                entity.Property(e => e.SimulationResultId).IsRequired();
                entity.Property(e => e.Month).IsRequired();
                entity.Property(e => e.Year).IsRequired();
                
                // Define relationship with SimulationResult
                entity.HasOne(e => e.SimulationResult)
                    .WithMany(s => s.MonthlyResults)
                    .HasForeignKey(e => e.SimulationResultId)
                    .OnDelete(DeleteBehavior.Cascade);
            });
            // modelBuilder.Entity<EnvelopeResults>().HasNoKey();
            // modelBuilder.Entity<SimulationResult>()
            //     .Ignore(s => s.EnvelopeResults);
                
            // modelBuilder.Entity<MonthlyEnergyResults>().HasNoKey();
            // modelBuilder.Entity<SimulationResult>()
            //     .Ignore(s => s.MonthlyEnergyResults);

            // modelBuilder.Entity<InternalGainsResults>().HasNoKey();
            // modelBuilder.Entity<SimulationResult>()
            //     .Ignore(s => s.InternalGainsResults);
       
            // modelBuilder.Entity<HvacResults>().HasNoKey();
            // modelBuilder.Entity<SimulationResult>()
            //     .Ignore(s => s.HvacResults);
            
            modelBuilder.Entity<StoredSimulationData>(entity =>
            {
                entity.ToTable("StoredSimulationData", "simulation"); // Specify schema
                entity.HasKey(e => e.Id);
                entity.HasIndex(e => e.HouseId).IsUnique();
            });
            
            // modelBuilder.Entity<AnnualEnergyResults>().HasNoKey();
            // modelBuilder.Entity<SimulationResult>()
            //     .Ignore(s => s.AnnualEnergyResults);
        }
    }
}