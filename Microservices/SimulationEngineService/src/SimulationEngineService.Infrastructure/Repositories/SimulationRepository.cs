using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using SimulationEngineService.Core.Interfaces;
using SimulationEngineService.Core.Models;
using SimulationEngineService.Infrastructure.Data;
using System.Text.Json;

namespace SimulationEngineService.Infrastructure.Repositories
{
    public class SimulationRepository : ISimulationRepository
    {
        private readonly SimulationDbContext _context;
        private readonly ILogger<SimulationRepository> _logger;
        private static string? _connectionString;
        
        public SimulationRepository(
            SimulationDbContext context,
            ILogger<SimulationRepository> logger)
        {
            _context = context ?? throw new ArgumentNullException(nameof(context));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            
            // Cache the connection string for future use in case of disposed contexts
            if (_connectionString == null)
            {
                _connectionString = _context.Database.GetConnectionString();
            }
        }

        public async Task<bool> StoreAsync(Guid houseId, SimulationDataPackage dataPackage)
        {
            try
            {
                var existingData = await _context.StoredSimulationData
                    .FirstOrDefaultAsync(s => s.HouseId == houseId);

                if (existingData != null)
                {
                    // Update existing
                    existingData.ModifiedDate = DateTime.UtcNow;
                    existingData.HouseDataJson = dataPackage.HouseDataJson;
                    existingData.WeatherDataJson = dataPackage.WeatherDataJson;
                    existingData.WallsDataJson = dataPackage.WallsDataJson;
                    existingData.CeilingsDataJson = dataPackage.CeilingsDataJson;
                    existingData.FloorsDataJson = dataPackage.FloorsDataJson;
                    existingData.DoorsDataJson = dataPackage.DoorsDataJson;
                    existingData.WindowsDataJson = dataPackage.WindowsDataJson;
                    existingData.BaseloadDataJson = dataPackage.BaseloadDataJson;
                    existingData.HvacDataJson = dataPackage.HvacDataJson;
                    existingData.HotWaterDataJson = dataPackage.HotWaterDataJson;
                    existingData.AirInfiltrationDataJson = dataPackage.AirInfiltrationDataJson;
                    existingData.GenerationDataJson = dataPackage.GenerationDataJson;
                    existingData.SimulationType = dataPackage.SimulationType;
                    existingData.LanguageCode = dataPackage.LanguageCode;
                    existingData.IsMetric = dataPackage.IsMetric;
                    existingData.StartDate = dataPackage.StartDate;
                    existingData.EndDate = dataPackage.EndDate;
                    existingData.TimeStepMinutes = dataPackage.TimeStepMinutes;
                    existingData.ConvergenceTolerance = dataPackage.ConvergenceTolerance;
                }
                else
                {
                    // Create new
                    var storedData = new StoredSimulationData
                    {
                        Id = Guid.NewGuid(),
                        HouseId = houseId,
                        CreatedDate = DateTime.UtcNow,
                        HouseDataJson = dataPackage.HouseDataJson,
                        WeatherDataJson = dataPackage.WeatherDataJson,
                        WallsDataJson = dataPackage.WallsDataJson,
                        CeilingsDataJson = dataPackage.CeilingsDataJson,
                        FloorsDataJson = dataPackage.FloorsDataJson,
                        DoorsDataJson = dataPackage.DoorsDataJson,
                        WindowsDataJson = dataPackage.WindowsDataJson,
                        BaseloadDataJson = dataPackage.BaseloadDataJson,
                        HvacDataJson = dataPackage.HvacDataJson,
                        HotWaterDataJson = dataPackage.HotWaterDataJson,
                        AirInfiltrationDataJson = dataPackage.AirInfiltrationDataJson,
                        GenerationDataJson = dataPackage.GenerationDataJson,
                        SimulationType = dataPackage.SimulationType,
                        LanguageCode = dataPackage.LanguageCode,
                        IsMetric = dataPackage.IsMetric,
                        StartDate = dataPackage.StartDate,
                        EndDate = dataPackage.EndDate,
                        TimeStepMinutes = dataPackage.TimeStepMinutes,
                        ConvergenceTolerance = dataPackage.ConvergenceTolerance
                    };

                    await _context.StoredSimulationData.AddAsync(storedData);
                }

                await _context.SaveChangesAsync();
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error storing simulation data for house {HouseId}: {Message}",
                    houseId, ex.Message);
                return false;
            }
        }


        public async Task<SimulationDataPackage> GetByHouseIdAsync(Guid houseId)
        {
            try
            {
                var storedData = await _context.StoredSimulationData
                    .FirstOrDefaultAsync(s => s.HouseId == houseId);

                if (storedData == null)
                {
                    return null;
                }

                return new SimulationDataPackage
                {
                    HouseId = houseId,
                    HouseDataJson = storedData.HouseDataJson,
                    WeatherDataJson = storedData.WeatherDataJson,
                    WallsDataJson = storedData.WallsDataJson,
                    CeilingsDataJson = storedData.CeilingsDataJson,
                    FloorsDataJson = storedData.FloorsDataJson,
                    DoorsDataJson = storedData.DoorsDataJson,
                    WindowsDataJson = storedData.WindowsDataJson,
                    BaseloadDataJson = storedData.BaseloadDataJson,
                    HvacDataJson = storedData.HvacDataJson,
                    HotWaterDataJson = storedData.HotWaterDataJson,
                    AirInfiltrationDataJson = storedData.AirInfiltrationDataJson,
                    GenerationDataJson = storedData.GenerationDataJson,
                    SimulationType = storedData.SimulationType,
                    LanguageCode = storedData.LanguageCode,
                    IsMetric = storedData.IsMetric,
                    StartDate = storedData.StartDate,
                    EndDate = storedData.EndDate,
                    TimeStepMinutes = storedData.TimeStepMinutes,
                    ConvergenceTolerance = storedData.ConvergenceTolerance,
                    CreatedAt = storedData.CreatedDate
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving simulation data for house {HouseId}: {Message}",
                    houseId, ex.Message);
                return null;
            }
        }

        public async Task<bool> ExistsAsync(Guid houseId)
        {
            return await _context.StoredSimulationData.AnyAsync(s => s.HouseId == houseId);
        }

        public async Task<IEnumerable<Simulation>> GetAllSimulationsAsync()
        {
            return await _context.Simulations
                .OrderByDescending(s => s.CreatedDate)
                .ToListAsync();
        }
        
        public async Task<Simulation?> GetSimulationByIdAsync(Guid id)
        {
            return await _context.Simulations
                .FirstOrDefaultAsync(s => s.Id == id);
        }
        
        public async Task<IEnumerable<Simulation>> GetSimulationsByHouseIdAsync(Guid houseId)
        {
            return await _context.Simulations
                .Where(s => s.HouseId == houseId)
                .OrderByDescending(s => s.CreatedDate)
                .ToListAsync();
        }
        
        public async Task<Simulation> CreateSimulationAsync(Simulation simulation)
        {
            _context.Simulations.Add(simulation);
            await _context.SaveChangesAsync();
            return simulation;
        }
        
        public async Task<Simulation> UpdateSimulationAsync(Simulation simulation)
        {
            try
            {
                _context.Entry(simulation).State = EntityState.Modified;
                await _context.SaveChangesAsync();
                return simulation;
            }
            catch (ObjectDisposedException)
            {
                _logger.LogInformation("Context was disposed, creating a new context for updating simulation {SimulationId}", simulation.Id);
                
                // When the original context is disposed (in case of background tasks),
                // create a new one with the same connection string
                if (_connectionString != null)
                {
                    var optionsBuilder = new DbContextOptionsBuilder<SimulationDbContext>();
                    optionsBuilder.UseSqlServer(_connectionString);
                    
                    using var newContext = new SimulationDbContext(optionsBuilder.Options);
                    
                    // Get a fresh copy of the entity from database
                    var dbEntity = await newContext.Simulations.FindAsync(simulation.Id);
                    if (dbEntity == null)
                    {
                        throw new KeyNotFoundException($"Simulation with ID {simulation.Id} not found");
                    }
                    
                    // Update all properties
                    dbEntity.Status = simulation.Status;
                    dbEntity.StartedDate = simulation.StartedDate;
                    dbEntity.CompletedDate = simulation.CompletedDate;
                    dbEntity.Description = simulation.Description;
                    dbEntity.Name = simulation.Name;
                    
                    // Save changes
                    await newContext.SaveChangesAsync();
                    return dbEntity;
                }
                else
                {
                    // Default to a hardcoded connection string for safety
                    var optionsBuilder = new DbContextOptionsBuilder<SimulationDbContext>();
                    optionsBuilder.UseSqlServer("Server=simulation-db;Database=SimulationDb;User=sa;Password=************;TrustServerCertificate=True;");
                    
                    using var newContext = new SimulationDbContext(optionsBuilder.Options);
                    
                    // Get a fresh copy of the entity from database
                    var dbEntity = await newContext.Simulations.FindAsync(simulation.Id);
                    if (dbEntity == null)
                    {
                        throw new KeyNotFoundException($"Simulation with ID {simulation.Id} not found");
                    }
                    
                    // Update all properties
                    dbEntity.Status = simulation.Status;
                    dbEntity.StartedDate = simulation.StartedDate;
                    dbEntity.CompletedDate = simulation.CompletedDate;
                    dbEntity.Description = simulation.Description;
                    dbEntity.Name = simulation.Name;
                    
                    // Save changes
                    await newContext.SaveChangesAsync();
                    return dbEntity;
                }
            }
        }
        
        public async Task<bool> DeleteSimulationAsync(Guid id)
        {
            var simulation = await _context.Simulations.FindAsync(id);
            if (simulation == null)
            {
                return false;
            }
            
            _context.Simulations.Remove(simulation);
            await _context.SaveChangesAsync();
            return true;
        }
        
        public async Task<SimulationResult?> GetSimulationResultByHouseIdAsync(Guid houseId)
        {
            // First find the most recent completed simulation for this house
            var latestSimulation = await _context.Simulations
                .Where(s => s.HouseId == houseId && s.Status == SimulationStatus.Completed)
                .OrderByDescending(s => s.CompletedDate)
                .FirstOrDefaultAsync();
                
            if (latestSimulation == null)
            {
                return null;
            }
            
            // Now get the simulation result for this simulation
            return await _context.SimulationResults
                .Include(r => r.MonthlyResults)
                .FirstOrDefaultAsync(r => r.SimulationId == latestSimulation.Id);
        }
        
        public async Task<SimulationResult?> GetSimulationResultAsync(Guid simulationId)
        {
            return await _context.SimulationResults
                .Include(r => r.MonthlyResults)
                .FirstOrDefaultAsync(r => r.SimulationId == simulationId);
        }
        
        public async Task<SimulationResult> SaveSimulationResultAsync(SimulationResult result)
        {
            try
            {
                // Check if result already exists
                var existingResult = await _context.SimulationResults
                    .FirstOrDefaultAsync(r => r.SimulationId == result.SimulationId);
                    
                if (existingResult != null)
                {
                    // Remove existing result and monthly results
                    _context.MonthlyResults.RemoveRange(
                        _context.MonthlyResults.Where(m => m.SimulationResultId == existingResult.Id));
                    _context.SimulationResults.Remove(existingResult);
                }
                
                // Add the new result
                _context.SimulationResults.Add(result);
                await _context.SaveChangesAsync();
                return result;
            }
            catch (ObjectDisposedException)
            {
                _logger.LogInformation("Context was disposed, creating a new context for saving simulation result {SimulationId}", result.SimulationId);
                
                // When the original context is disposed (in case of background tasks),
                // create a new one with the cached connection string
                if (_connectionString != null)
                {
                    var optionsBuilder = new DbContextOptionsBuilder<SimulationDbContext>();
                    optionsBuilder.UseSqlServer(_connectionString);
                    
                    using var newContext = new SimulationDbContext(optionsBuilder.Options);
                    
                    // Check if result already exists
                    var existingResult = await newContext.SimulationResults
                        .FirstOrDefaultAsync(r => r.SimulationId == result.SimulationId);
                        
                    if (existingResult != null)
                    {
                        // Remove existing result and monthly results
                        newContext.MonthlyResults.RemoveRange(
                            newContext.MonthlyResults.Where(m => m.SimulationResultId == existingResult.Id));
                        newContext.SimulationResults.Remove(existingResult);
                    }
                    
                    // Add the new result
                    newContext.SimulationResults.Add(result);
                    await newContext.SaveChangesAsync();
                    return result;
                }
                else
                {
                    // Default to a hardcoded connection string for safety
                    var optionsBuilder = new DbContextOptionsBuilder<SimulationDbContext>();
                    optionsBuilder.UseSqlServer("Server=simulation-db;Database=SimulationDb;User=sa;Password=************;TrustServerCertificate=True;");
                    
                    using var newContext = new SimulationDbContext(optionsBuilder.Options);
                    
                    // Check if result already exists
                    var existingResult = await newContext.SimulationResults
                        .FirstOrDefaultAsync(r => r.SimulationId == result.SimulationId);
                        
                    if (existingResult != null)
                    {
                        // Remove existing result and monthly results
                        newContext.MonthlyResults.RemoveRange(
                            newContext.MonthlyResults.Where(m => m.SimulationResultId == existingResult.Id));
                        newContext.SimulationResults.Remove(existingResult);
                    }
                    
                    // Add the new result
                    newContext.SimulationResults.Add(result);
                    await newContext.SaveChangesAsync();
                    return result;
                }
            }
        }

        public async Task<Simulation?> GetSimulationByEnergyUpgradeIdAsync(Guid energyUpgradeId)
        {
            _logger.LogInformation("Getting simulation by energy upgrade ID: {EnergyUpgradeId}", energyUpgradeId);

            return await _context.Simulations
                .FirstOrDefaultAsync(s => s.EnergyUpgradeId == energyUpgradeId);
        }

        public async Task<Simulation> DuplicateSimulationForEnergyUpgradeAsync(Simulation baseSimulation, Guid energyUpgradeId)
        {
            _logger.LogInformation("Duplicating simulation {SimulationId} for energy upgrade {EnergyUpgradeId}",
                baseSimulation.Id, energyUpgradeId);

            // Create a deep copy of the simulation
            var duplicatedSimulation = new Simulation
            {
                Id = Guid.NewGuid(),
                Name = baseSimulation.Name,
                Description = baseSimulation.Description,
                Status = SimulationStatus.Pending,
                CreatedDate = DateTime.UtcNow,
                StartedDate = null,
                CompletedDate = null,
                HouseId = baseSimulation.HouseId,
                EnergyUpgradeId = energyUpgradeId,
                Parameters = new SimulationParameters
                {
                    Id = Guid.NewGuid(),
                    IsMetric = baseSimulation.Parameters.IsMetric,
                    LanguageCode = baseSimulation.Parameters.LanguageCode,
                    StartDate = baseSimulation.Parameters.StartDate,
                    EndDate = baseSimulation.Parameters.EndDate,
                    SimulationType = baseSimulation.Parameters.SimulationType,
                    IncludeHeatLoss = baseSimulation.Parameters.IncludeHeatLoss,
                    IncludeEnergyConsumption = baseSimulation.Parameters.IncludeEnergyConsumption,
                    IncludeGHGEmissions = baseSimulation.Parameters.IncludeGHGEmissions,
                    TimeStepMinutes = baseSimulation.Parameters.TimeStepMinutes,
                    ConvergenceTolerance = baseSimulation.Parameters.ConvergenceTolerance,
                    MaxIterations = baseSimulation.Parameters.MaxIterations,
                    SkipHvacCalculation = baseSimulation.Parameters.SkipHvacCalculation,
                    CustomParameters = new Dictionary<string, string>(baseSimulation.Parameters.CustomParameters)
                }
            };

            // Add to context and save
            _context.Simulations.Add(duplicatedSimulation);
            await _context.SaveChangesAsync();

            return duplicatedSimulation;
        }

    }
}