using HouseFileService.Core.Models;
using Microsoft.EntityFrameworkCore;

namespace HouseFileService.Infrastructure.Data
{
    public class HouseFileDbContext : DbContext
    {
        public HouseFileDbContext(DbContextOptions<HouseFileDbContext> options)
            : base(options)
        {
        }
        
        // Main entities
        public DbSet<Housefile> Housefiles { get; set; } = null!;
        public DbSet<ProgramInformation> ProgramInformations { get; set; } = null!;
        public DbSet<Files> Files { get; set; } = null!;
        public DbSet<Client> Clients { get; set; } = null!;
        public DbSet<Justifications> Justifications { get; set; } = null!;
        public DbSet<InfoCodeAndText> InfoCodeAndTexts { get; set; } = null!;
        public DbSet<Specification> Specifications { get; set; } = null!;
        public DbSet<RoofCavity> RoofCavities { get; set; } = null!;
        public DbSet<GableEnds> GableEnds { get; set; } = null!;
        public DbSet<SlopedRoof> SlopedRoofs { get; set; } = null!;
        public DbSet<NumberOf> NumberOfs { get; set; } = null!;
        public DbSet<HeatedFloorArea> HeatedFloorAreas { get; set; } = null!;

        // Hot2K Resource classes are handled as value objects, not separate entities

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            // Set default schema for all entities
            modelBuilder.HasDefaultSchema("housefile");

            // Ignore base classes that are used for composition, not inheritance (following EnvelopeService pattern)
            modelBuilder.Ignore<ResourceList>();
            modelBuilder.Ignore<CodeAndText>();
            modelBuilder.Ignore<CodeTextAndValue>();

            // Configure Housefile entity
            modelBuilder.Entity<Housefile>(entity =>
            {
                entity.ToTable("Housefiles", "housefile");
                entity.HasKey(e => e.Id);
                entity.Property(e => e.Id).ValueGeneratedOnAdd();
                entity.Property(e => e.UserId).IsRequired();
                entity.Property(e => e.HouseName).IsRequired().HasMaxLength(200);
                entity.Property(e => e.EnergyUpgradeId).IsRequired(false);
                entity.Property(e => e.CreatedDate).HasDefaultValueSql("GETUTCDATE()");
                entity.Property(e => e.ModifiedDate).HasDefaultValueSql("GETUTCDATE()");

                // Add index on UserId for better performance when querying by user
                entity.HasIndex(e => e.UserId).HasDatabaseName("IX_Housefiles_UserId");

                // Add index on HouseName for better performance when searching by house name
                entity.HasIndex(e => e.HouseName).HasDatabaseName("IX_Housefiles_HouseName");
            });

            // Configure ProgramInformation entity
            modelBuilder.Entity<ProgramInformation>(entity =>
            {
                entity.ToTable("ProgramInformations", "housefile");
                entity.HasKey(e => e.Id);
                entity.Property(e => e.Id).ValueGeneratedOnAdd();

                // Configure new properties
                entity.Property(e => e.HouseId).IsRequired();
                entity.Property(e => e.Mixed).HasDefaultValue(false);

                // Add index on HouseId if it's frequently queried
                entity.HasIndex(e => e.HouseId).HasDatabaseName("IX_ProgramInformations_HouseId");

                // One-to-one relationships
                entity.HasOne(e => e.Files)
                    .WithOne(f => f.ProgramInformation)
                    .HasForeignKey<Files>(f => f.ProgramInformationId)
                    .OnDelete(DeleteBehavior.Cascade);

                entity.HasOne(e => e.Client)
                    .WithOne(c => c.ProgramInformation)
                    .HasForeignKey<Client>(c => c.ProgramInformationId)
                    .OnDelete(DeleteBehavior.Cascade);

                entity.HasOne(e => e.Justifications)
                    .WithOne(j => j.ProgramInformation)
                    .HasForeignKey<Justifications>(j => j.ProgramInformationId)
                    .OnDelete(DeleteBehavior.Cascade);

                // One-to-many relationship with InfoCodeAndText
                entity.HasMany(e => e.Information)
                    .WithOne(i => i.ProgramInformation)
                    .HasForeignKey(i => i.ProgramInformationId)
                    .OnDelete(DeleteBehavior.Cascade);
            });

            // Configure InfoCodeAndText entity
            modelBuilder.Entity<InfoCodeAndText>(entity =>
            {
                entity.ToTable("InfoCodeAndTexts", "housefile");
                entity.HasKey(e => e.Id);
                entity.Property(e => e.Id).ValueGeneratedOnAdd();
                entity.Property(e => e.ProgramInformationId).IsRequired();
                entity.Property(e => e.Code).IsRequired().HasMaxLength(50);
                entity.Property(e => e.EnglishText).HasMaxLength(500);
                entity.Property(e => e.FrenchText).HasMaxLength(500);
                entity.Property(e => e.Text).HasMaxLength(500);

                // Add index for better performance
                entity.HasIndex(e => e.ProgramInformationId);
            });

            // Configure Files entity with proper ownership columns
            modelBuilder.Entity<Files>(entity =>
            {
                entity.ToTable("Files", "housefile");
                entity.HasKey(e => e.Id);
                entity.Property(e => e.Id).ValueGeneratedOnAdd();
                entity.Property(e => e.Identification).HasMaxLength(100).HasDefaultValue("");
                entity.Property(e => e.PreviousFileId).HasMaxLength(100).HasDefaultValue("");
                entity.Property(e => e.ApplicationNumber).HasMaxLength(100).HasDefaultValue("");
                entity.Property(e => e.EnrollmentId).HasMaxLength(100).HasDefaultValue("");
                entity.Property(e => e.TaxNumber).HasMaxLength(50).HasDefaultValue("");
                entity.Property(e => e.EnteredBy).HasMaxLength(200).HasDefaultValue("");
                entity.Property(e => e.Company).HasMaxLength(200).HasDefaultValue("");
                entity.Property(e => e.BuilderName).HasMaxLength(200).HasDefaultValue("");
                entity.Property(e => e.UserTelephone).HasMaxLength(20).HasDefaultValue("");
                entity.Property(e => e.UserExtension).HasMaxLength(10).HasDefaultValue("");
                entity.Property(e => e.CompanyTelephone).HasMaxLength(20).HasDefaultValue("");
                entity.Property(e => e.CompanyExtension).HasMaxLength(10).HasDefaultValue("");
                entity.Property(e => e.HomeownerAuthorizationId).HasMaxLength(100).HasDefaultValue("");

                // Configure HouseOwnerships as owned entity (following EnvelopeService pattern)
                entity.OwnsOne(e => e.Ownership, owned =>
                {
                    owned.Property(o => o.Code).HasColumnName("OwnershipCode").HasMaxLength(50);
                    owned.Property(o => o.English).HasColumnName("OwnershipEnglish").HasMaxLength(200);
                    owned.Property(o => o.French).HasColumnName("OwnershipFrench").HasMaxLength(200);
                    owned.Property(o => o.IsUserSpecified).HasColumnName("OwnershipIsUserSpecified");
                });

                // Configure HouseOwnerOccupied as owned entity (following EnvelopeService pattern)
                entity.OwnsOne(e => e.OwnerOccupied, owned =>
                {
                    owned.Property(o => o.Code).HasColumnName("OwnerOccupiedCode").HasMaxLength(50);
                    owned.Property(o => o.English).HasColumnName("OwnerOccupiedEnglish").HasMaxLength(200);
                    owned.Property(o => o.French).HasColumnName("OwnerOccupiedFrench").HasMaxLength(200);
                    owned.Property(o => o.IsUserSpecified).HasColumnName("OwnerOccupiedIsUserSpecified");
                });
            });

            // Configure Client entity
            modelBuilder.Entity<Client>(entity =>
            {
                entity.ToTable("Clients", "housefile");
                entity.HasKey(e => e.Id);
                entity.Property(e => e.Id).ValueGeneratedOnAdd();
                entity.Property(e => e.Telephone).HasMaxLength(20);

                // Configure Name as owned entity (simple value object)
                entity.OwnsOne(e => e.Name, owned =>
                {
                    owned.Property(n => n.FirstName).HasColumnName("FirstName").HasMaxLength(100);
                    owned.Property(n => n.LastName).HasColumnName("LastName").HasMaxLength(100);
                });

                // Configure addresses as owned entities
                entity.OwnsOne(e => e.StreetAddress, owned =>
                {
                    owned.Property(a => a.Street).HasColumnName("StreetAddress_Street").HasMaxLength(200);
                    owned.Property(a => a.UnitNumber).HasColumnName("StreetAddress_UnitNumber").HasMaxLength(50);
                    owned.Property(a => a.ProvinceOrTerritory).HasColumnName("StreetAddress_Province").HasMaxLength(50);
                    owned.Property(a => a.PostalCode).HasColumnName("StreetAddress_PostalCode").HasMaxLength(20);

                    // Configure City as owned entity (following EnvelopeService pattern)
                    owned.OwnsOne(a => a.City, city =>
                    {
                        city.Property(c => c.Code).HasColumnName("StreetAddress_CityCode").HasMaxLength(50);
                        city.Property(c => c.EnglishText).HasColumnName("StreetAddress_CityEnglishText").HasMaxLength(200);
                        city.Property(c => c.FrenchText).HasColumnName("StreetAddress_CityFrenchText").HasMaxLength(200);
                        city.Property(c => c.Text).HasColumnName("StreetAddress_CityText").HasMaxLength(200);
                        city.Property(c => c.IsUserSpecified).HasColumnName("StreetAddress_CityIsUserSpecified");
                    });
                });

                entity.OwnsOne(e => e.MailingAddress, owned =>
                {
                    owned.Property(a => a.Name).HasColumnName("MailingAddress_Name").HasMaxLength(200);
                    owned.Property(a => a.Street).HasColumnName("MailingAddress_Street").HasMaxLength(200);
                    owned.Property(a => a.UnitNumber).HasColumnName("MailingAddress_UnitNumber").HasMaxLength(50);
                    owned.Property(a => a.ProvinceOrTerritory).HasColumnName("MailingAddress_Province").HasMaxLength(50);
                    owned.Property(a => a.PostalCode).HasColumnName("MailingAddress_PostalCode").HasMaxLength(20);

                    // Configure City as owned entity (following EnvelopeService pattern)
                    owned.OwnsOne(a => a.City, city =>
                    {
                        city.Property(c => c.Code).HasColumnName("MailingAddress_CityCode").HasMaxLength(50);
                        city.Property(c => c.EnglishText).HasColumnName("MailingAddress_CityEnglishText").HasMaxLength(200);
                        city.Property(c => c.FrenchText).HasColumnName("MailingAddress_CityFrenchText").HasMaxLength(200);
                        city.Property(c => c.Text).HasColumnName("MailingAddress_CityText").HasMaxLength(200);
                        city.Property(c => c.IsUserSpecified).HasColumnName("MailingAddress_CityIsUserSpecified");
                    });
                });
            });

            // Configure Justifications entity
            modelBuilder.Entity<Justifications>(entity =>
            {
                entity.ToTable("Justifications", "housefile");
                entity.HasKey(e => e.Id);
                entity.Property(e => e.Id).ValueGeneratedOnAdd();

                // Configure simple owned entities (value objects)
                entity.OwnsOne(e => e.PossessionDate, owned =>
                {
                    owned.Property(p => p.Selected).HasColumnName("PossessionDate_Selected");
                    owned.Property(p => p.Date).HasColumnName("PossessionDate_Date").HasMaxLength(50);
                });

                entity.OwnsOne(e => e.HeatingVolumeDecrease, owned =>
                {
                    owned.Property(h => h.Selected).HasColumnName("HeatingVolumeDecrease_Selected");
                    owned.Property(h => h.Text).HasColumnName("HeatingVolumeDecrease_Text").HasMaxLength(500);
                });

                entity.OwnsOne(e => e.Other, owned =>
                {
                    owned.Property(o => o.Selected).HasColumnName("Other_Selected");
                    owned.Property(o => o.Text).HasColumnName("Other_Text").HasMaxLength(500);
                });

                entity.OwnsOne(e => e.CorrectedInsulation, owned =>
                {
                    owned.OwnsOne(ci => ci.Ceilings, ceilings =>
                    {
                        ceilings.Property(c => c.Selected).HasColumnName("CorrectedInsulation_Ceilings_Selected");
                        ceilings.Property(c => c.Text).HasColumnName("CorrectedInsulation_Ceilings_Text").HasMaxLength(500);
                    });

                    owned.OwnsOne(ci => ci.Walls, walls =>
                    {
                        walls.Property(w => w.Selected).HasColumnName("CorrectedInsulation_Walls_Selected");
                        walls.Property(w => w.Text).HasColumnName("CorrectedInsulation_Walls_Text").HasMaxLength(500);
                    });

                    owned.OwnsOne(ci => ci.Basement, basement =>
                    {
                        basement.Property(b => b.Selected).HasColumnName("CorrectedInsulation_Basement_Selected");
                        basement.Property(b => b.Text).HasColumnName("CorrectedInsulation_Basement_Text").HasMaxLength(500);
                    });
                });

                // Configure EnergyStar as owned entity (following EnvelopeService pattern)
                entity.OwnsOne(e => e.EnergyStar, energyStar =>
                {
                    energyStar.Property(es => es.Code).HasColumnName("EnergyStarCode").HasMaxLength(50);
                    energyStar.Property(es => es.EnglishText).HasColumnName("EnergyStarEnglishText").HasMaxLength(200);
                    energyStar.Property(es => es.FrenchText).HasColumnName("EnergyStarFrenchText").HasMaxLength(200);
                    energyStar.Property(es => es.Text).HasColumnName("EnergyStarText").HasMaxLength(200);
                    energyStar.Property(es => es.IsUserSpecified).HasColumnName("EnergyStarIsUserSpecified");
                });
            });

            // Hot2K Resource entities are static classes, not database entities
            // We use CodeAndText and CodeTextAndValue for database storage

            // Configure Specification entity
            modelBuilder.Entity<Specification>(entity =>
            {
                entity.ToTable("Specifications", "housefile");
                entity.HasKey(e => e.Id);
                entity.Property(e => e.Id).ValueGeneratedOnAdd();
                entity.Property(e => e.HouseId).IsRequired();
                entity.Property(e => e.EffectiveMassFraction).HasPrecision(18, 4);
                entity.Property(e => e.DefaultRoofCavity).HasDefaultValue(false);
                entity.Property(e => e.EligibleForNBC).HasDefaultValue(false);
                entity.Property(e => e.BuildingType).HasDefaultValue(BuildingTypes.HOUSE);

                // Add index on HouseId for better performance
                entity.HasIndex(e => e.HouseId).HasDatabaseName("IX_Specifications_HouseId");

                // Hot2K resource classes stored as owned entities with explicit column mapping
                entity.OwnsOne(e => e.HouseType, owned =>
                {
                    owned.Property(o => o.Code).HasColumnName("HouseTypeCode").HasMaxLength(50).HasDefaultValue("");
                    owned.Property(o => o.English).HasColumnName("HouseTypeEnglish").HasMaxLength(200).HasDefaultValue("");
                    owned.Property(o => o.French).HasColumnName("HouseTypeFrench").HasMaxLength(200).HasDefaultValue("");
                    owned.Property(o => o.IsUserSpecified).HasColumnName("HouseTypeIsUserSpecified").HasDefaultValue(false);
                });

                entity.OwnsOne(e => e.PlanShape, owned =>
                {
                    owned.Property(o => o.Code).HasColumnName("PlanShapeCode").HasMaxLength(50).HasDefaultValue("");
                    owned.Property(o => o.English).HasColumnName("PlanShapeEnglish").HasMaxLength(200).HasDefaultValue("");
                    owned.Property(o => o.French).HasColumnName("PlanShapeFrench").HasMaxLength(200).HasDefaultValue("");
                    owned.Property(o => o.IsUserSpecified).HasColumnName("PlanShapeIsUserSpecified").HasDefaultValue(false);
                });

                entity.OwnsOne(e => e.Storeys, owned =>
                {
                    owned.Property(o => o.Code).HasColumnName("StoreysCode").HasMaxLength(50).HasDefaultValue("");
                    owned.Property(o => o.English).HasColumnName("StoreysEnglish").HasMaxLength(200).HasDefaultValue("");
                    owned.Property(o => o.French).HasColumnName("StoreysFrench").HasMaxLength(200).HasDefaultValue("");
                    owned.Property(o => o.IsUserSpecified).HasColumnName("StoreysIsUserSpecified").HasDefaultValue(false);
                });

                entity.OwnsOne(e => e.FacingDirection, owned =>
                {
                    owned.Property(o => o.Code).HasColumnName("FacingDirectionCode").HasMaxLength(50).HasDefaultValue("");
                    owned.Property(o => o.English).HasColumnName("FacingDirectionEnglish").HasMaxLength(200).HasDefaultValue("");
                    owned.Property(o => o.French).HasColumnName("FacingDirectionFrench").HasMaxLength(200).HasDefaultValue("");
                    owned.Property(o => o.IsUserSpecified).HasColumnName("FacingDirectionIsUserSpecified").HasDefaultValue(false);
                });

                entity.OwnsOne(e => e.ThermalMass, owned =>
                {
                    owned.Property(o => o.Code).HasColumnName("ThermalMassCode").HasMaxLength(50).HasDefaultValue("");
                    owned.Property(o => o.English).HasColumnName("ThermalMassEnglish").HasMaxLength(200).HasDefaultValue("");
                    owned.Property(o => o.French).HasColumnName("ThermalMassFrench").HasMaxLength(200).HasDefaultValue("");
                    owned.Property(o => o.IsUserSpecified).HasColumnName("ThermalMassIsUserSpecified").HasDefaultValue(false);
                });

                entity.OwnsOne(e => e.YearBuilt, owned =>
                {
                    owned.Property(o => o.Code).HasColumnName("YearBuiltCode").HasMaxLength(50).HasDefaultValue("");
                    owned.Property(o => o.English).HasColumnName("YearBuiltEnglish").HasMaxLength(200).HasDefaultValue("");
                    owned.Property(o => o.French).HasColumnName("YearBuiltFrench").HasMaxLength(200).HasDefaultValue("");
                    owned.Property(o => o.Value).HasColumnName("YearBuiltValue").HasPrecision(18, 4).HasDefaultValue(0.0m);
                    owned.Property(o => o.IsUserSpecified).HasColumnName("YearBuiltIsUserSpecified").HasDefaultValue(false);
                });

                entity.OwnsOne(e => e.WallColour, owned =>
                {
                    owned.Property(o => o.Code).HasColumnName("WallColourCode").HasMaxLength(50).HasDefaultValue("");
                    owned.Property(o => o.English).HasColumnName("WallColourEnglish").HasMaxLength(200).HasDefaultValue("");
                    owned.Property(o => o.French).HasColumnName("WallColourFrench").HasMaxLength(200).HasDefaultValue("");
                    owned.Property(o => o.Value).HasColumnName("WallColourValue").HasPrecision(18, 4).HasDefaultValue(0.0m);
                    owned.Property(o => o.IsUserSpecified).HasColumnName("WallColourIsUserSpecified").HasDefaultValue(false);
                });

                entity.OwnsOne(e => e.SoilCondition, owned =>
                {
                    owned.Property(o => o.Code).HasColumnName("SoilConditionCode").HasMaxLength(50).HasDefaultValue("");
                    owned.Property(o => o.English).HasColumnName("SoilConditionEnglish").HasMaxLength(200).HasDefaultValue("");
                    owned.Property(o => o.French).HasColumnName("SoilConditionFrench").HasMaxLength(200).HasDefaultValue("");
                    owned.Property(o => o.IsUserSpecified).HasColumnName("SoilConditionIsUserSpecified").HasDefaultValue(false);
                });

                entity.OwnsOne(e => e.RoofColour, owned =>
                {
                    owned.Property(o => o.Code).HasColumnName("RoofColourCode").HasMaxLength(50).HasDefaultValue("");
                    owned.Property(o => o.English).HasColumnName("RoofColourEnglish").HasMaxLength(200).HasDefaultValue("");
                    owned.Property(o => o.French).HasColumnName("RoofColourFrench").HasMaxLength(200).HasDefaultValue("");
                    owned.Property(o => o.Value).HasColumnName("RoofColourValue").HasPrecision(18, 4).HasDefaultValue(0.0m);
                    owned.Property(o => o.IsUserSpecified).HasColumnName("RoofColourIsUserSpecified").HasDefaultValue(false);
                });

                entity.OwnsOne(e => e.WaterLevel, owned =>
                {
                    owned.Property(o => o.Code).HasColumnName("WaterLevelCode").HasMaxLength(50).HasDefaultValue("");
                    owned.Property(o => o.English).HasColumnName("WaterLevelEnglish").HasMaxLength(200).HasDefaultValue("");
                    owned.Property(o => o.French).HasColumnName("WaterLevelFrench").HasMaxLength(200).HasDefaultValue("");
                    owned.Property(o => o.IsUserSpecified).HasColumnName("WaterLevelIsUserSpecified").HasDefaultValue(false);
                });

                // Configure relationships with complex entities
                entity.HasOne(e => e.RoofCavity).WithMany().OnDelete(DeleteBehavior.Cascade).IsRequired(false);
                entity.HasOne(e => e.NumberOf).WithMany().OnDelete(DeleteBehavior.Cascade).IsRequired(false);
                entity.HasOne(e => e.HeatedFloorArea).WithMany().OnDelete(DeleteBehavior.Cascade).IsRequired(false);
            });

            // Configure RoofCavity entity
            modelBuilder.Entity<RoofCavity>(entity =>
            {
                entity.ToTable("RoofCavities", "housefile");
                entity.HasKey(e => e.Id);
                entity.Property(e => e.Id).ValueGeneratedOnAdd();
                entity.Property(e => e.Volume).HasPrecision(18, 4);
                entity.Property(e => e.VentilationRate).HasPrecision(18, 4);

                entity.HasOne(e => e.GableEnds).WithMany().OnDelete(DeleteBehavior.Cascade).IsRequired(false);
                entity.HasOne(e => e.SlopedRoof).WithMany().OnDelete(DeleteBehavior.Cascade).IsRequired(false);
            });

            // Configure GableEnds entity
            modelBuilder.Entity<GableEnds>(entity =>
            {
                entity.ToTable("GableEnds", "housefile");
                entity.HasKey(e => e.Id);
                entity.Property(e => e.Id).ValueGeneratedOnAdd();
                entity.Property(e => e.Area).HasPrecision(18, 4);

                // Configure materials as owned entities with explicit column mapping
                entity.OwnsOne(e => e.SheatingMaterial, owned =>
                {
                    owned.Property(o => o.Code).HasColumnName("SheatingMaterialCode").HasMaxLength(50).HasDefaultValue("");
                    owned.Property(o => o.English).HasColumnName("SheatingMaterialEnglish").HasMaxLength(200).HasDefaultValue("");
                    owned.Property(o => o.French).HasColumnName("SheatingMaterialFrench").HasMaxLength(200).HasDefaultValue("");
                    owned.Property(o => o.Value).HasColumnName("SheatingMaterialValue").HasPrecision(18, 4).HasDefaultValue(0.0m);
                    owned.Property(o => o.IsUserSpecified).HasColumnName("SheatingMaterialIsUserSpecified").HasDefaultValue(false);
                });

                entity.OwnsOne(e => e.ExteriorMaterial, owned =>
                {
                    owned.Property(o => o.Code).HasColumnName("ExteriorMaterialCode").HasMaxLength(50).HasDefaultValue("");
                    owned.Property(o => o.English).HasColumnName("ExteriorMaterialEnglish").HasMaxLength(200).HasDefaultValue("");
                    owned.Property(o => o.French).HasColumnName("ExteriorMaterialFrench").HasMaxLength(200).HasDefaultValue("");
                    owned.Property(o => o.Value).HasColumnName("ExteriorMaterialValue").HasPrecision(18, 4).HasDefaultValue(0.0m);
                    owned.Property(o => o.IsUserSpecified).HasColumnName("ExteriorMaterialIsUserSpecified").HasDefaultValue(false);
                });
            });

            // Configure SlopedRoof entity
            modelBuilder.Entity<SlopedRoof>(entity =>
            {
                entity.ToTable("SlopedRoofs", "housefile");
                entity.HasKey(e => e.Id);
                entity.Property(e => e.Id).ValueGeneratedOnAdd();
                entity.Property(e => e.Area).HasPrecision(18, 4);

                // Configure materials as owned entities with explicit column mapping
                entity.OwnsOne(e => e.SheatingMaterial, owned =>
                {
                    owned.Property(o => o.Code).HasColumnName("SheatingMaterialCode").HasMaxLength(50).HasDefaultValue("");
                    owned.Property(o => o.English).HasColumnName("SheatingMaterialEnglish").HasMaxLength(200).HasDefaultValue("");
                    owned.Property(o => o.French).HasColumnName("SheatingMaterialFrench").HasMaxLength(200).HasDefaultValue("");
                    owned.Property(o => o.Value).HasColumnName("SheatingMaterialValue").HasPrecision(18, 4).HasDefaultValue(0.0m);
                    owned.Property(o => o.IsUserSpecified).HasColumnName("SheatingMaterialIsUserSpecified").HasDefaultValue(false);
                });

                entity.OwnsOne(e => e.RoofingMaterial, owned =>
                {
                    owned.Property(o => o.Code).HasColumnName("RoofingMaterialCode").HasMaxLength(50).HasDefaultValue("");
                    owned.Property(o => o.English).HasColumnName("RoofingMaterialEnglish").HasMaxLength(200).HasDefaultValue("");
                    owned.Property(o => o.French).HasColumnName("RoofingMaterialFrench").HasMaxLength(200).HasDefaultValue("");
                    owned.Property(o => o.Value).HasColumnName("RoofingMaterialValue").HasPrecision(18, 4).HasDefaultValue(0.0m);
                    owned.Property(o => o.IsUserSpecified).HasColumnName("RoofingMaterialIsUserSpecified").HasDefaultValue(false);
                });
            });

            // Configure NumberOf entity
            modelBuilder.Entity<NumberOf>(entity =>
            {
                entity.ToTable("NumberOfs", "housefile");
                entity.HasKey(e => e.Id);
                entity.Property(e => e.Id).ValueGeneratedOnAdd();
                entity.Property(e => e.StoreysInBuilding).HasDefaultValue(0u);
                entity.Property(e => e.DwellingUnits).HasDefaultValue(1u);
                entity.Property(e => e.NonResUnits).HasDefaultValue(0u);
                entity.Property(e => e.UnitsVisited).HasDefaultValue(0u);
            });

            // Configure HeatedFloorArea entity
            modelBuilder.Entity<HeatedFloorArea>(entity =>
            {
                entity.ToTable("HeatedFloorAreas", "housefile");
                entity.HasKey(e => e.Id);
                entity.Property(e => e.Id).ValueGeneratedOnAdd();
                entity.Property(e => e.AboveGrade).HasPrecision(18, 4).HasDefaultValue(0.1m);
                entity.Property(e => e.BelowGrade).HasPrecision(18, 4).HasDefaultValue(0.1m);
                entity.Property(e => e.NonResUnits).HasPrecision(18, 4).HasDefaultValue(0m);
                entity.Property(e => e.CommonSpace).HasPrecision(18, 4).HasDefaultValue(0m);
            });

        }
    }
}