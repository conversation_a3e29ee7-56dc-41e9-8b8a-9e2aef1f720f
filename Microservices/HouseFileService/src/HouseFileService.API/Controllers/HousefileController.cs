// UPDATED HousefileController.cs with UserId and HouseName
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using AutoMapper;
using HouseFileService.API.Models;
using HouseFileService.Core.Interfaces;
using HouseFileService.Core.Models;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;

namespace HouseFileService.API.Controllers
{
    [ApiController]
    [Route("api/houses/housefiles")]
    public class HousefileController : ControllerBase
    {
        private readonly IHouseFileService _houseFileService;
        private readonly IMapper _mapper;
        private readonly ILogger<HousefileController> _logger;
        
        public HousefileController(
            IHouseFileService houseFileService,
            IMapper mapper,
            ILogger<HousefileController> logger)
        {
            _houseFileService = houseFileService;
            _mapper = mapper;
            _logger = logger;
        }
        
        // GET: api/houses/housefiles
        [HttpGet]
        [ProducesResponseType(StatusCodes.Status200OK)]
        public async Task<ActionResult<IEnumerable<HousefileDto>>> GetHousefiles()
        {
            var housefiles = await _houseFileService.GetAllHousefilesAsync();
            var housefileDtos = _mapper.Map<IEnumerable<HousefileDto>>(housefiles);
            
            return Ok(housefileDtos);
        }
        
        // GET: api/houses/housefiles/{id}
        [HttpGet("{id}")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<ActionResult<HousefileDto>> GetHousefile(Guid id)
        {
            var housefile = await _houseFileService.GetHousefileByIdAsync(id);
            
            if (housefile == null)
                return NotFound(new { Error = "Housefile not found", Id = id });
                
            var housefileDto = _mapper.Map<HousefileDto>(housefile);
            return Ok(housefileDto);
        }

        // NEW: GET: api/houses/housefiles/user/{userId}
        [HttpGet("user/{userId:guid}")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        public async Task<ActionResult<IEnumerable<HousefileDto>>> GetHousefilesByUserId(Guid userId)
        {
            var housefiles = await _houseFileService.GetHousefilesByUserIdAsync(userId);
            var housefileDtos = _mapper.Map<IEnumerable<HousefileDto>>(housefiles);
            
            return Ok(new
            {
                UserId = userId,
                Housefiles = housefileDtos,
                Count = housefileDtos.Count()
            });
        }

        // NEW: GET: api/houses/housefiles/house-name/{houseName}
        [HttpGet("house-name/{houseName}")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<ActionResult<HousefileDto>> GetHousefileByHouseName(string houseName)
        {
            var housefile = await _houseFileService.GetHousefileByHouseNameAsync(houseName);
            
            if (housefile == null)
                return NotFound(new { Error = "Housefile not found", HouseName = houseName });
                
            var housefileDto = _mapper.Map<HousefileDto>(housefile);
            return Ok(housefileDto);
        }

        // NEW: GET: api/houses/housefiles/check-house-name
        [HttpGet("check-house-name")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        public async Task<IActionResult> CheckHouseNameAvailability([FromQuery] Guid userId, [FromQuery] string houseName)
        {
            try
            {
                var exists = await _houseFileService.HouseNameExistsForUserAsync(userId, houseName);
                return Ok(new
                {
                    UserId = userId,
                    HouseName = houseName,
                    Available = exists
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error checking house name availability for User {UserId} and House {HouseName}", userId, houseName);
                return StatusCode(500, new { Error = "Internal server error" });
            }
        }
        
        // POST: api/houses/housefiles
        [HttpPost]
        [ProducesResponseType(StatusCodes.Status201Created)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        public async Task<ActionResult<HousefileDto>> CreateHousefile([FromBody] CreateHousefileRequest request)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(request.HouseName))
                {
                    return BadRequest(new { Error = "House name is required" });
                }

                // Check if house name already exists for this user
                if (await _houseFileService.HouseNameExistsForUserAsync(request.UserId, request.HouseName))
                {
                    return Conflict(new { Error = $"House name '{request.HouseName}' already exists for this user" });
                }

                var housefile = _mapper.Map<Housefile>(request);
                var createdHousefile = await _houseFileService.CreateHousefileAsync(housefile);
                var housefileDto = _mapper.Map<HousefileDto>(createdHousefile);
                
                return CreatedAtAction(nameof(GetHousefile), new { id = housefileDto.Id }, housefileDto);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating housefile");
                return StatusCode(500, new { Error = "Internal server error" });
            }
        }
        
        // PUT: api/houses/housefiles/{id}
        [HttpPut("{id}")]
        [ProducesResponseType(StatusCodes.Status204NoContent)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<IActionResult> UpdateHousefile(Guid id, [FromBody] UpdateHousefileRequest request)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(request.HouseName))
                {
                    return BadRequest(new { Error = "House name is required" });
                }

                var existingHousefile = await _houseFileService.GetHousefileByIdAsync(id);
                
                if (existingHousefile == null)
                    return NotFound(new { Error = "Housefile not found", Id = id });

                // Check if house name is being changed and if new name already exists for this user
                if (!string.Equals(existingHousefile.HouseName, request.HouseName, StringComparison.OrdinalIgnoreCase))
                {
                    if (await _houseFileService.HouseNameExistsForUserAsync(request.UserId, request.HouseName))
                    {
                        return Conflict(new { Error = $"House name '{request.HouseName}' already exists for this user" });
                    }
                }

                // Update fields
                existingHousefile.UserId = request.UserId;
                existingHousefile.HouseName = request.HouseName;
                
                var result = await _houseFileService.UpdateHousefileAsync(existingHousefile);
                
                if (!result)
                    return BadRequest(new { Error = "Failed to update housefile" });
                    
                return NoContent();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating housefile {Id}", id);
                return StatusCode(500, new { Error = "Internal server error" });
            }
        }
        
        // DELETE: api/houses/housefiles/{id}
        [HttpDelete("{id}")]
        [ProducesResponseType(StatusCodes.Status204NoContent)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<IActionResult> DeleteHousefile(Guid id)
        {
            try
            {
                var result = await _houseFileService.DeleteHousefileAsync(id);
                
                if (!result)
                    return NotFound(new { Error = "Housefile not found", Id = id });
                    
                return NoContent();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting housefile {Id}", id);
                return StatusCode(500, new { Error = "Internal server error" });
            }
        }
    }
}
