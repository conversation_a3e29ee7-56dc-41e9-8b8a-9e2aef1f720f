// 1. UPDATED HouseFileService.Core.Models.Housefile
using System;
using System.Collections.Generic;

namespace HouseFileService.Core.Models
{
    public class Housefile
    {
        public Guid Id { get; set; }
        public Guid UserId { get; set; }  // CHANGED: from Username to UserId
        public string HouseName { get; set; } = string.Empty;  // CHANGED: from Password to HouseName

        public Guid? EnergyUpgradeId { get; set; }

        // Timestamps only for Housefile
        public DateTime CreatedDate { get; set; }
        public DateTime ModifiedDate { get; set; }
    }
}