using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using HouseFileService.Core.Models;

namespace HouseFileService.Core.Interfaces
{
    public interface IHouseFileService
    {
        // Housefile operations
        Task<IEnumerable<Housefile>> GetAllHousefilesAsync();
        Task<Housefile?> GetHousefileByIdAsync(Guid id);
        Task<IEnumerable<Housefile>> GetHousefilesByUserIdAsync(Guid userId);  // NEW
        Task<Housefile?> GetHousefileByHouseNameAsync(string houseName);  // NEW
        Task<bool> HouseNameExistsForUserAsync(Guid userId, string houseName);  // NEW
        Task<Housefile> CreateHousefileAsync(Housefile housefile);
        Task<bool> UpdateHousefileAsync(Housefile housefile);
        Task<bool> DeleteHousefileAsync(Guid id);
        
        // ProgramInformation operations
        Task<ProgramInformation?> GetProgramInformationByIdAsync(Guid id);
        Task<ProgramInformation?> GetProgramInformationByHouseIdAsync(Guid houseId);
        Task<ProgramInformation> CreateProgramInformationAsync(ProgramInformation programInformation);
        Task<ProgramInformation> UpdateProgramInformationAsync(ProgramInformation programInformation);
        Task<bool> DeleteProgramInformationAsync(Guid id);

        // File operations
        Task<Files?> GetFileByIdAsync(Guid id);
        Task<Files?> GetFileByProgramInformationIdAsync(Guid programInformationId);
        Task<Files> UpdateFileAsync(Files files);

        // Client operations
        Task<Client?> GetClientByIdAsync(Guid id);
        Task<Client?> GetClientByProgramInformationIdAsync(Guid programInformationId);
        Task<Client> UpdateClientAsync(Client client);

        // Justifications operations
        Task<Justifications?> GetJustificationsByIdAsync(Guid id);
        Task<Justifications?> GetJustificationsByProgramInformationIdAsync(Guid programInformationId);
        Task<Justifications> UpdateJustificationsAsync(Justifications justifications);

        // InfoCodeAndText operations (for ProgramInformation.Information)
        Task<InfoCodeAndText?> GetInfoCodeAndTextByIdAsync(Guid id);
        Task<IEnumerable<InfoCodeAndText>> GetInfoCodeAndTextByProgramInformationIdAsync(Guid programInformationId);
        Task<InfoCodeAndText> CreateInfoCodeAndTextAsync(InfoCodeAndText infoCodeAndText);
        Task<InfoCodeAndText> UpdateInfoCodeAndTextAsync(InfoCodeAndText infoCodeAndText);
        Task<bool> DeleteInfoCodeAndTextAsync(Guid id);

        // Specification operations
        Task<Specification?> GetSpecificationByIdAsync(Guid id);
        Task<Specification?> GetSpecificationByHouseIdAsync(Guid houseId);
        Task<Specification> CreateSpecificationAsync(Specification specification);
        Task<Specification> UpdateSpecificationAsync(Specification specification);
        Task<bool> DeleteSpecificationAsync(Guid id);
    }
}
