using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using EnvelopeService.Core.Interfaces;
using EnvelopeService.Core.Models;
using EnvelopeService.Infrastructure.Data;
using Microsoft.EntityFrameworkCore;

namespace EnvelopeService.Infrastructure.Repositories
{
    public class FloorHeaderRepository : IFloorHeaderRepository
    {
        private readonly EnvelopDbContext _context;

        public FloorHeaderRepository(EnvelopDbContext context)
        {
            _context = context;
        }

        public async Task<List<FloorHeader>> GetAllByHouseIdAsync(Guid houseId)
        {
            return await _context.FloorHeaders
                .Include(fh => fh.Construction)
                .Include(fh => fh.Measurements)
                .Where(fh => fh.HouseId == houseId)
                .ToListAsync();
        }

        public async Task<List<FloorHeader>> GetAllByEnergyUpgradeIdAsync(Guid energyUpgradeId)
        {
            return await _context.FloorHeaders
                .Include(fh => fh.Construction)
                .Include(fh => fh.Measurements)
                .Where(fh => fh.EnergyUpgradeId == energyUpgradeId)
                .ToListAsync();
        }

        public async Task<FloorHeader> GetByIdAsync(Guid id)
        {
            return await _context.FloorHeaders
                .AsNoTracking()
                .Include(fh => fh.Construction)
                .Include(fh => fh.Measurements)
                .FirstOrDefaultAsync(fh => fh.Id == id);
        }

        public async Task<FloorHeader> AddAsync(FloorHeader floorHeader)
        {
            // Set IDs for the floor header and related entities
            if (floorHeader.Id == Guid.Empty)
            {
                floorHeader.Id = Guid.NewGuid();
            }
            
            if (floorHeader.Construction != null)
            {
                if (floorHeader.Construction.Id == Guid.Empty)
                {
                    floorHeader.Construction.Id = Guid.NewGuid();
                }
                floorHeader.Construction.FloorHeaderId = floorHeader.Id;

                // If Type is a new entity, set its ID
                if (floorHeader.Construction.Type != null && floorHeader.Construction.Type.Id == Guid.Empty)
                {
                    floorHeader.Construction.Type.Id = Guid.NewGuid();
                }

                // Set TypeReferenceId to match the Type.Id
                if (floorHeader.Construction.Type != null)
                {
                    floorHeader.Construction.TypeReferenceId = floorHeader.Construction.Type.Id;
                }
            }
            
            if (floorHeader.Measurements != null)
            {
                if (floorHeader.Measurements.Id == Guid.Empty)
                {
                    floorHeader.Measurements.Id = Guid.NewGuid();
                }
                floorHeader.Measurements.FloorHeaderId = floorHeader.Id;
            }

            _context.FloorHeaders.Add(floorHeader);
            await _context.SaveChangesAsync();
            return floorHeader;
        }

        public async Task<FloorHeader> UpdateAsync(FloorHeader floorHeader)
        {
            var existingFloorHeader = await GetByIdAsync(floorHeader.Id);
            if (existingFloorHeader == null)
                return null;

            // Update floor header properties manually (avoid SetValues to prevent ID modification)
            existingFloorHeader.HouseId = floorHeader.HouseId;
            existingFloorHeader.Label = floorHeader.Label;
            existingFloorHeader.AdjacentEnclosedSpace = floorHeader.AdjacentEnclosedSpace;

            // Handle Construction updates
            if (floorHeader.Construction != null)
            {
                if (existingFloorHeader.Construction != null && floorHeader.Construction.Type != null)
                {
                    // Create a new CodeReference
                    var newCodeRef = new CodeReference
                    {
                        Id = Guid.NewGuid(),
                        Code = floorHeader.Construction.Type.Code,
                        Text = floorHeader.Construction.Type.Text,
                        RValue = floorHeader.Construction.Type.RValue
                    };

                    // Add the new CodeReference to context
                    _context.CodeReferences.Add(newCodeRef);

                    // Update construction properties manually (avoid SetValues to prevent ID modification)
                    existingFloorHeader.Construction.FloorHeaderId = floorHeader.Construction.FloorHeaderId;
                    existingFloorHeader.Construction.Type = newCodeRef;
                    existingFloorHeader.Construction.TypeReferenceId = newCodeRef.Id;
                }
                else if (existingFloorHeader.Construction == null)
                {
                    // Create new construction
                    floorHeader.Construction.Id = Guid.NewGuid();
                    floorHeader.Construction.FloorHeaderId = floorHeader.Id;
                    if (floorHeader.Construction.Type != null && floorHeader.Construction.Type.Id == Guid.Empty)
                    {
                        floorHeader.Construction.Type.Id = Guid.NewGuid();
                    }
                    existingFloorHeader.Construction = floorHeader.Construction;
                }
            }

            // Handle Measurements updates
            if (floorHeader.Measurements != null)
            {
                if (existingFloorHeader.Measurements != null)
                {
                    // Update measurements properties manually (avoid SetValues to prevent ID modification)
                    existingFloorHeader.Measurements.Height = floorHeader.Measurements.Height;
                    existingFloorHeader.Measurements.Perimeter = floorHeader.Measurements.Perimeter;
                    existingFloorHeader.Measurements.FloorHeaderId = floorHeader.Measurements.FloorHeaderId;
                }
                else
                {
                    // Create new measurements
                    floorHeader.Measurements.Id = Guid.NewGuid();
                    floorHeader.Measurements.FloorHeaderId = floorHeader.Id;
                    existingFloorHeader.Measurements = floorHeader.Measurements;
                }
            }

            // Handle FacingDirection (owned entity)
            if (floorHeader.FacingDirection != null)
            {
                existingFloorHeader.FacingDirection = floorHeader.FacingDirection;
            }

            await _context.SaveChangesAsync();
            return existingFloorHeader;
        }

        public async Task<bool> DeleteAsync(Guid id)
        {
            var floorHeader = await GetByIdAsync(id);
            if (floorHeader == null)
                return false;

            _context.FloorHeaders.Remove(floorHeader);
            await _context.SaveChangesAsync();
            return true;
        }

        public async Task<bool> ExistsAsync(Guid id)
        {
            return await _context.FloorHeaders.AnyAsync(fh => fh.Id == id);
        }
    }
}
