
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using EnvelopeService.Core.Interfaces;
using EnvelopeService.Core.Models;
using EnvelopeService.Infrastructure.Data;
using Microsoft.EntityFrameworkCore;

namespace EnvelopeService.Infrastructure.Repositories
{
    public class WallRepository : IWallRepository
    {
        private readonly EnvelopDbContext _context;

        public WallRepository(EnvelopDbContext context)
        {
            _context = context ?? throw new ArgumentNullException(nameof(context));
        }

        public async Task<List<Wall>> GetWallsByHouseIdAsync(Guid houseId)
        {
            var walls = await _context.Walls
                .Include(w => w.Construction)
                    .ThenInclude(c => c.Type)
                        .ThenInclude(t => t.UserDefinedCodeLayers)
                .Include(w => w.Construction)
                    .ThenInclude(c => c.LintelType)
                        .ThenInclude(l => l.UserDefinedCodeLayers)
                .Include(w => w.Measurements)
                .Where(w => w.HouseId == houseId)
                .ToListAsync();

            // Populate FacingDirection text values based on codes
            foreach (var wall in walls)
            {
                if (wall.FacingDirection != null && !string.IsNullOrEmpty(wall.FacingDirection.Code))
                {
                    var wallDirection = WallDirections.FromCode(wall.FacingDirection.Code, wall.FacingDirection.IsUserSpecified);
                    wall.FacingDirection = new WallDirections
                    {
                        Code = wall.FacingDirection.Code,
                        EnglishText = wallDirection.EnglishText,
                        FrenchText = wallDirection.FrenchText,
                        IsUserSpecified = wall.FacingDirection.IsUserSpecified
                    };
                }
            }

            return walls;
        }

        public async Task<List<Wall>> GetWallsByEnergyUpgradeIdAsync(Guid energyUpgradeId)
        {
            var walls = await _context.Walls
                .Include(w => w.Construction)
                    .ThenInclude(c => c.Type)
                        .ThenInclude(t => t.UserDefinedCodeLayers)
                .Include(w => w.Construction)
                    .ThenInclude(c => c.LintelType)
                        .ThenInclude(l => l.UserDefinedCodeLayers)
                .Include(w => w.Measurements)
                .Where(w => w.EnergyUpgradeId == energyUpgradeId)
                .ToListAsync();

            // Populate FacingDirection text values based on codes
            foreach (var wall in walls)
            {
                if (wall.FacingDirection != null && !string.IsNullOrEmpty(wall.FacingDirection.Code))
                {
                    var wallDirection = WallDirections.FromCode(wall.FacingDirection.Code, wall.FacingDirection.IsUserSpecified);
                    wall.FacingDirection = new WallDirections
                    {
                        Code = wall.FacingDirection.Code,
                        EnglishText = wallDirection.EnglishText,
                        FrenchText = wallDirection.FrenchText,
                        IsUserSpecified = wall.FacingDirection.IsUserSpecified
                    };
                }
            }

            return walls;
        }

        public async Task<Wall> GetWallByIdAsync(Guid id)
        {
            var wall = await _context.Walls
                .AsNoTracking()
                .Include(w => w.Construction)
                    .ThenInclude(c => c.Type)
                        .ThenInclude(t => t.UserDefinedCodeLayers)
                .Include(w => w.Construction)
                    .ThenInclude(c => c.LintelType)
                        .ThenInclude(l => l.UserDefinedCodeLayers)
                .Include(w => w.Measurements)
                .FirstOrDefaultAsync(w => w.Id == id);

            // Populate FacingDirection text values based on code
            if (wall?.FacingDirection != null && !string.IsNullOrEmpty(wall.FacingDirection.Code))
            {
                var wallDirection = WallDirections.FromCode(wall.FacingDirection.Code, wall.FacingDirection.IsUserSpecified);
                wall.FacingDirection = new WallDirections
                {
                    Code = wall.FacingDirection.Code,
                    EnglishText = wallDirection.EnglishText,
                    FrenchText = wallDirection.FrenchText,
                    IsUserSpecified = wall.FacingDirection.IsUserSpecified
                };
            }

            return wall;
        }

        public async Task<Wall> AddWallAsync(Wall wall)
        {
            // Set IDs for the wall and related entities
            if (wall.Id == Guid.Empty)
            {
                wall.Id = Guid.NewGuid();
            }

            // UPDATED: Handle standalone WallConstruction
            if (wall.Construction != null)
            {
                if (wall.Construction.Id == Guid.Empty)
                {
                    wall.Construction.Id = Guid.NewGuid();
                }
                wall.Construction.WallId = wall.Id;

                // Handle Type CodeReference (standalone property)
                if (wall.Construction.Type != null && wall.Construction.Type.Id == Guid.Empty)
                {
                    wall.Construction.Type.Id = Guid.NewGuid();
                }

                // Handle LintelType CodeReference (standalone property)  
                if (wall.Construction.LintelType != null && wall.Construction.LintelType.Id == Guid.Empty)
                {
                    wall.Construction.LintelType.Id = Guid.NewGuid();
                }
            }

            // Set Measurements ID
            if (wall.Measurements != null)
            {
                if (wall.Measurements.Id == Guid.Empty)
                {
                    wall.Measurements.Id = Guid.NewGuid();
                }
                wall.Measurements.WallId = wall.Id;
            }

            _context.Walls.Add(wall);
            await _context.SaveChangesAsync();
            return wall;
        }

        public async Task<Wall> UpdateWallAsync(Wall wall)
        {
            var existingWall = await GetWallByIdAsync(wall.Id);
            if (existingWall == null)
                return null;

            // UPDATED: Update standalone Wall properties 
            existingWall.Label = wall.Label;
            existingWall.AdjacentEnclosedSpace = wall.AdjacentEnclosedSpace;

            // Update standalone FacingDirection properties
            if (wall.FacingDirection != null)
            {
                existingWall.FacingDirection.Code = wall.FacingDirection.Code;
                existingWall.FacingDirection.EnglishText = wall.FacingDirection.EnglishText;
                existingWall.FacingDirection.FrenchText = wall.FacingDirection.FrenchText;
                existingWall.FacingDirection.IsUserSpecified = wall.FacingDirection.IsUserSpecified;
            }

            // UPDATED: Update standalone WallConstruction
            if (wall.Construction != null && existingWall.Construction != null)
            {
                existingWall.Construction.Corners = wall.Construction.Corners;
                existingWall.Construction.Intersections = wall.Construction.Intersections;

                // Update Type CodeReference (standalone property)
                if (wall.Construction.Type != null)
                {
                    if (existingWall.Construction.Type == null)
                    {
                        existingWall.Construction.Type = new CodeReference();
                        if (existingWall.Construction.Type.Id == Guid.Empty)
                        {
                            existingWall.Construction.Type.Id = Guid.NewGuid();
                        }
                    }

                    existingWall.Construction.Type.Code = wall.Construction.Type.Code;
                    existingWall.Construction.Type.RValue = wall.Construction.Type.RValue;
                    existingWall.Construction.Type.NominalInsulation = wall.Construction.Type.NominalInsulation;
                    existingWall.Construction.Type.Text = wall.Construction.Type.Text;
                    existingWall.Construction.Type.IdRef = wall.Construction.Type.IdRef;

                    // Update UserDefinedCodeLayers
                    if (wall.Construction.Type.UserDefinedCodeLayers != null)
                    {
                        UpdateUserDefinedCodeLayers(
                            existingWall.Construction.Type.UserDefinedCodeLayers,
                            wall.Construction.Type.UserDefinedCodeLayers);
                    }
                }

                // Update LintelType CodeReference (standalone property)
                if (wall.Construction.LintelType != null)
                {
                    if (existingWall.Construction.LintelType == null)
                    {
                        existingWall.Construction.LintelType = new CodeReference();
                        if (existingWall.Construction.LintelType.Id == Guid.Empty)
                        {
                            existingWall.Construction.LintelType.Id = Guid.NewGuid();
                        }
                    }

                    existingWall.Construction.LintelType.Code = wall.Construction.LintelType.Code;
                    existingWall.Construction.LintelType.RValue = wall.Construction.LintelType.RValue;
                    existingWall.Construction.LintelType.NominalInsulation = wall.Construction.LintelType.NominalInsulation;
                    existingWall.Construction.LintelType.Text = wall.Construction.LintelType.Text;
                    existingWall.Construction.LintelType.IdRef = wall.Construction.LintelType.IdRef;

                    // Update UserDefinedCodeLayers
                    if (wall.Construction.LintelType.UserDefinedCodeLayers != null)
                    {
                        UpdateUserDefinedCodeLayers(
                            existingWall.Construction.LintelType.UserDefinedCodeLayers,
                            wall.Construction.LintelType.UserDefinedCodeLayers);
                    }
                }
            }
            else if (wall.Construction != null)
            {
                // Create new construction if it doesn't exist
                wall.Construction.WallId = wall.Id;
                existingWall.Construction = wall.Construction;
            }

            // Update measurements
            if (wall.Measurements != null && existingWall.Measurements != null)
            {
                existingWall.Measurements.Height = wall.Measurements.Height;
                existingWall.Measurements.Perimeter = wall.Measurements.Perimeter;
            }
            else if (wall.Measurements != null)
            {
                // Create new measurements if they don't exist
                wall.Measurements.WallId = wall.Id;
                existingWall.Measurements = wall.Measurements;
            }

            await _context.SaveChangesAsync();
            return existingWall;
        }

        private void UpdateUserDefinedCodeLayers(
            ICollection<UserDefinedLayer> existingLayers,
            ICollection<UserDefinedLayer> newLayers)
        {
            if (existingLayers == null || newLayers == null)
                return;

            // Remove layers that aren't in the new collection
            var layersToRemove = existingLayers
                .Where(existing => !newLayers.Any(newLayer => newLayer.Id == existing.Id))
                .ToList();

            foreach (var layer in layersToRemove)
            {
                existingLayers.Remove(layer);
                _context.UserDefinedLayers.Remove(layer);
            }

            // Update or add layers
            foreach (var newLayer in newLayers)
            {
                var existingLayer = existingLayers
                    .FirstOrDefault(l => l.Id == newLayer.Id);

                if (existingLayer != null)
                {
                    // Update existing layer
                    existingLayer.Rank = newLayer.Rank;
                    existingLayer.LayerType = newLayer.LayerType;
                }
                else
                {
                    // Add new layer
                    existingLayers.Add(newLayer);
                }
            }
        }

        public async Task DeleteWallAsync(Guid id)
        {
            var wall = await _context.Walls.FindAsync(id);

            if (wall == null)
                throw new KeyNotFoundException($"Wall with ID {id} not found");

            _context.Walls.Remove(wall);
            await _context.SaveChangesAsync();
        }

        public async Task<bool> ExistsAsync(Guid id)
        {
            return await _context.Walls.AnyAsync(w => w.Id == id);
        }
    }
}