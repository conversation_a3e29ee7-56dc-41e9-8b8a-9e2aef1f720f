using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using EnvelopeService.Core.Interfaces;
using EnvelopeService.Core.Models;
using EnvelopeService.Infrastructure.Data;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace EnvelopeService.Infrastructure.Repositories
{
    public class FloorRepository : IFloorRepository
    {
        private readonly EnvelopDbContext _context;

        public FloorRepository(EnvelopDbContext context)
        {
            _context = context;
        }

        public async Task<List<Floor>> GetAllByHouseIdAsync(Guid houseId)
        {
            return await _context.Floors
                .Include(f => f.Construction)
                    .ThenInclude(c => c.Type)
                        .ThenInclude(t => t.UserDefinedCodeLayers)
                .Include(f => f.Measurements)
                .Where(f => f.HouseId == houseId)
                .ToListAsync(); // ✅ Return list of all floors for house
        }

        public async Task<List<Floor>> GetAllByEnergyUpgradeIdAsync(Guid energyUpgradeId)
        {
            return await _context.Floors
                .Include(f => f.Construction)
                    .ThenInclude(c => c.Type)
                        .ThenInclude(t => t.UserDefinedCodeLayers)
                .Include(f => f.Measurements)
                .Where(f => f.EnergyUpgradeId == energyUpgradeId)
                .ToListAsync();
        }


        public async Task<Floor> GetByIdAsync(Guid id)
        {
            return await _context.Floors
                .AsNoTracking()
                .Include(f => f.Construction)
                    .ThenInclude(c => c.Type)
                        .ThenInclude(t => t.UserDefinedCodeLayers)
                .Include(f => f.Measurements)
                .FirstOrDefaultAsync(f => f.Id == id);
        }

        public async Task<Floor> AddAsync(Floor floor)
        {
            // Set IDs for the floor and related entities
            if (floor.Id == Guid.Empty)
            {
                floor.Id = Guid.NewGuid();
            }
            
            if (floor.Construction != null)
            {
                if (floor.Construction.Id == Guid.Empty)
                {
                    floor.Construction.Id = Guid.NewGuid();
                }
                floor.Construction.FloorId = floor.Id;

                // If Type is a new entity, set its ID
                if (floor.Construction.Type != null && floor.Construction.Type.Id == Guid.Empty)
                {
                    floor.Construction.Type.Id = Guid.NewGuid();
                    floor.Construction.TypeReferenceId = floor.Construction.Type.Id;
                }
            }
            
            if (floor.Measurements != null)
            {
                if (floor.Measurements.Id == Guid.Empty)
                {
                    floor.Measurements.Id = Guid.NewGuid();
                }
                floor.Measurements.FloorId = floor.Id;
            }

            _context.Floors.Add(floor);
            await _context.SaveChangesAsync();
            return floor;
        }

        public async Task<Floor> UpdateAsync(Floor floor)
        {
            var existingFloor = await GetByIdAsync(floor.Id);
            if (existingFloor == null)
                return null;

            // Update floor properties
            _context.Entry(existingFloor).CurrentValues.SetValues(floor);

            // Handle Type/CodeReference updates
            if (floor.Construction != null)
            {
                if (existingFloor.Construction != null && floor.Construction.Type != null)
                {
                    // 1. First create a new CodeReference
                    var newCodeRef = new CodeReference
                    {
                        Id = Guid.NewGuid(),
                        Code = floor.Construction.Type.Code,
                        IdRef = floor.Construction.Type.IdRef,
                        NominalInsulation = floor.Construction.Type.NominalInsulation,
                        RValue = floor.Construction.Type.RValue,
                        Text = floor.Construction.Type.Text
                    };
                    _context.CodeReferences.Add(newCodeRef);
                    await _context.SaveChangesAsync();

                    // 2. Delete existing UserDefinedLayers if they exist
                    if (existingFloor.Construction.Type?.UserDefinedCodeLayers != null)
                    {
                        foreach (var layer in existingFloor.Construction.Type.UserDefinedCodeLayers.ToList())
                        {
                            _context.UserDefinedLayers.Remove(layer);
                        }
                        await _context.SaveChangesAsync();
                    }

                    // 3. Create new UserDefinedLayers
                    if (floor.Construction.Type.UserDefinedCodeLayers != null)
                    {
                        foreach (var layer in floor.Construction.Type.UserDefinedCodeLayers)
                        {
                            var newLayer = new UserDefinedLayer
                            {
                                Id = Guid.NewGuid(),
                                CodeReferenceId = newCodeRef.Id,
                                Rank = layer.Rank
                            };
                            _context.UserDefinedLayers.Add(newLayer);
                        }
                    }

                    // 4. Update the FloorConstruction to point to the new CodeReference
                    existingFloor.Construction.TypeReferenceId = newCodeRef.Id;
                }
                else if (existingFloor.Construction == null)
                {
                    // Create new construction
                    if (floor.Construction.Id == Guid.Empty)
                    {
                        floor.Construction.Id = Guid.NewGuid();
                    }

                    // If Type is provided, ensure it has an ID
                    if (floor.Construction.Type != null && floor.Construction.Type.Id == Guid.Empty)
                    {
                        floor.Construction.Type.Id = Guid.NewGuid();
                        floor.Construction.TypeReferenceId = floor.Construction.Type.Id;
                    }

                    floor.Construction.FloorId = floor.Id;
                    existingFloor.Construction = floor.Construction;
                }
            }

            // Update measurements
            if (floor.Measurements != null)
            {
                if (existingFloor.Measurements != null)
                {
                    // Only update non-key properties
                    existingFloor.Measurements.Area = floor.Measurements.Area;
                    existingFloor.Measurements.Length = floor.Measurements.Length;
                }
                else
                {
                    // Create new measurements
                    if (floor.Measurements.Id == Guid.Empty)
                    {
                        floor.Measurements.Id = Guid.NewGuid();
                    }
                    floor.Measurements.FloorId = floor.Id;
                    existingFloor.Measurements = floor.Measurements;
                }
            }

            await _context.SaveChangesAsync();
            return existingFloor;
        }

        public async Task<bool> DeleteAsync(Guid id)
        {
            var floor = await _context.Floors.FindAsync(id);
            if (floor == null)
                return false;

            _context.Floors.Remove(floor);
            await _context.SaveChangesAsync();
            return true;
        }

        public async Task<bool> ExistsAsync(Guid id)
        {
            return await _context.Floors.AnyAsync(f => f.Id == id);
        }
        
        private void UpdateUserDefinedLayers(
            ICollection<UserDefinedLayer> existingLayers,
            ICollection<UserDefinedLayer> newLayers)
        {
            if (existingLayers == null || newLayers == null)
                return;
                
            // Remove layers that aren't in the new collection
            var layersToRemove = existingLayers
                .Where(existing => !newLayers.Any(newLayer => newLayer.Id == existing.Id))
                .ToList();
                
            foreach (var layer in layersToRemove)
            {
                existingLayers.Remove(layer);
                _context.UserDefinedLayers.Remove(layer);
            }
            
            // Update or add layers
            foreach (var newLayer in newLayers)
            {
                var existingLayer = existingLayers
                    .FirstOrDefault(l => l.Id == newLayer.Id);
                    
                if (existingLayer != null)
                {
                    // Update existing layer
                    _context.Entry(existingLayer).CurrentValues.SetValues(newLayer);
                }
                else
                {
                    // Add new layer
                    existingLayers.Add(newLayer);
                }
            }
        }
    }
}