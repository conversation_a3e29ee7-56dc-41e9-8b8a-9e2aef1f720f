using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using EnvelopeService.Core.Interfaces;
using EnvelopeService.Core.Models;
using EnvelopeService.Infrastructure.Data;
using Microsoft.EntityFrameworkCore;

namespace EnvelopeService.Infrastructure.Repositories
{
    public class WindowRepository : IWindowRepository
    {
        private readonly EnvelopDbContext _context;

        public WindowRepository(EnvelopDbContext context)
        {
            _context = context ?? throw new ArgumentNullException(nameof(context));
        }

        public async Task<List<Window>> GetWindowsByHouseIdAsync(Guid houseId)
        {
            var windows = await _context.Windows
                .Include(w => w.Construction)
                    .ThenInclude(c => c.Type)
                .Include(w => w.Measurements)
                .Include(w => w.Shading)
                .Include(w => w.EnergyStar)
                .Where(w => w.HouseId == houseId)
                .ToListAsync();

            // Populate resource text values based on codes
            foreach (var window in windows)
            {
                PopulateResourceTextValues(window);
            }

            return windows;
        }

        public async Task<List<Window>> GetWindowsByEnergyUpgradeIdAsync(Guid energyUpgradeId)
        {
            var windows = await _context.Windows
                .Include(w => w.Construction)
                    .ThenInclude(c => c.Type)
                .Include(w => w.Measurements)
                .Include(w => w.Shading)
                .Include(w => w.EnergyStar)
                .Where(w => w.EnergyUpgradeId == energyUpgradeId)
                .ToListAsync();

            // Populate resource text values based on codes
            foreach (var window in windows)
            {
                PopulateResourceTextValues(window);
            }

            return windows;
        }

        public async Task<Window> GetWindowByIdAsync(Guid id)
        {
            var window = await _context.Windows
                .AsNoTracking()
                .Include(w => w.Construction)
                    .ThenInclude(c => c.Type)
                .Include(w => w.Measurements)
                .Include(w => w.Shading)
                .Include(w => w.EnergyStar)
                .FirstOrDefaultAsync(w => w.Id == id);

            if (window != null)
            {
                PopulateResourceTextValues(window);
            }

            return window;
        }

        public async Task<List<Window>> GetAllWindowsAsync()
        {
            var windows = await _context.Windows
                .Include(w => w.Construction)
                    .ThenInclude(c => c.Type)
                .Include(w => w.Measurements)
                .Include(w => w.Shading)
                .Include(w => w.EnergyStar)
                .ToListAsync();

            // Populate resource text values based on codes
            foreach (var window in windows)
            {
                PopulateResourceTextValues(window);
            }

            return windows;
        }

        private void PopulateResourceTextValues(Window window)
        {
            // Populate FacingDirection text values based on code
            if (window.FacingDirection != null && !string.IsNullOrEmpty(window.FacingDirection.Code))
            {
                var direction = GetWindowDirectionByCode(window.FacingDirection.Code);
                window.FacingDirection = new WindowDirections
                {
                    Code = window.FacingDirection.Code,
                    English = direction.English,
                    French = direction.French,
                    IsUserSpecified = window.FacingDirection.IsUserSpecified
                };
            }

            // Populate WindowTilt text values based on code
            if (window.Measurements?.Tilt != null && !string.IsNullOrEmpty(window.Measurements.Tilt.Code))
            {
                var tilt = GetWindowTiltByCode(window.Measurements.Tilt.Code);
                window.Measurements.Tilt = new WindowTilts
                {
                    Code = window.Measurements.Tilt.Code,
                    English = tilt.English,
                    French = tilt.French,
                    Value = window.Measurements.Tilt.Value,
                    IsUserSpecified = window.Measurements.Tilt.IsUserSpecified
                };
            }
        }

        public async Task<Window> AddWindowAsync(Window window)
        {
            // Set IDs for the window and related entities
            if (window.Id == Guid.Empty)
            {
                window.Id = Guid.NewGuid();
            }

            // Handle WindowConstruction
            if (window.Construction != null)
            {
                if (window.Construction.Id == Guid.Empty)
                {
                    window.Construction.Id = Guid.NewGuid();
                }
                window.Construction.WindowId = window.Id;

                // Handle Type CodeReference
                if (window.Construction.Type != null && window.Construction.Type.Id == Guid.Empty)
                {
                    window.Construction.Type.Id = Guid.NewGuid();
                }
            }

            // Set Measurements ID
            if (window.Measurements != null)
            {
                if (window.Measurements.Id == Guid.Empty)
                {
                    window.Measurements.Id = Guid.NewGuid();
                }
                window.Measurements.WindowId = window.Id;
            }

            // Set Shading ID
            if (window.Shading != null)
            {
                if (window.Shading.Id == Guid.Empty)
                {
                    window.Shading.Id = Guid.NewGuid();
                }
                window.Shading.WindowId = window.Id;
            }

            // Set EnergyStar ID
            if (window.EnergyStar != null)
            {
                if (window.EnergyStar.Id == Guid.Empty)
                {
                    window.EnergyStar.Id = Guid.NewGuid();
                }
                window.EnergyStar.WindowId = window.Id;
            }

            _context.Windows.Add(window);
            await _context.SaveChangesAsync();
            return window;
        }

        public async Task<Window> UpdateWindowAsync(Window window)
        {
            var existingWindow = await GetWindowByIdAsync(window.Id);
            if (existingWindow == null)
                return null;

            // Update Window properties 
            existingWindow.Label = window.Label;
            existingWindow.Number = window.Number;
            existingWindow.Er = window.Er;
            existingWindow.Shgc = window.Shgc;
            existingWindow.FrameHeight = window.FrameHeight;
            existingWindow.FrameAreaFraction = window.FrameAreaFraction;
            existingWindow.EdgeOfGlassFraction = window.EdgeOfGlassFraction;
            existingWindow.CentreOfGlassFraction = window.CentreOfGlassFraction;
            existingWindow.AdjacentEnclosedSpace = window.AdjacentEnclosedSpace;

            // Update FacingDirection
            if (window.FacingDirection != null)
            {
                existingWindow.FacingDirection.Code = window.FacingDirection.Code;
                existingWindow.FacingDirection.English = window.FacingDirection.English;
                existingWindow.FacingDirection.French = window.FacingDirection.French;
                existingWindow.FacingDirection.IsUserSpecified = window.FacingDirection.IsUserSpecified;
            }

            // Update WindowConstruction
            if (window.Construction != null && existingWindow.Construction != null)
            {
                existingWindow.Construction.EnergyStar = window.Construction.EnergyStar;

                // Update Type CodeReference
                if (window.Construction.Type != null)
                {
                    if (existingWindow.Construction.Type == null)
                    {
                        existingWindow.Construction.Type = new CodeReference();
                        if (existingWindow.Construction.Type.Id == Guid.Empty)
                        {
                            existingWindow.Construction.Type.Id = Guid.NewGuid();
                        }
                    }

                    existingWindow.Construction.Type.Code = window.Construction.Type.Code;
                    existingWindow.Construction.Type.RValue = window.Construction.Type.RValue;
                    existingWindow.Construction.Type.NominalInsulation = window.Construction.Type.NominalInsulation;
                    existingWindow.Construction.Type.Text = window.Construction.Type.Text;
                    existingWindow.Construction.Type.IdRef = window.Construction.Type.IdRef;
                }
            }
            else if (window.Construction != null)
            {
                // Create new construction if it doesn't exist
                window.Construction.WindowId = window.Id;
                existingWindow.Construction = window.Construction;
            }

            // Update measurements
            if (window.Measurements != null && existingWindow.Measurements != null)
            {
                existingWindow.Measurements.Height = window.Measurements.Height;
                existingWindow.Measurements.Width = window.Measurements.Width;
                existingWindow.Measurements.HeaderHeight = window.Measurements.HeaderHeight;
                existingWindow.Measurements.OverhangWidth = window.Measurements.OverhangWidth;

                // Update Tilt
                if (window.Measurements.Tilt != null)
                {
                    existingWindow.Measurements.Tilt.Code = window.Measurements.Tilt.Code;
                    existingWindow.Measurements.Tilt.English = window.Measurements.Tilt.English;
                    existingWindow.Measurements.Tilt.French = window.Measurements.Tilt.French;
                    existingWindow.Measurements.Tilt.Value = window.Measurements.Tilt.Value;
                    existingWindow.Measurements.Tilt.IsUserSpecified = window.Measurements.Tilt.IsUserSpecified;
                }
            }
            else if (window.Measurements != null)
            {
                // Create new measurements if they don't exist
                window.Measurements.WindowId = window.Id;
                existingWindow.Measurements = window.Measurements;
            }

            // Update shading
            if (window.Shading != null && existingWindow.Shading != null)
            {
                existingWindow.Shading.Curtain = window.Shading.Curtain;
                existingWindow.Shading.ShutterRValue = window.Shading.ShutterRValue;
            }
            else if (window.Shading != null)
            {
                // Create new shading if it doesn't exist
                window.Shading.WindowId = window.Id;
                existingWindow.Shading = window.Shading;
            }

            // Update EnergyStar
            if (window.EnergyStar != null && existingWindow.EnergyStar != null)
            {
                existingWindow.EnergyStar.ERText = window.EnergyStar.ERText;
                existingWindow.EnergyStar.UValueMinText = window.EnergyStar.UValueMinText;
                existingWindow.EnergyStar.UValueMaxText = window.EnergyStar.UValueMaxText;
            }
            else if (window.EnergyStar != null)
            {
                // Create new EnergyStar if it doesn't exist
                window.EnergyStar.WindowId = window.Id;
                existingWindow.EnergyStar = window.EnergyStar;
            }

            await _context.SaveChangesAsync();
            return existingWindow;
        }

        public async Task DeleteWindowAsync(Guid id)
        {
            var window = await _context.Windows.FindAsync(id);

            if (window == null)
                throw new KeyNotFoundException($"Window with ID {id} not found");

            _context.Windows.Remove(window);
            await _context.SaveChangesAsync();
        }

        public async Task<bool> ExistsAsync(Guid id)
        {
            return await _context.Windows.AnyAsync(w => w.Id == id);
        }

        private static WindowDirections GetWindowDirectionByCode(string code)
        {
            return code switch
            {
                "1" => WindowDirections.South,
                "2" => WindowDirections.Southeast,
                "3" => WindowDirections.East,
                "4" => WindowDirections.Northeast,
                "5" => WindowDirections.North,
                "6" => WindowDirections.Northwest,
                "7" => WindowDirections.West,
                "8" => WindowDirections.Southwest,
                _ => WindowDirections.South
            };
        }

        private static WindowTilts GetWindowTiltByCode(string code)
        {
            return code switch
            {
                "1" => WindowTilts.Vertical,
                "2" => WindowTilts.Horizontal,
                "3" => WindowTilts.SameAsRoof,
                "4" => WindowTilts.UserSpecified,
                _ => WindowTilts.Vertical
            };
        }
    }
}
