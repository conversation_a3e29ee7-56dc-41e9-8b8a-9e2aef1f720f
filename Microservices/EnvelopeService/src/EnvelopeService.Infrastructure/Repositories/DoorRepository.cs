using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using EnvelopeService.Core.Interfaces;
using EnvelopeService.Core.Models;
using EnvelopeService.Infrastructure.Data;
using Microsoft.EntityFrameworkCore;

namespace EnvelopeService.Infrastructure.Repositories
{
    public class DoorRepository : IDoorRepository
    {
        private readonly EnvelopDbContext _context;

        public DoorRepository(EnvelopDbContext context)
        {
            _context = context ?? throw new ArgumentNullException(nameof(context));
        }

        public async Task<List<Door>> GetDoorsByHouseIdAsync(Guid houseId)
        {
            var doors = await _context.Doors
                .Include(d => d.Construction)
                .Include(d => d.Measurements)
                .Where(d => d.HouseId == houseId)
                .ToListAsync();

            // Populate DoorType text values based on codes
            foreach (var door in doors)
            {
                if (door.Construction?.Type != null && !string.IsNullOrEmpty(door.Construction.Type.Code))
                {
                    var doorType = GetDoorTypeByCode(door.Construction.Type.Code);
                    if (doorType != null)
                    {
                        door.Construction.Type = new DoorTypes
                        {
                            Code = door.Construction.Type.Code,
                            English = doorType.English,
                            French = doorType.French,
                            Value = door.Construction.Type.Value,
                            IsUserSpecified = door.Construction.Type.IsUserSpecified
                        };
                    }
                }
            }

            return doors;
        }

        public async Task<List<Door>> GetDoorsByEnergyUpgradeIdAsync(Guid energyUpgradeId)
        {
            var doors = await _context.Doors
                .Include(d => d.Construction)
                .Include(d => d.Measurements)
                .Where(d => d.EnergyUpgradeId == energyUpgradeId)
                .ToListAsync();

            // Populate DoorType text values based on codes
            foreach (var door in doors)
            {
                if (door.Construction?.Type != null && !string.IsNullOrEmpty(door.Construction.Type.Code))
                {
                    var doorType = GetDoorTypeByCode(door.Construction.Type.Code);
                    if (doorType != null)
                    {
                        door.Construction.Type = new DoorTypes
                        {
                            Code = door.Construction.Type.Code,
                            English = doorType.English,
                            French = doorType.French,
                            Value = door.Construction.Type.Value,
                            IsUserSpecified = door.Construction.Type.IsUserSpecified
                        };
                    }
                }
            }

            return doors;
        }

        public async Task<Door> GetDoorByIdAsync(Guid id)
        {
            var door = await _context.Doors
                .AsNoTracking()
                .Include(d => d.Construction)
                .Include(d => d.Measurements)
                .FirstOrDefaultAsync(d => d.Id == id);

            // Populate DoorType text values based on code
            if (door?.Construction?.Type != null && !string.IsNullOrEmpty(door.Construction.Type.Code))
            {
                var doorType = GetDoorTypeByCode(door.Construction.Type.Code);
                door.Construction.Type = new DoorTypes
                {
                    Code = door.Construction.Type.Code,
                    English = doorType.English,
                    French = doorType.French,
                    Value = door.Construction.Type.Value,
                    IsUserSpecified = door.Construction.Type.IsUserSpecified
                };
            }

            return door;
        }

        public async Task<List<Door>> GetAllDoorsAsync()
        {
            var doors = await _context.Doors
                .Include(d => d.Construction)
                .Include(d => d.Measurements)
                .ToListAsync();

            // Populate DoorType text values based on codes
            foreach (var door in doors)
            {
                if (door.Construction?.Type != null && !string.IsNullOrEmpty(door.Construction.Type.Code))
                {
                    var doorType = GetDoorTypeByCode(door.Construction.Type.Code);
                    door.Construction.Type = new DoorTypes
                    {
                        Code = door.Construction.Type.Code,
                        English = doorType.English,
                        French = doorType.French,
                        Value = door.Construction.Type.Value,
                        IsUserSpecified = door.Construction.Type.IsUserSpecified
                    };
                }
            }

            return doors;
        }

        public async Task<Door> AddDoorAsync(Door door)
        {
            // Set IDs for the door and related entities
            if (door.Id == Guid.Empty)
            {
                door.Id = Guid.NewGuid();
            }

            // Handle DoorConstruction
            if (door.Construction != null)
            {
                if (door.Construction.Id == Guid.Empty)
                {
                    door.Construction.Id = Guid.NewGuid();
                }
                door.Construction.DoorId = door.Id;
            }

            // Set Measurements ID
            if (door.Measurements != null)
            {
                if (door.Measurements.Id == Guid.Empty)
                {
                    door.Measurements.Id = Guid.NewGuid();
                }
                door.Measurements.DoorId = door.Id;
            }

            _context.Doors.Add(door);
            await _context.SaveChangesAsync();
            return door;
        }

        public async Task<Door> UpdateDoorAsync(Door door)
        {
            var existingDoor = await GetDoorByIdAsync(door.Id);
            if (existingDoor == null)
                return null;

            // Update Door properties 
            existingDoor.Label = door.Label;
            existingDoor.AdjacentEnclosedSpace = door.AdjacentEnclosedSpace;

            // Update DoorConstruction
            if (door.Construction != null && existingDoor.Construction != null)
            {
                existingDoor.Construction.EnergyStar = door.Construction.EnergyStar;

                // Update Type
                if (door.Construction.Type != null)
                {
                    existingDoor.Construction.Type.Code = door.Construction.Type.Code;
                    existingDoor.Construction.Type.English = door.Construction.Type.English;
                    existingDoor.Construction.Type.French = door.Construction.Type.French;
                    existingDoor.Construction.Type.Value = door.Construction.Type.Value;
                    existingDoor.Construction.Type.IsUserSpecified = door.Construction.Type.IsUserSpecified;
                }
            }
            else if (door.Construction != null)
            {
                // Create new construction if it doesn't exist
                door.Construction.DoorId = door.Id;
                existingDoor.Construction = door.Construction;
            }

            // Update measurements
            if (door.Measurements != null && existingDoor.Measurements != null)
            {
                existingDoor.Measurements.Height = door.Measurements.Height;
                existingDoor.Measurements.Width = door.Measurements.Width;
            }
            else if (door.Measurements != null)
            {
                // Create new measurements if they don't exist
                door.Measurements.DoorId = door.Id;
                existingDoor.Measurements = door.Measurements;
            }

            await _context.SaveChangesAsync();
            return existingDoor;
        }

        public async Task DeleteDoorAsync(Guid id)
        {
            var door = await _context.Doors.FindAsync(id);

            if (door == null)
                throw new KeyNotFoundException($"Door with ID {id} not found");

            _context.Doors.Remove(door);
            await _context.SaveChangesAsync();
        }

        public async Task<bool> ExistsAsync(Guid id)
        {
            return await _context.Doors.AnyAsync(d => d.Id == id);
        }

        private static DoorTypes GetDoorTypeByCode(string code)
        {
            return code switch
            {
                "1" => DoorTypes.WoodHollowCore,
                "2" => DoorTypes.SolidWood,
                "3" => DoorTypes.SteelFibreglassCore,
                "4" => DoorTypes.SteelPolystyreneCore,
                "5" => DoorTypes.SteelMediumDensitySprayFoamCore,
                "6" => DoorTypes.FibreglassPolystyreneCore,
                "7" => DoorTypes.FibreglassMediumDensitySprayFoamCore,
                "8" => DoorTypes.UserSpecified,
                _ => DoorTypes.WoodHollowCore
            };
        }
    }
}
