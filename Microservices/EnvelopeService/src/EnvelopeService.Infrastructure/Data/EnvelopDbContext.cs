// ==============================================
// UPDATED DbContext - Fix Ceiling configurations
// ==============================================

using EnvelopeService.Core.Models;
using Microsoft.EntityFrameworkCore;

namespace EnvelopeService.Infrastructure.Data
{
    public class EnvelopDbContext : DbContext
    {
        public EnvelopDbContext(DbContextOptions<EnvelopDbContext> options)
            : base(options)
        {
        }
        
        // Keep all DbSets as they were
        public DbSet<Wall> Walls { get; set; } = null!;
        public DbSet<WallConstruction> WallConstructions { get; set; } = null!;
        public DbSet<WallMeasurements> WallMeasurements { get; set; } = null!;
        public DbSet<Ceiling> Ceilings { get; set; } = null!;
        public DbSet<CeilingConstruction> CeilingConstructions { get; set; } = null!;
        public DbSet<CeilingMeasurements> CeilingMeasurements { get; set; } = null!;
        public DbSet<CodeReference> CodeReferences { get; set; } = null!;
        public DbSet<UserDefinedLayer> UserDefinedLayers { get; set; } = null!;
        public DbSet<Floor> Floors { get; set; } = null!;
        public DbSet<FloorConstruction> FloorConstructions { get; set; } = null!;
        public DbSet<FloorMeasurements> FloorMeasurements { get; set; } = null!;
        public DbSet<RsiSection> RsiSections { get; set; } = null!;
        public DbSet<CodeDescriptionAndComposite> CodeDescriptionAndComposites { get; set; } = null!;

        // Door and Window DbSets
        public DbSet<Door> Doors { get; set; } = null!;
        public DbSet<DoorConstruction> DoorConstructions { get; set; } = null!;
        public DbSet<DoorMeasurements> DoorMeasurements { get; set; } = null!;
        public DbSet<Window> Windows { get; set; } = null!;
        public DbSet<WindowConstruction> WindowConstructions { get; set; } = null!;
        public DbSet<WindowMeasurements> WindowMeasurements { get; set; } = null!;
        public DbSet<WindowShading> WindowShadings { get; set; } = null!;
        public DbSet<EnergyStar> EnergyStars { get; set; } = null!;

        // FloorHeader DbSets
        public DbSet<FloorHeader> FloorHeaders { get; set; } = null!;
        public DbSet<FloorHeaderConstruction> FloorHeaderConstructions { get; set; } = null!;
        public DbSet<FloorHeaderMeasurements> FloorHeaderMeasurements { get; set; } = null!;

        // Room DbSets
        public DbSet<Room> Rooms { get; set; } = null!;
        public DbSet<RoomConstruction> RoomConstructions { get; set; } = null!;
        public DbSet<RoomMeasurements> RoomMeasurements { get; set; } = null!;

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            modelBuilder.HasDefaultSchema("envelope");

            // Ignore base classes that are used for composition, not inheritance
            // Note: CodeAndText and CodeTextAndValue are only used as owned entities (following HvacService pattern)
            modelBuilder.Ignore<CodeAndText>();
            modelBuilder.Ignore<CodeTextAndValue>();

            base.OnModelCreating(modelBuilder);

            // WALL configurations (no changes)
            modelBuilder.Entity<Wall>(entity =>
            {
                entity.ToTable("Walls", "envelope");
                entity.HasKey(e => e.Id);
                entity.Property(e => e.Label).IsRequired().HasMaxLength(100);

                entity.OwnsOne(e => e.FacingDirection, direction =>
                {
                    direction.Property(d => d.Code).HasColumnName("FacingDirectionCode").HasMaxLength(10);
                    direction.Property(d => d.EnglishText).HasColumnName("FacingDirectionEnglishText").HasMaxLength(100);
                    direction.Property(d => d.FrenchText).HasColumnName("FacingDirectionFrenchText").HasMaxLength(100);
                    direction.Property(d => d.IsUserSpecified).HasColumnName("FacingDirectionIsUserSpecified");
                });

                entity.HasOne(e => e.Construction)
                    .WithOne(e => e.Wall)
                    .HasForeignKey<WallConstruction>(e => e.WallId)
                    .OnDelete(DeleteBehavior.Cascade);

                entity.HasOne(e => e.Measurements)
                    .WithOne(e => e.Wall)
                    .HasForeignKey<WallMeasurements>(e => e.WallId)
                    .OnDelete(DeleteBehavior.Cascade);
            });

            modelBuilder.Entity<WallConstruction>(entity =>
            {
                entity.ToTable("WallConstructions", "envelope");
                entity.HasKey(e => e.Id);
                entity.Property(e => e.Corners).HasPrecision(18, 2);
                entity.Property(e => e.Intersections).HasPrecision(18, 2);

                entity.HasOne(e => e.Type)
                    .WithMany()
                    .OnDelete(DeleteBehavior.NoAction);

                entity.HasOne(e => e.LintelType)
                    .WithMany()
                    .OnDelete(DeleteBehavior.NoAction);

                entity.HasOne(e => e.Wall)
                    .WithOne(e => e.Construction)
                    .HasForeignKey<WallConstruction>(e => e.WallId)
                    .OnDelete(DeleteBehavior.Cascade);
            });
            
            // ✅ UPDATED: Ceiling configurations to match new models
            modelBuilder.Entity<Ceiling>(entity =>
            {
                entity.ToTable("Ceilings", "envelope");
                entity.HasKey(e => e.Id);
                entity.Property(e => e.Label).IsRequired().HasMaxLength(100); // ✅ Changed from Name to Label
                
                entity.HasOne(e => e.Construction)
                    .WithOne(e => e.Ceiling)
                    .HasForeignKey<CeilingConstruction>(e => e.CeilingId)
                    .OnDelete(DeleteBehavior.Cascade);
                    
                entity.HasOne(e => e.Measurements)
                    .WithOne(e => e.Ceiling)
                    .HasForeignKey<CeilingMeasurements>(e => e.CeilingId)
                    .OnDelete(DeleteBehavior.Cascade);
            });
            
            // ✅ UPDATED: CeilingConstruction configuration to match new model
            modelBuilder.Entity<CeilingConstruction>(entity =>
            {
                entity.ToTable("CeilingConstructions", "envelope");
                entity.HasKey(e => e.Id);

                // ✅ Configure Type as owned entity - save individual fields to database
                entity.OwnsOne(e => e.Type, type =>
                {
                    type.Property(t => t.Code).HasColumnName("TypeCode").HasMaxLength(10);
                    type.Property(t => t.EnglishText).HasColumnName("TypeEnglishText").HasMaxLength(100);
                    type.Property(t => t.FrenchText).HasColumnName("TypeFrenchText").HasMaxLength(100);
                    type.Property(t => t.IsUserSpecified).HasColumnName("TypeIsUserSpecified");
                });

                // ✅ Configure the CodeReference relationship
                entity.HasOne(e => e.CeilingTypeReference)
                    .WithMany()
                    .HasForeignKey(e => e.CeilingTypeReferenceId)
                    .OnDelete(DeleteBehavior.Cascade);

                // ✅ Configure relationship with Ceiling
                entity.HasOne(e => e.Ceiling)
                    .WithOne(e => e.Construction)
                    .HasForeignKey<CeilingConstruction>(e => e.CeilingId)
                    .OnDelete(DeleteBehavior.Cascade);
            });
            
            // ✅ UPDATED: CeilingMeasurements configuration to match new model
            modelBuilder.Entity<CeilingMeasurements>(entity =>
            {
                entity.ToTable("CeilingMeasurements", "envelope");
                entity.HasKey(e => e.Id);
                entity.Property(e => e.Area).HasPrecision(18, 2);
                entity.Property(e => e.Length).HasPrecision(18, 2);
                entity.Property(e => e.HeelHeight).HasPrecision(18, 2);

                // ✅ Configure Slope as owned entity - save individual fields to database
                entity.OwnsOne(e => e.Slope, slope =>
                {
                    slope.Property(s => s.Code).HasColumnName("SlopeCode").HasMaxLength(10);
                    slope.Property(s => s.EnglishText).HasColumnName("SlopeEnglishText").HasMaxLength(100);
                    slope.Property(s => s.FrenchText).HasColumnName("SlopeFrenchText").HasMaxLength(100);
                    slope.Property(s => s.IsUserSpecified).HasColumnName("SlopeIsUserSpecified");
                    slope.Property(s => s.Value).HasColumnName("SlopeValue").HasPrecision(18, 6);
                });

                // ✅ Configure relationship with Ceiling
                entity.HasOne(e => e.Ceiling)
                    .WithOne(e => e.Measurements)
                    .HasForeignKey<CeilingMeasurements>(e => e.CeilingId)
                    .OnDelete(DeleteBehavior.Cascade);
            });
            
            // CodeReference configuration (no changes)
            modelBuilder.Entity<CodeReference>(entity =>
            {
                entity.ToTable("CodeReferences", "envelope");
                entity.HasKey(e => e.Id);
                entity.Property(e => e.IdRef).HasMaxLength(50);
                entity.Property(e => e.Code).HasMaxLength(50);
                entity.Property(e => e.RValue).HasPrecision(18, 2);
                entity.Property(e => e.NominalInsulation).HasPrecision(18, 2);
                entity.Property(e => e.Text).HasMaxLength(255);
                
                entity.HasMany(e => e.UserDefinedCodeLayers)
                    .WithOne(e => e.CodeReference)
                    .HasForeignKey(e => e.CodeReferenceId)
                    .OnDelete(DeleteBehavior.Cascade);
            });
            
            // UserDefinedLayer configuration (no changes)
            modelBuilder.Entity<UserDefinedLayer>(entity =>
            {
                entity.ToTable("UserDefinedLayers", "envelope");
                entity.HasKey(e => e.Id);
                entity.Property(e => e.LayerType).HasMaxLength(50);
                
                entity.HasDiscriminator<string>("LayerType")
                    .HasValue<UserDefinedLayer>("UserDefinedLayer");
            });
            
            // RsiSection configuration (no changes)
            modelBuilder.Entity<RsiSection>(entity =>
            {
                entity.ToTable("RsiSections", "envelope");
                entity.HasKey(e => e.Id);
                entity.Property(e => e.Percentage).HasPrecision(18, 2);
                entity.Property(e => e.Rsi).HasPrecision(18, 2);
                entity.Property(e => e.NominalRsi).HasPrecision(18, 2);
                
                entity.HasOne(e => e.CodeDescriptionAndComposite)
                    .WithMany(e => e.Composite)
                    .HasForeignKey(e => e.CodeDescriptionAndCompositeId)
                    .OnDelete(DeleteBehavior.Cascade);
            });
            
            // CodeDescriptionAndComposite configuration (no changes)
            modelBuilder.Entity<CodeDescriptionAndComposite>(entity =>
            {
                entity.ToTable("CodeDescriptionAndComposites", "envelope");
                entity.HasKey(e => e.Id);
                entity.Property(e => e.Description).HasMaxLength(255);
                entity.Property(e => e.IdRef).HasMaxLength(50);
                entity.Property(e => e.Code).HasMaxLength(50);
                entity.Property(e => e.NominalInsulation).HasPrecision(18, 2);
                
                entity.HasMany(e => e.Composite)
                    .WithOne(e => e.CodeDescriptionAndComposite)
                    .HasForeignKey(e => e.CodeDescriptionAndCompositeId)
                    .OnDelete(DeleteBehavior.Cascade);
                    
                entity.HasMany(e => e.UserDefinedCodeLayers)
                    .WithOne()
                    .OnDelete(DeleteBehavior.Cascade);
            });
            
            // Floor configurations (no changes)
            modelBuilder.Entity<Floor>(entity =>
            {
                entity.ToTable("Floors", "envelope");
                entity.HasKey(e => e.Id);
                entity.Property(e => e.Label).IsRequired().HasMaxLength(100);
                
                entity.HasOne(e => e.Construction)
                    .WithOne(e => e.Floor)
                    .HasForeignKey<FloorConstruction>(e => e.FloorId)
                    .OnDelete(DeleteBehavior.Cascade);
                    
                entity.HasOne(e => e.Measurements)
                    .WithOne(e => e.Floor)
                    .HasForeignKey<FloorMeasurements>(e => e.FloorId)
                    .OnDelete(DeleteBehavior.Cascade);
            });
            
            modelBuilder.Entity<FloorConstruction>(entity =>
            {
                entity.ToTable("FloorConstructions", "envelope");
                entity.HasKey(e => e.Id);
                
                entity.HasOne(e => e.Type)
                    .WithMany()
                    .HasForeignKey(e => e.TypeReferenceId)
                    .OnDelete(DeleteBehavior.Cascade);
            });
            
            modelBuilder.Entity<FloorMeasurements>(entity =>
            {
                entity.ToTable("FloorMeasurements", "envelope");
                entity.HasKey(e => e.Id);
                entity.Property(e => e.Area).HasPrecision(18, 2);
                entity.Property(e => e.Length).HasPrecision(18, 2);
            });

            // DOOR configurations
            modelBuilder.Entity<Door>(entity =>
            {
                entity.ToTable("Doors", "envelope");
                entity.HasKey(e => e.Id);
                entity.Property(e => e.Label).IsRequired().HasMaxLength(100);

                entity.HasOne(e => e.Construction)
                    .WithOne(e => e.Door)
                    .HasForeignKey<DoorConstruction>(e => e.DoorId)
                    .OnDelete(DeleteBehavior.Cascade);

                entity.HasOne(e => e.Measurements)
                    .WithOne(e => e.Door)
                    .HasForeignKey<DoorMeasurements>(e => e.DoorId)
                    .OnDelete(DeleteBehavior.Cascade);
            });

            modelBuilder.Entity<DoorConstruction>(entity =>
            {
                entity.ToTable("DoorConstructions", "envelope");
                entity.HasKey(e => e.Id);

                entity.OwnsOne(e => e.Type, type =>
                {
                    type.Property(t => t.Code).HasColumnName("TypeCode").HasMaxLength(10);
                    type.Property(t => t.English).HasColumnName("TypeEnglish").HasMaxLength(100);
                    type.Property(t => t.French).HasColumnName("TypeFrench").HasMaxLength(100);
                    type.Property(t => t.Value).HasColumnName("TypeValue").HasPrecision(18, 2);
                    type.Property(t => t.IsUserSpecified).HasColumnName("TypeIsUserSpecified");
                });

                entity.HasOne(e => e.Door)
                    .WithOne(e => e.Construction)
                    .HasForeignKey<DoorConstruction>(e => e.DoorId)
                    .OnDelete(DeleteBehavior.Cascade);
            });

            modelBuilder.Entity<DoorMeasurements>(entity =>
            {
                entity.ToTable("DoorMeasurements", "envelope");
                entity.HasKey(e => e.Id);
                entity.Property(e => e.Height).HasPrecision(18, 2);
                entity.Property(e => e.Width).HasPrecision(18, 2);

                entity.HasOne(e => e.Door)
                    .WithOne(e => e.Measurements)
                    .HasForeignKey<DoorMeasurements>(e => e.DoorId)
                    .OnDelete(DeleteBehavior.Cascade);
            });

            // WINDOW configurations
            modelBuilder.Entity<Window>(entity =>
            {
                entity.ToTable("Windows", "envelope");
                entity.HasKey(e => e.Id);
                entity.Property(e => e.Label).IsRequired().HasMaxLength(100);
                entity.Property(e => e.Number).HasPrecision(18, 2);
                entity.Property(e => e.Er).HasPrecision(18, 2);
                entity.Property(e => e.Shgc).HasPrecision(18, 2);
                entity.Property(e => e.FrameHeight).HasPrecision(18, 2);
                entity.Property(e => e.FrameAreaFraction).HasPrecision(18, 2);
                entity.Property(e => e.EdgeOfGlassFraction).HasPrecision(18, 2);
                entity.Property(e => e.CentreOfGlassFraction).HasPrecision(18, 2);

                entity.OwnsOne(e => e.FacingDirection, direction =>
                {
                    direction.Property(d => d.Code).HasColumnName("FacingDirectionCode").HasMaxLength(10);
                    direction.Property(d => d.English).HasColumnName("FacingDirectionEnglish").HasMaxLength(100);
                    direction.Property(d => d.French).HasColumnName("FacingDirectionFrench").HasMaxLength(100);
                    direction.Property(d => d.IsUserSpecified).HasColumnName("FacingDirectionIsUserSpecified");
                });

                entity.HasOne(e => e.Construction)
                    .WithOne(e => e.Window)
                    .HasForeignKey<WindowConstruction>(e => e.WindowId)
                    .OnDelete(DeleteBehavior.Cascade);

                entity.HasOne(e => e.Measurements)
                    .WithOne(e => e.Window)
                    .HasForeignKey<WindowMeasurements>(e => e.WindowId)
                    .OnDelete(DeleteBehavior.Cascade);

                entity.HasOne(e => e.Shading)
                    .WithOne(e => e.Window)
                    .HasForeignKey<WindowShading>(e => e.WindowId)
                    .OnDelete(DeleteBehavior.Cascade);

                entity.HasOne(e => e.EnergyStar)
                    .WithOne(e => e.Window)
                    .HasForeignKey<EnergyStar>(e => e.WindowId)
                    .OnDelete(DeleteBehavior.Cascade);
            });

            modelBuilder.Entity<WindowConstruction>(entity =>
            {
                entity.ToTable("WindowConstructions", "envelope");
                entity.HasKey(e => e.Id);

                entity.HasOne(e => e.Type)
                    .WithMany()
                    .OnDelete(DeleteBehavior.NoAction);

                entity.HasOne(e => e.Window)
                    .WithOne(e => e.Construction)
                    .HasForeignKey<WindowConstruction>(e => e.WindowId)
                    .OnDelete(DeleteBehavior.Cascade);
            });

            modelBuilder.Entity<WindowMeasurements>(entity =>
            {
                entity.ToTable("WindowMeasurements", "envelope");
                entity.HasKey(e => e.Id);
                entity.Property(e => e.Height).HasPrecision(18, 2);
                entity.Property(e => e.Width).HasPrecision(18, 2);
                entity.Property(e => e.HeaderHeight).HasPrecision(18, 2);
                entity.Property(e => e.OverhangWidth).HasPrecision(18, 2);

                entity.OwnsOne(e => e.Tilt, tilt =>
                {
                    tilt.Property(t => t.Code).HasColumnName("TiltCode").HasMaxLength(10);
                    tilt.Property(t => t.English).HasColumnName("TiltEnglish").HasMaxLength(100);
                    tilt.Property(t => t.French).HasColumnName("TiltFrench").HasMaxLength(100);
                    tilt.Property(t => t.Value).HasColumnName("TiltValue").HasPrecision(18, 6);
                    tilt.Property(t => t.IsUserSpecified).HasColumnName("TiltIsUserSpecified");
                });

                entity.HasOne(e => e.Window)
                    .WithOne(e => e.Measurements)
                    .HasForeignKey<WindowMeasurements>(e => e.WindowId)
                    .OnDelete(DeleteBehavior.Cascade);
            });

            modelBuilder.Entity<WindowShading>(entity =>
            {
                entity.ToTable("WindowShadings", "envelope");
                entity.HasKey(e => e.Id);
                entity.Property(e => e.Curtain).HasPrecision(18, 2);
                entity.Property(e => e.ShutterRValue).HasPrecision(18, 2);

                entity.HasOne(e => e.Window)
                    .WithOne(e => e.Shading)
                    .HasForeignKey<WindowShading>(e => e.WindowId)
                    .OnDelete(DeleteBehavior.Cascade);
            });

            modelBuilder.Entity<EnergyStar>(entity =>
            {
                entity.ToTable("EnergyStars", "envelope");
                entity.HasKey(e => e.Id);
                entity.Property(e => e.ERText).HasMaxLength(50);
                entity.Property(e => e.UValueMinText).HasMaxLength(50);
                entity.Property(e => e.UValueMaxText).HasMaxLength(50);

                entity.HasOne(e => e.Window)
                    .WithOne(e => e.EnergyStar)
                    .HasForeignKey<EnergyStar>(e => e.WindowId)
                    .OnDelete(DeleteBehavior.Cascade);
            });

            // FLOORHEADER configurations
            modelBuilder.Entity<FloorHeader>(entity =>
            {
                entity.ToTable("FloorHeaders", "envelope");
                entity.HasKey(e => e.Id);
                entity.Property(e => e.Label).IsRequired().HasMaxLength(100);

                entity.OwnsOne(e => e.FacingDirection, direction =>
                {
                    direction.Property(d => d.Code).HasColumnName("FacingDirectionCode").HasMaxLength(10);
                    direction.Property(d => d.English).HasColumnName("FacingDirectionEnglish").HasMaxLength(100);
                    direction.Property(d => d.French).HasColumnName("FacingDirectionFrench").HasMaxLength(100);
                    direction.Property(d => d.IsUserSpecified).HasColumnName("FacingDirectionIsUserSpecified");
                });

                entity.HasOne(e => e.Construction)
                    .WithOne(e => e.FloorHeader)
                    .HasForeignKey<FloorHeaderConstruction>(e => e.FloorHeaderId)
                    .OnDelete(DeleteBehavior.Cascade);

                entity.HasOne(e => e.Measurements)
                    .WithOne(e => e.FloorHeader)
                    .HasForeignKey<FloorHeaderMeasurements>(e => e.FloorHeaderId)
                    .OnDelete(DeleteBehavior.Cascade);
            });

            modelBuilder.Entity<FloorHeaderConstruction>(entity =>
            {
                entity.ToTable("FloorHeaderConstructions", "envelope");
                entity.HasKey(e => e.Id);

                // Configure the CodeReference relationship (Type is inherited from Construction)
                entity.HasOne(e => e.Type)
                    .WithMany()
                    .HasForeignKey(e => e.TypeReferenceId)
                    .OnDelete(DeleteBehavior.Cascade);

                entity.HasOne(e => e.FloorHeader)
                    .WithOne(e => e.Construction)
                    .HasForeignKey<FloorHeaderConstruction>(e => e.FloorHeaderId)
                    .OnDelete(DeleteBehavior.Cascade);
            });

            modelBuilder.Entity<FloorHeaderMeasurements>(entity =>
            {
                entity.ToTable("FloorHeaderMeasurements", "envelope");
                entity.HasKey(e => e.Id);
                entity.Property(e => e.Height).HasPrecision(18, 2);
                entity.Property(e => e.Perimeter).HasPrecision(18, 2);

                entity.HasOne(e => e.FloorHeader)
                    .WithOne(e => e.Measurements)
                    .HasForeignKey<FloorHeaderMeasurements>(e => e.FloorHeaderId)
                    .OnDelete(DeleteBehavior.Cascade);
            });

            // ROOM configurations
            modelBuilder.Entity<Room>(entity =>
            {
                entity.ToTable("Rooms", "envelope");
                entity.HasKey(e => e.Id);
                entity.Property(e => e.Label).IsRequired().HasMaxLength(100);
                entity.Property(e => e.EnergyUpgradeId).IsRequired(false);

                entity.HasOne(e => e.Construction)
                    .WithOne(e => e.Room)
                    .HasForeignKey<RoomConstruction>(e => e.RoomId)
                    .OnDelete(DeleteBehavior.Cascade);

                entity.HasOne(e => e.Measurements)
                    .WithOne(e => e.Room)
                    .HasForeignKey<RoomMeasurements>(e => e.RoomId)
                    .OnDelete(DeleteBehavior.Cascade);
            });

            modelBuilder.Entity<RoomConstruction>(entity =>
            {
                entity.ToTable("RoomConstructions", "envelope");
                entity.HasKey(e => e.Id);

                // Configure owned entities for resource properties
                entity.OwnsOne(e => e.Type, type =>
                {
                    type.Property(t => t.Code).HasColumnName("TypeCode").HasMaxLength(10);
                    type.Property(t => t.English).HasColumnName("TypeEnglish").HasMaxLength(100);
                    type.Property(t => t.French).HasColumnName("TypeFrench").HasMaxLength(100);
                    type.Property(t => t.IsUserSpecified).HasColumnName("TypeIsUserSpecified");
                });

                entity.OwnsOne(e => e.Floor, floor =>
                {
                    floor.Property(f => f.Code).HasColumnName("FloorCode").HasMaxLength(10);
                    floor.Property(f => f.English).HasColumnName("FloorEnglish").HasMaxLength(100);
                    floor.Property(f => f.French).HasColumnName("FloorFrench").HasMaxLength(100);
                    floor.Property(f => f.IsUserSpecified).HasColumnName("FloorIsUserSpecified");
                });

                entity.OwnsOne(e => e.FoundationBelow, foundation =>
                {
                    foundation.Property(f => f.Code).HasColumnName("FoundationBelowCode").HasMaxLength(10);
                    foundation.Property(f => f.EnglishText).HasColumnName("FoundationBelowEnglish").HasMaxLength(100);
                    foundation.Property(f => f.FrenchText).HasColumnName("FoundationBelowFrench").HasMaxLength(100);
                });

                entity.HasOne(e => e.Room)
                    .WithOne(e => e.Construction)
                    .HasForeignKey<RoomConstruction>(e => e.RoomId)
                    .OnDelete(DeleteBehavior.Cascade);
            });

            modelBuilder.Entity<RoomMeasurements>(entity =>
            {
                entity.ToTable("RoomMeasurements", "envelope");
                entity.HasKey(e => e.Id);
                entity.Property(e => e.Height).HasPrecision(18, 2);
                entity.Property(e => e.Width).HasPrecision(18, 2);
                entity.Property(e => e.Depth).HasPrecision(18, 2);
                entity.Property(e => e.Perimeter).HasPrecision(18, 2);
                entity.Property(e => e.Area).HasPrecision(18, 2);

                entity.HasOne(e => e.Room)
                    .WithOne(e => e.Measurements)
                    .HasForeignKey<RoomMeasurements>(e => e.RoomId)
                    .OnDelete(DeleteBehavior.Cascade);
            });
        }
    }
}