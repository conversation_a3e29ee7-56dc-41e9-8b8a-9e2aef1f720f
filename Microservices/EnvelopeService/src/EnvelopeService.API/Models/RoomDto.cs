using System;
using System.ComponentModel.DataAnnotations;

namespace EnvelopeService.API.Models
{
    /// <summary>
    /// Room DTO following EnvelopeService pattern
    /// </summary>
    public class RoomDto
    {
        public Guid Id { get; set; }

        public Guid HouseId { get; set; }

        // Energy upgrade support
        public Guid? EnergyUpgradeId { get; set; }

        // Component properties
        [Required]
        [StringLength(100)]
        public string Label { get; set; } = string.Empty;
        
        // Navigation properties
        public RoomConstructionDto Construction { get; set; } = new RoomConstructionDto();
        public RoomMeasurementsDto Measurements { get; set; } = new RoomMeasurementsDto();
    }
}
