using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using EnvelopeService.Core.Interfaces;
using EnvelopeService.Core.Models;
using Microsoft.Extensions.Logging;

namespace EnvelopeService.Core.Services
{
    public class DoorService : IDoorService
    {
        private readonly IDoorRepository _doorRepository;
        private readonly ILogger<DoorService> _logger;
        
        public DoorService(IDoorRepository doorRepository, ILogger<DoorService> logger)
        {
            _doorRepository = doorRepository ?? throw new ArgumentNullException(nameof(doorRepository));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        }
        
        public async Task<List<Door>> GetDoorsByHouseIdAsync(Guid houseId)
        {
            _logger.LogInformation("Getting doors for house with ID: {HouseId}", houseId);
            return await _doorRepository.GetDoorsByHouseIdAsync(houseId);
        }

        public async Task<List<Door>> GetDoorsByEnergyUpgradeIdAsync(Guid energyUpgradeId)
        {
            _logger.LogInformation("Getting doors for energy upgrade ID: {EnergyUpgradeId}", energyUpgradeId);
            return await _doorRepository.GetDoorsByEnergyUpgradeIdAsync(energyUpgradeId);
        }
        
        public async Task<Door> GetDoorByIdAsync(Guid id)
        {
            _logger.LogInformation("Getting door with ID: {DoorId}", id);
            return await _doorRepository.GetDoorByIdAsync(id);
        }

        public async Task<List<Door>> GetAllDoorsAsync()
        {
            _logger.LogInformation("Getting all doors");
            return await _doorRepository.GetAllDoorsAsync();
        }
        
        public async Task<Door> AddDoorAsync(Door door)
        {
            _logger.LogInformation("Adding new door for house with ID: {HouseId}", door.HouseId);
            
            // Ensure the door has an ID
            if (door.Id == Guid.Empty)
            {
                door.Id = Guid.NewGuid();
            }

            // Set DoorType based on DoorType.Code from DTO
            if (door.Construction?.Type != null && !string.IsNullOrEmpty(door.Construction.Type.Code))
            {
                var typeCode = door.Construction.Type.Code;
                var isUserSpecified = door.Construction.Type.IsUserSpecified;
                var value = door.Construction.Type.Value;

                // Find the door type by code and preserve IsUserSpecified value
                var doorType = GetDoorTypeByCode(typeCode);

                door.Construction.Type = new DoorTypes
                {
                    Code = typeCode,
                    English = doorType.English,
                    French = doorType.French,
                    Value = value,
                    IsUserSpecified = isUserSpecified
                };
            }
            
            // Initialize objects with defaults if needed
            if (door.Construction == null)
            {
                door.Construction = new DoorConstruction();
                door.Construction.SetDefaults();
            }
            
            if (door.Measurements == null)
            {
                door.Measurements = new DoorMeasurements();
                door.Measurements.SetDefaults();
            }
            
            // Setup DoorConstruction properly
            if (door.Construction != null)
            {
                door.Construction.DoorId = door.Id;
                if (door.Construction.Id == Guid.Empty)
                {
                    door.Construction.Id = Guid.NewGuid();
                }
            }
            
            // Ensure Measurements is properly set up
            if (door.Measurements != null)
            {
                door.Measurements.DoorId = door.Id;
                if (door.Measurements.Id == Guid.Empty)
                {
                    door.Measurements.Id = Guid.NewGuid();
                }
            }
            
            return await _doorRepository.AddDoorAsync(door);
        }
        
        public async Task<Door> UpdateDoorAsync(Door door)
        {
            _logger.LogInformation("Updating door with ID: {DoorId}", door.Id);

            var exists = await _doorRepository.ExistsAsync(door.Id);
            if (!exists)
            {
                _logger.LogWarning("Door with ID: {DoorId} not found for update", door.Id);
                return null;
            }

            // Set DoorType based on DoorType.Code from DTO before updating
            if (door.Construction?.Type != null && !string.IsNullOrEmpty(door.Construction.Type.Code))
            {
                var typeCode = door.Construction.Type.Code;
                var isUserSpecified = door.Construction.Type.IsUserSpecified;
                var value = door.Construction.Type.Value;

                // Find the door type by code and preserve IsUserSpecified value
                var doorType = GetDoorTypeByCode(typeCode);

                door.Construction.Type = new DoorTypes
                {
                    Code = typeCode,
                    English = doorType.English,
                    French = doorType.French,
                    Value = value,
                    IsUserSpecified = isUserSpecified
                };
            }

            return await _doorRepository.UpdateDoorAsync(door);
        }
        
        public async Task DeleteDoorAsync(Guid id)
        {
            _logger.LogInformation("Deleting door with ID: {DoorId}", id);
            await _doorRepository.DeleteDoorAsync(id);
        }

        public async Task<Door> DuplicateDoorForEnergyUpgradeAsync(Door baseDoor, Guid energyUpgradeId)
        {
            _logger.LogInformation("Duplicating door {BaseDoorId} for energy upgrade {EnergyUpgradeId}",
                baseDoor.Id, energyUpgradeId);

            // Create a deep copy using the copy constructor - this copies ALL properties automatically
            var duplicatedDoor = new Door(baseDoor);

            // Override the specific properties for the energy upgrade
            duplicatedDoor.Id = Guid.NewGuid(); // New ID for the duplicate
            duplicatedDoor.EnergyUpgradeId = energyUpgradeId; // Link to energy upgrade
            duplicatedDoor.Label = baseDoor.Label + " (Energy Upgrade)"; // Distinguish the label

            // Update the foreign key references to point to the new door ID
            if (duplicatedDoor.Construction != null)
            {
                duplicatedDoor.Construction.Id = Guid.NewGuid();
                duplicatedDoor.Construction.DoorId = duplicatedDoor.Id;
            }

            if (duplicatedDoor.Measurements != null)
            {
                duplicatedDoor.Measurements.Id = Guid.NewGuid();
                duplicatedDoor.Measurements.DoorId = duplicatedDoor.Id;
            }

            // Add the duplicated door to the database
            var result = await AddDoorAsync(duplicatedDoor);

            _logger.LogInformation("Successfully duplicated door {BaseDoorId} as {NewDoorId} for energy upgrade {EnergyUpgradeId}",
                baseDoor.Id, result.Id, energyUpgradeId);

            return result;
        }

        private static DoorTypes GetDoorTypeByCode(string code)
        {
            return code switch
            {
                "1" => DoorTypes.WoodHollowCore,
                "2" => DoorTypes.SolidWood,
                "3" => DoorTypes.SteelFibreglassCore,
                "4" => DoorTypes.SteelPolystyreneCore,
                "5" => DoorTypes.SteelMediumDensitySprayFoamCore,
                "6" => DoorTypes.FibreglassPolystyreneCore,
                "7" => DoorTypes.FibreglassMediumDensitySprayFoamCore,
                "8" => DoorTypes.UserSpecified,
                _ => DoorTypes.WoodHollowCore
            };
        }
    }
}
