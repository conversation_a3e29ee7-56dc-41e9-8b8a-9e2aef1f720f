using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using EnvelopeService.Core.Interfaces;
using EnvelopeService.Core.Models;
using Microsoft.Extensions.Logging;

namespace EnvelopeService.Core.Services
{
    public class RoomService : IRoomService
    {
        private readonly IRoomRepository _roomRepository;
        private readonly ILogger<RoomService> _logger;
        
        public RoomService(IRoomRepository roomRepository, ILogger<RoomService> logger)
        {
            _roomRepository = roomRepository ?? throw new ArgumentNullException(nameof(roomRepository));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        }
        
        public async Task<List<Room>> GetRoomsByHouseIdAsync(Guid houseId)
        {
            _logger.LogInformation("Getting rooms for house with ID: {HouseId}", houseId);
            return await _roomRepository.GetRoomsByHouseIdAsync(houseId);
        }

        public async Task<List<Room>> GetRoomsByEnergyUpgradeIdAsync(Guid energyUpgradeId)
        {
            _logger.LogInformation("Getting rooms for energy upgrade ID: {EnergyUpgradeId}", energyUpgradeId);
            return await _roomRepository.GetRoomsByEnergyUpgradeIdAsync(energyUpgradeId);
        }
        
        public async Task<Room> GetRoomByIdAsync(Guid id)
        {
            _logger.LogInformation("Getting room with ID: {RoomId}", id);
            return await _roomRepository.GetRoomByIdAsync(id);
        }

        public async Task<List<Room>> GetAllRoomsAsync()
        {
            _logger.LogInformation("Getting all rooms");
            return await _roomRepository.GetAllRoomsAsync();
        }
        
        public async Task<Room> AddRoomAsync(Room room)
        {
            _logger.LogInformation("Adding new room for house with ID: {HouseId}", room.HouseId);
            
            // Ensure the room has an ID
            if (room.Id == Guid.Empty)
            {
                room.Id = Guid.NewGuid();
            }

            // Set RoomType based on RoomType.Code from DTO
            if (room.Construction?.Type != null && !string.IsNullOrEmpty(room.Construction.Type.Code))
            {
                var typeCode = room.Construction.Type.Code;
                var isUserSpecified = room.Construction.Type.IsUserSpecified;

                // Find the room type by code and preserve IsUserSpecified value
                var roomType = GetRoomTypeByCode(typeCode);

                room.Construction.Type = new RoomTypes
                {
                    Code = typeCode,
                    English = roomType.English,
                    French = roomType.French,
                    IsUserSpecified = isUserSpecified
                };
            }

            // Set RoomFloor based on RoomFloor.Code from DTO
            if (room.Construction?.Floor != null && !string.IsNullOrEmpty(room.Construction.Floor.Code))
            {
                var floorCode = room.Construction.Floor.Code;
                var isUserSpecified = room.Construction.Floor.IsUserSpecified;

                // Find the room floor by code and preserve IsUserSpecified value
                var roomFloor = GetRoomFloorByCode(floorCode);

                room.Construction.Floor = new RoomFloors
                {
                    Code = floorCode,
                    English = roomFloor.English,
                    French = roomFloor.French,
                    IsUserSpecified = isUserSpecified
                };
            }



            // Initialize objects with defaults if needed
            if (room.Construction == null)
            {
                room.Construction = new RoomConstruction();
                room.Construction.SetDefaults();
            }
            
            if (room.Measurements == null)
            {
                room.Measurements = new RoomMeasurements();
                room.Measurements.SetDefaults();
            }
            
            // Setup RoomConstruction properly
            if (room.Construction != null)
            {
                room.Construction.RoomId = room.Id;
                if (room.Construction.Id == Guid.Empty)
                {
                    room.Construction.Id = Guid.NewGuid();
                }
            }
            
            // Ensure Measurements is properly set up
            if (room.Measurements != null)
            {
                room.Measurements.RoomId = room.Id;
                if (room.Measurements.Id == Guid.Empty)
                {
                    room.Measurements.Id = Guid.NewGuid();
                }
            }
            
            return await _roomRepository.AddRoomAsync(room);
        }
        
        public async Task<Room> UpdateRoomAsync(Room room)
        {
            _logger.LogInformation("Updating room with ID: {RoomId}", room.Id);

            var exists = await _roomRepository.ExistsAsync(room.Id);
            if (!exists)
            {
                _logger.LogWarning("Room with ID: {RoomId} not found for update", room.Id);
                return null;
            }

            // Set RoomType based on RoomType.Code from DTO before updating
            if (room.Construction?.Type != null && !string.IsNullOrEmpty(room.Construction.Type.Code))
            {
                var typeCode = room.Construction.Type.Code;
                var isUserSpecified = room.Construction.Type.IsUserSpecified;

                // Find the room type by code and preserve IsUserSpecified value
                var roomType = GetRoomTypeByCode(typeCode);

                room.Construction.Type = new RoomTypes
                {
                    Code = typeCode,
                    English = roomType.English,
                    French = roomType.French,
                    IsUserSpecified = isUserSpecified
                };
            }

            // Set RoomFloor based on RoomFloor.Code from DTO before updating
            if (room.Construction?.Floor != null && !string.IsNullOrEmpty(room.Construction.Floor.Code))
            {
                var floorCode = room.Construction.Floor.Code;
                var isUserSpecified = room.Construction.Floor.IsUserSpecified;

                // Find the room floor by code and preserve IsUserSpecified value
                var roomFloor = GetRoomFloorByCode(floorCode);

                room.Construction.Floor = new RoomFloors
                {
                    Code = floorCode,
                    English = roomFloor.English,
                    French = roomFloor.French,
                    IsUserSpecified = isUserSpecified
                };
            }



            return await _roomRepository.UpdateRoomAsync(room);
        }
        
        public async Task DeleteRoomAsync(Guid id)
        {
            _logger.LogInformation("Deleting room with ID: {RoomId}", id);
            await _roomRepository.DeleteRoomAsync(id);
        }

        public async Task<Room> DuplicateRoomForEnergyUpgradeAsync(Room baseRoom, Guid energyUpgradeId)
        {
            _logger.LogInformation("Duplicating room {BaseRoomId} for energy upgrade {EnergyUpgradeId}",
                baseRoom.Id, energyUpgradeId);

            // Create a deep copy using the copy constructor - this copies ALL properties automatically
            var duplicatedRoom = new Room(baseRoom);

            // Override the specific properties for the energy upgrade
            duplicatedRoom.Id = Guid.NewGuid(); // New ID for the duplicate
            duplicatedRoom.EnergyUpgradeId = energyUpgradeId; // Link to energy upgrade
            duplicatedRoom.Label = baseRoom.Label + " (Energy Upgrade)"; // Distinguish the label

            // Update the foreign key references to point to the new room ID
            if (duplicatedRoom.Construction != null)
            {
                duplicatedRoom.Construction.Id = Guid.NewGuid();
                duplicatedRoom.Construction.RoomId = duplicatedRoom.Id;
            }

            if (duplicatedRoom.Measurements != null)
            {
                duplicatedRoom.Measurements.Id = Guid.NewGuid();
                duplicatedRoom.Measurements.RoomId = duplicatedRoom.Id;
            }

            // Save the duplicated room
            var result = await _roomRepository.AddRoomAsync(duplicatedRoom);

            _logger.LogInformation("Successfully duplicated room {BaseRoomId} as {NewRoomId} for energy upgrade {EnergyUpgradeId}",
                baseRoom.Id, result.Id, energyUpgradeId);

            return result;
        }

        private static RoomTypes GetRoomTypeByCode(string code)
        {
            return code switch
            {
                "1" => RoomTypes.Kitchen,
                "2" => RoomTypes.LivingRoom,
                "3" => RoomTypes.DiningRoom,
                "4" => RoomTypes.Bedroom,
                "5" => RoomTypes.Bathroom,
                "6" => RoomTypes.UtilityRoom,
                "7" => RoomTypes.Other,
                _ => RoomTypes.Kitchen
            };
        }

        private static RoomFloors GetRoomFloorByCode(string code)
        {
            return code switch
            {
                "1" => RoomFloors.GroundFloor,
                "2" => RoomFloors.SecondFloor,
                "3" => RoomFloors.ThirdFloor,
                _ => RoomFloors.GroundFloor
            };
        }


    }
}
