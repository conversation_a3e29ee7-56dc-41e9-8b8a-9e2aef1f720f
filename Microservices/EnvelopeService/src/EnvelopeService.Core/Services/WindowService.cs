using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using EnvelopeService.Core.Interfaces;
using EnvelopeService.Core.Models;
using Microsoft.Extensions.Logging;

namespace EnvelopeService.Core.Services
{
    public class WindowService : IWindowService
    {
        private readonly IWindowRepository _windowRepository;
        private readonly ILogger<WindowService> _logger;
        
        public WindowService(IWindowRepository windowRepository, ILogger<WindowService> logger)
        {
            _windowRepository = windowRepository ?? throw new ArgumentNullException(nameof(windowRepository));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        }
        
        public async Task<List<Window>> GetWindowsByHouseIdAsync(Guid houseId)
        {
            _logger.LogInformation("Getting windows for house with ID: {HouseId}", houseId);
            return await _windowRepository.GetWindowsByHouseIdAsync(houseId);
        }

        public async Task<List<Window>> GetWindowsByEnergyUpgradeIdAsync(Guid energyUpgradeId)
        {
            _logger.LogInformation("Getting windows for energy upgrade ID: {EnergyUpgradeId}", energyUpgradeId);
            return await _windowRepository.GetWindowsByEnergyUpgradeIdAsync(energyUpgradeId);
        }
        
        public async Task<Window> GetWindowByIdAsync(Guid id)
        {
            _logger.LogInformation("Getting window with ID: {WindowId}", id);
            return await _windowRepository.GetWindowByIdAsync(id);
        }

        public async Task<List<Window>> GetAllWindowsAsync()
        {
            _logger.LogInformation("Getting all windows");
            return await _windowRepository.GetAllWindowsAsync();
        }
        
        public async Task<Window> AddWindowAsync(Window window)
        {
            _logger.LogInformation("Adding new window for house with ID: {HouseId}", window.HouseId);
            
            // Ensure the window has an ID
            if (window.Id == Guid.Empty)
            {
                window.Id = Guid.NewGuid();
            }

            // Set FacingDirection based on FacingDirection.Code from DTO
            if (window.FacingDirection != null && !string.IsNullOrEmpty(window.FacingDirection.Code))
            {
                var directionCode = window.FacingDirection.Code;
                var isUserSpecified = window.FacingDirection.IsUserSpecified;

                // Find the direction by code and preserve IsUserSpecified value
                var windowDirection = GetWindowDirectionByCode(directionCode);

                window.FacingDirection = new WindowDirections
                {
                    Code = directionCode,
                    English = windowDirection.English,
                    French = windowDirection.French,
                    IsUserSpecified = isUserSpecified
                };
            }

            // Set WindowTilt based on Tilt.Code from DTO
            if (window.Measurements?.Tilt != null && !string.IsNullOrEmpty(window.Measurements.Tilt.Code))
            {
                var tiltCode = window.Measurements.Tilt.Code;
                var isUserSpecified = window.Measurements.Tilt.IsUserSpecified;
                var value = window.Measurements.Tilt.Value;

                // Find the tilt by code and preserve IsUserSpecified value
                var windowTilt = GetWindowTiltByCode(tiltCode);

                window.Measurements.Tilt = new WindowTilts
                {
                    Code = tiltCode,
                    English = windowTilt.English,
                    French = windowTilt.French,
                    Value = value,
                    IsUserSpecified = isUserSpecified
                };
            }
            
            // Initialize objects with defaults if needed
            if (window.Construction == null)
            {
                window.Construction = new WindowConstruction();
                window.Construction.SetDefaults();
            }
            
            if (window.Measurements == null)
            {
                window.Measurements = new WindowMeasurements();
                window.Measurements.SetDefaults();
            }

            if (window.Shading == null)
            {
                window.Shading = new WindowShading();
                window.Shading.SetDefaults();
            }

            if (window.EnergyStar == null)
            {
                window.EnergyStar = new EnergyStar();
                window.EnergyStar.SetDefaults();
            }
            
            // Setup WindowConstruction properly
            if (window.Construction != null)
            {
                window.Construction.WindowId = window.Id;
                if (window.Construction.Id == Guid.Empty)
                {
                    window.Construction.Id = Guid.NewGuid();
                }
                
                // Ensure Type CodeReference has ID
                if (window.Construction.Type != null && window.Construction.Type.Id == Guid.Empty)
                {
                    window.Construction.Type.Id = Guid.NewGuid();
                }
            }
            
            // Ensure Measurements is properly set up
            if (window.Measurements != null)
            {
                window.Measurements.WindowId = window.Id;
                if (window.Measurements.Id == Guid.Empty)
                {
                    window.Measurements.Id = Guid.NewGuid();
                }
            }

            // Ensure Shading is properly set up
            if (window.Shading != null)
            {
                window.Shading.WindowId = window.Id;
                if (window.Shading.Id == Guid.Empty)
                {
                    window.Shading.Id = Guid.NewGuid();
                }
            }

            // Ensure EnergyStar is properly set up
            if (window.EnergyStar != null)
            {
                window.EnergyStar.WindowId = window.Id;
                if (window.EnergyStar.Id == Guid.Empty)
                {
                    window.EnergyStar.Id = Guid.NewGuid();
                }
            }
            
            return await _windowRepository.AddWindowAsync(window);
        }
        
        public async Task<Window> UpdateWindowAsync(Window window)
        {
            _logger.LogInformation("Updating window with ID: {WindowId}", window.Id);

            var exists = await _windowRepository.ExistsAsync(window.Id);
            if (!exists)
            {
                _logger.LogWarning("Window with ID: {WindowId} not found for update", window.Id);
                return null;
            }

            // Set FacingDirection based on FacingDirection.Code from DTO before updating
            if (window.FacingDirection != null && !string.IsNullOrEmpty(window.FacingDirection.Code))
            {
                var directionCode = window.FacingDirection.Code;
                var isUserSpecified = window.FacingDirection.IsUserSpecified;

                // Find the direction by code and preserve IsUserSpecified value
                var windowDirection = GetWindowDirectionByCode(directionCode);

                window.FacingDirection = new WindowDirections
                {
                    Code = directionCode,
                    English = windowDirection.English,
                    French = windowDirection.French,
                    IsUserSpecified = isUserSpecified
                };
            }

            // Set WindowTilt based on Tilt.Code from DTO before updating
            if (window.Measurements?.Tilt != null && !string.IsNullOrEmpty(window.Measurements.Tilt.Code))
            {
                var tiltCode = window.Measurements.Tilt.Code;
                var isUserSpecified = window.Measurements.Tilt.IsUserSpecified;
                var value = window.Measurements.Tilt.Value;

                // Find the tilt by code and preserve IsUserSpecified value
                var windowTilt = GetWindowTiltByCode(tiltCode);

                window.Measurements.Tilt = new WindowTilts
                {
                    Code = tiltCode,
                    English = windowTilt.English,
                    French = windowTilt.French,
                    Value = value,
                    IsUserSpecified = isUserSpecified
                };
            }

            return await _windowRepository.UpdateWindowAsync(window);
        }

        public async Task DeleteWindowAsync(Guid id)
        {
            _logger.LogInformation("Deleting window with ID: {WindowId}", id);
            await _windowRepository.DeleteWindowAsync(id);
        }

        private static WindowDirections GetWindowDirectionByCode(string code)
        {
            return code switch
            {
                "1" => WindowDirections.South,
                "2" => WindowDirections.Southeast,
                "3" => WindowDirections.East,
                "4" => WindowDirections.Northeast,
                "5" => WindowDirections.North,
                "6" => WindowDirections.Northwest,
                "7" => WindowDirections.West,
                "8" => WindowDirections.Southwest,
                _ => WindowDirections.South
            };
        }

        public async Task<Window> DuplicateWindowForEnergyUpgradeAsync(Window baseWindow, Guid energyUpgradeId)
        {
            _logger.LogInformation("Duplicating window {BaseWindowId} for energy upgrade {EnergyUpgradeId}",
                baseWindow.Id, energyUpgradeId);

            // Create a deep copy using the copy constructor - this copies ALL properties automatically
            var duplicatedWindow = new Window(baseWindow);

            // Override the specific properties for the energy upgrade
            duplicatedWindow.Id = Guid.NewGuid(); // New ID for the duplicate
            duplicatedWindow.EnergyUpgradeId = energyUpgradeId; // Link to energy upgrade
            duplicatedWindow.Label = baseWindow.Label + " (Energy Upgrade)"; // Distinguish the label

            // Update the foreign key references to point to the new window ID
            if (duplicatedWindow.Construction != null)
            {
                duplicatedWindow.Construction.Id = Guid.NewGuid();
                duplicatedWindow.Construction.WindowId = duplicatedWindow.Id;
            }

            if (duplicatedWindow.Measurements != null)
            {
                duplicatedWindow.Measurements.Id = Guid.NewGuid();
                duplicatedWindow.Measurements.WindowId = duplicatedWindow.Id;
            }

            if (duplicatedWindow.Shading != null)
            {
                duplicatedWindow.Shading.Id = Guid.NewGuid();
                duplicatedWindow.Shading.WindowId = duplicatedWindow.Id;
            }

            if (duplicatedWindow.EnergyStar != null)
            {
                duplicatedWindow.EnergyStar.Id = Guid.NewGuid();
                duplicatedWindow.EnergyStar.WindowId = duplicatedWindow.Id;
            }

            // Add the duplicated window to the database
            var result = await AddWindowAsync(duplicatedWindow);

            _logger.LogInformation("Successfully duplicated window {BaseWindowId} as {NewWindowId} for energy upgrade {EnergyUpgradeId}",
                baseWindow.Id, result.Id, energyUpgradeId);

            return result;
        }

        private static WindowTilts GetWindowTiltByCode(string code)
        {
            return code switch
            {
                "1" => WindowTilts.Vertical,
                "2" => WindowTilts.Horizontal,
                "3" => WindowTilts.SameAsRoof,
                "4" => WindowTilts.UserSpecified,
                _ => WindowTilts.Vertical
            };
        }
    }
}
