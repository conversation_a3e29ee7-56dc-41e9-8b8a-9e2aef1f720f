using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace EnvelopeService.Core.Models
{
    /// <summary>
    /// Door construction - standalone without inheritance
    /// </summary>
    public class DoorConstruction
    {
        public Guid Id { get; set; }
        
        public Guid DoorId { get; set; }

        // EnergyStar certification
        public bool EnergyStar { get; set; }

        // Door type - using CodeReference (following Construction pattern)
        public DoorTypes Type { get; set; } = DoorTypes.WoodHollowCore;

        // Navigation property
        [ForeignKey("DoorId")]
        public virtual Door Door { get; set; } = null!;

        public DoorConstruction()
        {
            SetDefaults();
        }

        public DoorConstruction(DoorConstruction toCopy)
        {
            Id = toCopy.Id;
            DoorId = toCopy.DoorId;
            EnergyStar = toCopy.EnergyStar;
            Type = toCopy.Type ?? DoorTypes.WoodHollowCore;
        }

        public void SetDefaults()
        {
            EnergyStar = false;
            // Set default door type to Wood hollow core
            Type = DoorTypes.WoodHollowCore;
        }
    }
}
