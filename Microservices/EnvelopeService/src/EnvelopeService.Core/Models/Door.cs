using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace EnvelopeService.Core.Models
{
    /// <summary>
    /// Door model - standalone without inheritance
    /// </summary>
    public class Door
    {
        public Guid Id { get; set; }

        public Guid HouseId { get; set; }

        public Guid? EnergyUpgradeId { get; set; }

        // Component properties (previously inherited)
        [Required]
        [StringLength(100)]
        public string Label { get; set; } = string.Empty;
        
        // Door-specific properties
        public bool AdjacentEnclosedSpace { get; set; }

        // RValue property that gets value from Construction.Type.Value (following original pattern)
        [NotMapped]
        public decimal RValue
        {
            get { return Construction?.Type?.Value ?? 0m; }
        }

        // Navigation properties
        public DoorConstruction Construction { get; set; } = new DoorConstruction();
        public DoorMeasurements Measurements { get; set; } = new DoorMeasurements();

        public Door()
        {
            SetDefaults();
        }

        public Door(Door toCopy)
        {
            Id = toCopy.Id;
            HouseId = toCopy.HouseId;
            EnergyUpgradeId = toCopy.EnergyUpgradeId;
            Label = toCopy.Label;
            AdjacentEnclosedSpace = toCopy.AdjacentEnclosedSpace;
            Construction = toCopy.Construction != null ? new DoorConstruction(toCopy.Construction) : new DoorConstruction();
            Measurements = toCopy.Measurements != null ? new DoorMeasurements(toCopy.Measurements) : new DoorMeasurements();
        }

        public void SetDefaults()
        {
            Label = "Door";
            AdjacentEnclosedSpace = false;
            
            Construction = new DoorConstruction();
            Measurements = new DoorMeasurements();
            
            // Initialize related objects with their defaults
            Construction.SetDefaults();
            Measurements.SetDefaults();
        }
    }
}
