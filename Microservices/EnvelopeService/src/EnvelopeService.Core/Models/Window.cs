using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace EnvelopeService.Core.Models
{
    /// <summary>
    /// Window model - standalone without inheritance
    /// </summary>
    public class Window
    {
        public Guid Id { get; set; }

        public Guid HouseId { get; set; }

        public Guid? EnergyUpgradeId { get; set; }

        // Component properties (previously inherited)
        [Required]
        [StringLength(100)]
        public string Label { get; set; } = string.Empty;
        
        // Window-specific properties
        [Column(TypeName = "decimal(18,2)")]
        public decimal Number { get; set; } = 1m;

        [Column(TypeName = "decimal(18,2)")]
        public decimal Er { get; set; }

        [Column(TypeName = "decimal(18,2)")]
        public decimal Shgc { get; set; }

        [Column(TypeName = "decimal(18,2)")]
        public decimal FrameHeight { get; set; }

        [Column(TypeName = "decimal(18,2)")]
        public decimal FrameAreaFraction { get; set; }

        [Column(TypeName = "decimal(18,2)")]
        public decimal EdgeOfGlassFraction { get; set; }

        [Column(TypeName = "decimal(18,2)")]
        public decimal CentreOfGlassFraction { get; set; }

        public bool AdjacentEnclosedSpace { get; set; }
        
        // Facing Direction - using WindowDirections resource
        public WindowDirections FacingDirection { get; set; } = WindowDirections.South;
        
        // Navigation properties
        public WindowConstruction Construction { get; set; } = new WindowConstruction();
        public WindowMeasurements Measurements { get; set; } = new WindowMeasurements();
        public WindowShading Shading { get; set; } = new WindowShading();
        public EnergyStar EnergyStar { get; set; } = new EnergyStar();

        public Window()
        {
            SetDefaults();
        }

        public Window(Window toCopy)
        {
            Id = toCopy.Id;
            HouseId = toCopy.HouseId;
            EnergyUpgradeId = toCopy.EnergyUpgradeId;
            Label = toCopy.Label;
            Number = toCopy.Number;
            Er = toCopy.Er;
            Shgc = toCopy.Shgc;
            FrameHeight = toCopy.FrameHeight;
            FrameAreaFraction = toCopy.FrameAreaFraction;
            EdgeOfGlassFraction = toCopy.EdgeOfGlassFraction;
            CentreOfGlassFraction = toCopy.CentreOfGlassFraction;
            AdjacentEnclosedSpace = toCopy.AdjacentEnclosedSpace;
            FacingDirection = toCopy.FacingDirection ?? WindowDirections.South;
            Construction = toCopy.Construction != null ? new WindowConstruction(toCopy.Construction) : new WindowConstruction();
            Measurements = toCopy.Measurements != null ? new WindowMeasurements(toCopy.Measurements) : new WindowMeasurements();
            Shading = toCopy.Shading != null ? new WindowShading(toCopy.Shading) : new WindowShading();
            EnergyStar = toCopy.EnergyStar != null ? new EnergyStar(toCopy.EnergyStar) : new EnergyStar();
        }

        public void SetDefaults()
        {
            Label = "Window";
            Number = 1m;
            Er = 0m;
            Shgc = 0m;
            FrameHeight = 0m;
            FrameAreaFraction = 0m;
            EdgeOfGlassFraction = 0m;
            CentreOfGlassFraction = 0m;
            AdjacentEnclosedSpace = false;
            
            FacingDirection = WindowDirections.South;
            Construction = new WindowConstruction();
            Measurements = new WindowMeasurements();
            Shading = new WindowShading();
            EnergyStar = new EnergyStar();
            
            // Initialize related objects with their defaults
            Construction.SetDefaults();
            Measurements.SetDefaults();
            Shading.SetDefaults();
            EnergyStar.SetDefaults();
        }
    }
}
