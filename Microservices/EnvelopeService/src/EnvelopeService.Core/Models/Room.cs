using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace EnvelopeService.Core.Models
{
    /// <summary>
    /// Room model following EnvelopeService pattern
    /// Mirrors Hot/HouseFileLibrary/Components/RoomComponents/Room.cs
    /// </summary>
    public class Room
    {
        public Guid Id { get; set; }

        public Guid HouseId { get; set; }

        // Energy upgrade support
        public Guid? EnergyUpgradeId { get; set; }

        // Component properties (following the pattern from other envelope components)
        [Required]
        [StringLength(100)]
        public string Label { get; set; } = string.Empty;

        // Navigation properties
        public RoomConstruction Construction { get; set; } = new RoomConstruction();
        public RoomMeasurements Measurements { get; set; } = new RoomMeasurements();

        public Room()
        {
            SetDefaults();
        }

        public Room(Room toCopy)
        {
            Id = toCopy.Id;
            HouseId = toCopy.HouseId;
            EnergyUpgradeId = toCopy.EnergyUpgradeId;
            Label = toCopy.Label;
            Construction = toCopy.Construction != null ? new RoomConstruction(toCopy.Construction) : new RoomConstruction();
            Measurements = toCopy.Measurements != null ? new RoomMeasurements(toCopy.Measurements) : new RoomMeasurements();
        }

        public void SetDefaults()
        {
            Label = "Room";
            
            Construction = new RoomConstruction();
            Measurements = new RoomMeasurements();
            
            // Initialize related objects with their defaults
            Construction.SetDefaults();
            Measurements.SetDefaults();
        }
    }
}
