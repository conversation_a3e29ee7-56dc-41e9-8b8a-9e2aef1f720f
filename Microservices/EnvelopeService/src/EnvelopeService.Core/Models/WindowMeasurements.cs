using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace EnvelopeService.Core.Models
{
    public class WindowMeasurements
    {
        public Guid Id { get; set; }

        public Guid WindowId { get; set; }

        // Measurement properties
        [Column(TypeName = "decimal(18,2)")]
        public decimal Height { get; set; } = 500m;

        [Column(TypeName = "decimal(18,2)")]
        public decimal Width { get; set; } = 500m;

        [Column(TypeName = "decimal(18,2)")]
        public decimal HeaderHeight { get; set; }

        [Column(TypeName = "decimal(18,2)")]
        public decimal OverhangWidth { get; set; }

        // Window tilt - using WindowTilts resource
        public WindowTilts Tilt { get; set; } = WindowTilts.Vertical;

        // Navigation property
        [ForeignKey("WindowId")]
        public virtual Window Window { get; set; } = null!;

        public WindowMeasurements()
        {
            SetDefaults();
        }

        public WindowMeasurements(WindowMeasurements toCopy)
        {
            Id = toCopy.Id;
            WindowId = toCopy.WindowId;
            Height = toCopy.Height;
            Width = toCopy.Width;
            HeaderHeight = toCopy.HeaderHeight;
            OverhangWidth = toCopy.OverhangWidth;
            Tilt = toCopy.Tilt != null ? new WindowTilts
            {
                Code = toCopy.Tilt.Code,
                English = toCopy.Tilt.English,
                French = toCopy.Tilt.French,
                Value = toCopy.Tilt.Value
            } : WindowTilts.Vertical;
        }

        public void SetDefaults()
        {
            Height = 500m;
            Width = 500m;
            HeaderHeight = 0m;
            OverhangWidth = 0m;
            Tilt = WindowTilts.Vertical;
        }
    }
}
