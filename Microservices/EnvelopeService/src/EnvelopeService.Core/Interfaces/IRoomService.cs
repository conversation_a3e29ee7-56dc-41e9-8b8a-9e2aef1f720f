using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using EnvelopeService.Core.Models;

namespace EnvelopeService.Core.Interfaces
{
    public interface IRoomService
    {
        Task<List<Room>> GetRoomsByHouseIdAsync(Guid houseId);
        Task<List<Room>> GetRoomsByEnergyUpgradeIdAsync(Guid energyUpgradeId);
        Task<Room> GetRoomByIdAsync(Guid id);
        Task<Room> AddRoomAsync(Room room);
        Task<Room> UpdateRoomAsync(Room room);
        Task DeleteRoomAsync(Guid id);
        Task<List<Room>> GetAllRoomsAsync();
        Task<Room> DuplicateRoomForEnergyUpgradeAsync(Room baseRoom, Guid energyUpgradeId);
    }
}
