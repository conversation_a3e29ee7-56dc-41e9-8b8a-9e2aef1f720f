using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using VentilationService.Core.Interfaces;
using VentilationService.Core.Models;
using VentilationService.Infrastructure.Data;
using Microsoft.Extensions.Logging;

namespace VentilationService.Infrastructure.Repositories
{
    /// <summary>
    /// Repository implementation for ventilation operations
    /// Following the Wall repository approach (simple property updates, direct SaveChanges)
    /// </summary>
    public class VentilationRepository : IVentilationRepository
    {
        private readonly VentilationDbContext _context;
        private readonly ILogger<VentilationRepository> _logger;

        public VentilationRepository(VentilationDbContext context, ILogger<VentilationRepository> logger)
        {
            _context = context;
            _logger = logger;
        }

        public async Task<IEnumerable<Ventilation>> GetAllVentilationSystemsAsync()
        {
            _logger.LogInformation("Getting all ventilation systems from repository");

            return await _context.Ventilations
                .Include(v => v.Rooms)
                .Include(v => v.Requirements)
                .Include(v => v.SupplyAndExhaust)
                .Include(v => v.WholeHouseHrvList)
                .Include(v => v.WholeHouseDryerList)
                .Include(v => v.WholeHouseVentilators)
                .Include(v => v.SupplementalHrvList)
                .Include(v => v.SupplementalDryerList)
                .Include(v => v.SupplementalVentilators)
                .ToListAsync();
        }

        public async Task<Ventilation?> GetVentilationSystemByIdAsync(Guid id)
        {
            _logger.LogInformation("Getting ventilation system with ID: {Id} from repository", id);

            return await _context.Ventilations
                .AsNoTracking()
                .Include(v => v.Rooms)
                .Include(v => v.Requirements)
                .Include(v => v.SupplyAndExhaust)
                .Include(v => v.WholeHouseHrvList)
                .Include(v => v.WholeHouseDryerList)
                .Include(v => v.WholeHouseVentilators)
                .Include(v => v.SupplementalHrvList)
                .Include(v => v.SupplementalDryerList)
                .Include(v => v.SupplementalVentilators)
                .FirstOrDefaultAsync(v => v.Id == id);
        }

        public async Task<Ventilation?> GetVentilationSystemByHouseIdAsync(Guid houseId)
        {
            _logger.LogInformation("Getting ventilation system for house ID: {HouseId} from repository", houseId);

            return await _context.Ventilations
                .Include(v => v.Rooms)
                .Include(v => v.Requirements)
                .Include(v => v.SupplyAndExhaust)
                .Include(v => v.WholeHouseHrvList)
                .Include(v => v.WholeHouseDryerList)
                .Include(v => v.WholeHouseVentilators)
                .Include(v => v.SupplementalHrvList)
                .Include(v => v.SupplementalDryerList)
                .Include(v => v.SupplementalVentilators)
                .FirstOrDefaultAsync(v => v.HouseId == houseId);
        }

        public async Task<Ventilation> CreateVentilationSystemAsync(Ventilation ventilation)
        {
            _logger.LogInformation("Creating new ventilation system for house ID: {HouseId} in repository", ventilation.HouseId);

            _context.Ventilations.Add(ventilation);
            await _context.SaveChangesAsync();

            return ventilation;
        }

        public async Task UpdateVentilationSystemAsync(Ventilation ventilation)
        {
            _logger.LogInformation("Updating ventilation system with ID: {Id} in repository", ventilation.Id);

            var existingVentilation = await _context.Ventilations
                .Include(v => v.Rooms)
                .Include(v => v.Requirements)
                .Include(v => v.SupplyAndExhaust)
                .Include(v => v.WholeHouseHrvList)
                .Include(v => v.WholeHouseDryerList)
                .Include(v => v.WholeHouseVentilators)
                .Include(v => v.SupplementalHrvList)
                .Include(v => v.SupplementalDryerList)
                .Include(v => v.SupplementalVentilators)
                .FirstOrDefaultAsync(v => v.Id == ventilation.Id);

            if (existingVentilation == null)
            {
                throw new InvalidOperationException($"Ventilation system with ID {ventilation.Id} not found");
            }

            // Update main properties following Wall repository approach
            existingVentilation.HouseId = ventilation.HouseId;
            existingVentilation.Label = ventilation.Label;

            // Update Rooms
            UpdateRooms(existingVentilation, ventilation);

            // Update Requirements
            UpdateRequirements(existingVentilation, ventilation);

            // Update WholeHouseParameters
            UpdateWholeHouseParameters(existingVentilation, ventilation);

            // Update ventilator lists
            await UpdateVentilatorLists(existingVentilation, ventilation);

            await _context.SaveChangesAsync();
        }

        public async Task DeleteVentilationSystemAsync(Guid id)
        {
            _logger.LogInformation("Deleting ventilation system with ID: {Id} from repository", id);

            var ventilation = await _context.Ventilations.FindAsync(id);
            if (ventilation != null)
            {
                _context.Ventilations.Remove(ventilation);
                await _context.SaveChangesAsync();
            }
        }

        public async Task<bool> ExistsAsync(Guid id)
        {
            return await _context.Ventilations.AnyAsync(v => v.Id == id);
        }

        /// <summary>
        /// Update Rooms entity following Wall repository approach
        /// </summary>
        private void UpdateRooms(Ventilation existing, Ventilation updated)
        {
            if (updated.Rooms == null)
            {
                if (existing.Rooms != null)
                {
                    _context.Rooms.Remove(existing.Rooms);
                    existing.Rooms = null;
                }
                return;
            }

            if (existing.Rooms == null)
            {
                existing.Rooms = new Rooms
                {
                    Id = Guid.NewGuid(),
                    VentilationId = existing.Id
                };
                _context.Rooms.Add(existing.Rooms);
            }

            // Update properties manually following Wall approach
            existing.Rooms.Living = updated.Rooms.Living;
            existing.Rooms.Bedrooms = updated.Rooms.Bedrooms;
            existing.Rooms.Bathrooms = updated.Rooms.Bathrooms;
            existing.Rooms.Utility = updated.Rooms.Utility;
            existing.Rooms.OtherHabitable = updated.Rooms.OtherHabitable;

            // Update resource properties
            UpdateResourceProperty(existing.Rooms.VentilationRate, updated.Rooms.VentilationRate);
            UpdateResourceProperty(existing.Rooms.DepressurizationLimit, updated.Rooms.DepressurizationLimit);
        }

        /// <summary>
        /// Update Requirements entity following Wall repository approach
        /// </summary>
        private void UpdateRequirements(Ventilation existing, Ventilation updated)
        {
            if (updated.Requirements == null)
            {
                if (existing.Requirements != null)
                {
                    _context.Requirements.Remove(existing.Requirements);
                    existing.Requirements = null;
                }
                return;
            }

            if (existing.Requirements == null)
            {
                existing.Requirements = new Requirements
                {
                    Id = Guid.NewGuid(),
                    VentilationId = existing.Id
                };
                _context.Requirements.Add(existing.Requirements);
            }

            // Update properties manually following Wall approach
            existing.Requirements.Ach = updated.Requirements.Ach;
            existing.Requirements.Supply = updated.Requirements.Supply;
            existing.Requirements.Exhaust = updated.Requirements.Exhaust;

            // Update resource properties
            UpdateResourceProperty(existing.Requirements.Use, updated.Requirements.Use);
        }

        /// <summary>
        /// Update WholeHouseParameters entity following Wall repository approach
        /// </summary>
        private void UpdateWholeHouseParameters(Ventilation existing, Ventilation updated)
        {
            if (updated.SupplyAndExhaust == null)
            {
                if (existing.SupplyAndExhaust != null)
                {
                    _context.WholeHouseParameters.Remove(existing.SupplyAndExhaust);
                    existing.SupplyAndExhaust = null;
                }
                return;
            }

            if (existing.SupplyAndExhaust == null)
            {
                existing.SupplyAndExhaust = new WholeHouseParameters
                {
                    Id = Guid.NewGuid(),
                    VentilationId = existing.Id
                };
                _context.WholeHouseParameters.Add(existing.SupplyAndExhaust);
            }

            // Update properties manually following Wall approach
            existing.SupplyAndExhaust.TemperatureControlLower = updated.SupplyAndExhaust.TemperatureControlLower;
            existing.SupplyAndExhaust.TemperatureControlUpper = updated.SupplyAndExhaust.TemperatureControlUpper;
            existing.SupplyAndExhaust.HviHrvErvInMurb = updated.SupplyAndExhaust.HviHrvErvInMurb;

            // Update resource properties
            UpdateResourceProperty(existing.SupplyAndExhaust.AirDistributionType, updated.SupplyAndExhaust.AirDistributionType);
            UpdateResourceProperty(existing.SupplyAndExhaust.AirDistributionFanPower, updated.SupplyAndExhaust.AirDistributionFanPower);
            UpdateResourceProperty(existing.SupplyAndExhaust.OperationSchedule, updated.SupplyAndExhaust.OperationSchedule);
        }

        /// <summary>
        /// Update ventilator lists using individual type collections (following HvacService pattern)
        /// </summary>
        private async Task UpdateVentilatorLists(Ventilation existing, Ventilation updated)
        {
            // Update individual type collections using HvacService pattern
            await UpdateVentilatorTypeListAsync<WholeHouseHrv>(existing.Id, updated.WholeHouseHrvList, _context.WholeHouseHrvs);
            await UpdateVentilatorTypeListAsync<WholeHouseDryer>(existing.Id, updated.WholeHouseDryerList, _context.WholeHouseDryers);
            await UpdateVentilatorTypeListAsync<WholeHouseVentilator>(existing.Id, updated.WholeHouseVentilators, _context.WholeHouseVentilators);
            await UpdateVentilatorTypeListAsync<SupplementalHrv>(existing.Id, updated.SupplementalHrvList, _context.SupplementalHrvs);
            await UpdateVentilatorTypeListAsync<SupplementalDryer>(existing.Id, updated.SupplementalDryerList, _context.SupplementalDryers);
            await UpdateVentilatorTypeListAsync<SupplementalVentilator>(existing.Id, updated.SupplementalVentilators, _context.SupplementalVentilators);
        }

        /// <summary>
        /// Update a specific ventilator type list following HvacService pattern
        /// </summary>
        private async Task UpdateVentilatorTypeListAsync<T>(
            Guid ventilationId,
            List<T> updatedList,
            DbSet<T> dbSet) where T : VentilatorObjects
        {
            // Load existing entities from database (not from tracked entities)
            var existingEntities = await dbSet
                .Where(v => v.VentilationId == ventilationId)
                .ToListAsync();

            var newEntities = updatedList ?? new List<T>();

            // Create a mapping of existing entities by ID for quick lookup
            var existingEntitiesDict = existingEntities.ToDictionary(e => e.Id);

            // Track which existing entities should be kept (either updated or unchanged)
            var entitiesToKeep = new HashSet<Guid>();

            // First pass: Update existing entities and track which ones to keep
            foreach (var newEntity in newEntities)
            {
                if (newEntity.Id != Guid.Empty && existingEntitiesDict.ContainsKey(newEntity.Id))
                {
                    entitiesToKeep.Add(newEntity.Id);
                    var existingEntity = existingEntitiesDict[newEntity.Id];

                    // Update existing entity properties
                    UpdateVentilatorProperties(existingEntity, newEntity);

                    // Mark as modified
                    _context.Entry(existingEntity).State = EntityState.Modified;
                }
            }

            // Remove entities that are no longer needed
            var entitiesToRemove = existingEntities.Where(existing =>
                !entitiesToKeep.Contains(existing.Id)).ToList();

            if (entitiesToRemove.Any())
            {
                _context.RemoveRange(entitiesToRemove);
            }

            // Second pass: Add new entities (those with empty GUID or not found in existing)
            foreach (var newEntity in newEntities)
            {
                if (newEntity.Id == Guid.Empty || !existingEntitiesDict.ContainsKey(newEntity.Id))
                {
                    // Set the ventilation ID and generate new ID if needed
                    newEntity.VentilationId = ventilationId;
                    if (newEntity.Id == Guid.Empty)
                    {
                        newEntity.Id = Guid.NewGuid();
                    }

                    _context.Add(newEntity);
                }
            }
        }

        /// <summary>
        /// Update resource property following Wall repository approach
        /// </summary>
        private void UpdateResourceProperty<T>(T? existing, T? updated) where T : ResourceList
        {
            if (updated == null) return;
            if (existing == null) return;

            existing.Code = updated.Code;
            existing.English = updated.English;
            existing.French = updated.French;
            existing.IsUserSpecified = updated.IsUserSpecified;

            // Handle value property for resources that have it
            if (existing is CodeTextAndValue existingValue && updated is CodeTextAndValue updatedValue)
            {
                existingValue.Value = updatedValue.Value;
            }
        }

        /// <summary>
        /// Update ventilator properties using reflection (handles all ventilator types)
        /// </summary>
        private void UpdateVentilatorProperties<T>(T existing, T updated) where T : VentilatorObjects
        {
            // Update base properties
            existing.SupplyFlowrate = updated.SupplyFlowrate;
            existing.ExhaustFlowrate = updated.ExhaustFlowrate;
            existing.FanPower1 = updated.FanPower1;
            existing.IsDefaultFanpower = updated.IsDefaultFanpower;
            existing.IsEnergyStar = updated.IsEnergyStar;
            existing.IsHomeVentilatingInstituteCertified = updated.IsHomeVentilatingInstituteCertified;
            existing.IsSupplemental = updated.IsSupplemental;
            existing.VentilatorType = updated.VentilatorType;
            existing.EquipmentInformation = updated.EquipmentInformation;
            existing.OperationSchedule = updated.OperationSchedule;

            // Update type-specific properties using reflection
            var properties = typeof(T).GetProperties()
                .Where(p => p.CanWrite && p.Name != "Id" && p.Name != "VentilationId")
                .ToList();

            foreach (var property in properties)
            {
                var updatedValue = property.GetValue(updated);
                property.SetValue(existing, updatedValue);
            }
        }

        public async Task<Ventilation?> GetVentilationByEnergyUpgradeIdAsync(Guid energyUpgradeId)
        {
            _logger.LogInformation("Getting ventilation by energy upgrade ID: {EnergyUpgradeId}", energyUpgradeId);

            return await _context.Ventilations
                .Include(v => v.Rooms)
                .Include(v => v.Requirements)
                .Include(v => v.SupplyAndExhaust)
                .Include(v => v.WholeHouseHrvList)
                .Include(v => v.WholeHouseDryerList)
                .Include(v => v.WholeHouseVentilators)
                .Include(v => v.SupplementalHrvList)
                .Include(v => v.SupplementalDryerList)
                .Include(v => v.SupplementalVentilators)
                .FirstOrDefaultAsync(v => v.EnergyUpgradeId == energyUpgradeId);
        }

        public async Task<Ventilation> DuplicateVentilationForEnergyUpgradeAsync(Ventilation baseVentilation, Guid energyUpgradeId)
        {
            _logger.LogInformation("Duplicating ventilation {VentilationId} for energy upgrade {EnergyUpgradeId}",
                baseVentilation.Id, energyUpgradeId);

            // Create a deep copy of the ventilation system
            var duplicatedVentilation = new Ventilation
            {
                Id = Guid.NewGuid(),
                HouseId = baseVentilation.HouseId,
                EnergyUpgradeId = energyUpgradeId,
                Label = baseVentilation.Label
            };

            // Deep copy Rooms
            if (baseVentilation.Rooms != null)
            {
                duplicatedVentilation.Rooms = new Rooms
                {
                    Id = Guid.NewGuid(),
                    VentilationId = duplicatedVentilation.Id,
                    Living = baseVentilation.Rooms.Living,
                    Bedrooms = baseVentilation.Rooms.Bedrooms,
                    Bathrooms = baseVentilation.Rooms.Bathrooms,
                    Utility = baseVentilation.Rooms.Utility,
                    OtherHabitable = baseVentilation.Rooms.OtherHabitable,
                    VentilationRate = baseVentilation.Rooms.VentilationRate,
                    DepressurizationLimit = baseVentilation.Rooms.DepressurizationLimit
                };
            }

            // Deep copy Requirements
            if (baseVentilation.Requirements != null)
            {
                duplicatedVentilation.Requirements = new Requirements
                {
                    Id = Guid.NewGuid(),
                    VentilationId = duplicatedVentilation.Id,
                    Ach = baseVentilation.Requirements.Ach,
                    Supply = baseVentilation.Requirements.Supply,
                    Exhaust = baseVentilation.Requirements.Exhaust,
                    Use = baseVentilation.Requirements.Use
                };
            }

            // Deep copy WholeHouseParameters
            if (baseVentilation.SupplyAndExhaust != null)
            {
                duplicatedVentilation.SupplyAndExhaust = new WholeHouseParameters
                {
                    Id = Guid.NewGuid(),
                    VentilationId = duplicatedVentilation.Id,
                    TemperatureControlLower = baseVentilation.SupplyAndExhaust.TemperatureControlLower,
                    TemperatureControlUpper = baseVentilation.SupplyAndExhaust.TemperatureControlUpper,
                    HviHrvErvInMurb = baseVentilation.SupplyAndExhaust.HviHrvErvInMurb,
                    AirDistributionType = baseVentilation.SupplyAndExhaust.AirDistributionType,
                    AirDistributionFanPower = baseVentilation.SupplyAndExhaust.AirDistributionFanPower,
                    OperationSchedule = baseVentilation.SupplyAndExhaust.OperationSchedule
                };
            }

            // Deep copy ventilator lists using proper copy constructors
            duplicatedVentilation.WholeHouseHrvList = baseVentilation.WholeHouseHrvList?.Select(hrv =>
                new WholeHouseHrv(hrv) { Id = Guid.NewGuid(), VentilationId = duplicatedVentilation.Id }).ToList() ?? new List<WholeHouseHrv>();

            duplicatedVentilation.WholeHouseDryerList = baseVentilation.WholeHouseDryerList?.Select(dryer =>
                new WholeHouseDryer(dryer) { Id = Guid.NewGuid(), VentilationId = duplicatedVentilation.Id }).ToList() ?? new List<WholeHouseDryer>();

            duplicatedVentilation.WholeHouseVentilators = baseVentilation.WholeHouseVentilators?.Select(vent =>
                new WholeHouseVentilator(vent) { Id = Guid.NewGuid(), VentilationId = duplicatedVentilation.Id }).ToList() ?? new List<WholeHouseVentilator>();

            duplicatedVentilation.SupplementalHrvList = baseVentilation.SupplementalHrvList?.Select(hrv =>
                new SupplementalHrv(hrv) { Id = Guid.NewGuid(), VentilationId = duplicatedVentilation.Id }).ToList() ?? new List<SupplementalHrv>();

            duplicatedVentilation.SupplementalDryerList = baseVentilation.SupplementalDryerList?.Select(dryer =>
                new SupplementalDryer(dryer) { Id = Guid.NewGuid(), VentilationId = duplicatedVentilation.Id }).ToList() ?? new List<SupplementalDryer>();

            duplicatedVentilation.SupplementalVentilators = baseVentilation.SupplementalVentilators?.Select(vent =>
                new SupplementalVentilator(vent) { Id = Guid.NewGuid(), VentilationId = duplicatedVentilation.Id }).ToList() ?? new List<SupplementalVentilator>();

            // Add to context and save
            _context.Ventilations.Add(duplicatedVentilation);
            await _context.SaveChangesAsync();

            return duplicatedVentilation;
        }


    }
}