using Microsoft.EntityFrameworkCore;
using VentilationService.Core.Models;

namespace VentilationService.Infrastructure.Data
{
    public class VentilationDbContext : DbContext
    {
        public VentilationDbContext(DbContextOptions<VentilationDbContext> options)
            : base(options)
        {
        }

        // Main entities
        public DbSet<Ventilation> Ventilations { get; set; }
        public DbSet<Rooms> Rooms { get; set; }
        public DbSet<Requirements> Requirements { get; set; }
        public DbSet<WholeHouseParameters> WholeHouseParameters { get; set; }

        // Ventilator entities (separate tables for each type following HvacService pattern)
        public DbSet<WholeHouseHrv> WholeHouseHrvs { get; set; }
        public DbSet<WholeHouseDryer> WholeHouseDryers { get; set; }
        public DbSet<WholeHouseVentilator> WholeHouseVentilators { get; set; }
        public DbSet<SupplementalHrv> SupplementalHrvs { get; set; }
        public DbSet<SupplementalDryer> SupplementalDryers { get; set; }
        public DbSet<SupplementalVentilator> SupplementalVentilators { get; set; }

        // Resource entities are now owned entities, not separate tables

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            base.OnModelCreating(modelBuilder);

            // Configure schema (following BaseLoadService pattern)
            modelBuilder.HasDefaultSchema("ventilation");

            // Ignore the abstract base class to prevent EF from trying to create inheritance mapping
            modelBuilder.Ignore<VentilatorObjects>();

            ConfigureEntities(modelBuilder);
        }

        private void ConfigureEntities(ModelBuilder modelBuilder)
        {
            // Ventilation configuration
            modelBuilder.Entity<Ventilation>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.HouseId);
                entity.Property(e => e.EnergyUpgradeId).IsRequired(false);
                entity.Property(e => e.Label).HasMaxLength(100);

                // Configure related components (like HotWater Primary/Secondary)
                entity.HasOne(e => e.Rooms)
                      .WithOne(r => r.Ventilation)
                      .HasForeignKey<Rooms>(r => r.VentilationId)
                      .OnDelete(DeleteBehavior.Cascade);

                entity.HasOne(e => e.Requirements)
                      .WithOne(r => r.Ventilation)
                      .HasForeignKey<Requirements>(r => r.VentilationId)
                      .OnDelete(DeleteBehavior.Cascade);

                entity.HasOne(e => e.SupplyAndExhaust)
                      .WithOne(s => s.Ventilation)
                      .HasForeignKey<WholeHouseParameters>(s => s.VentilationId)
                      .OnDelete(DeleteBehavior.Cascade);

                // Configure relationships to individual ventilator type collections
                entity.HasMany(e => e.WholeHouseHrvList)
                      .WithOne(v => v.Ventilation)
                      .HasForeignKey(v => v.VentilationId)
                      .OnDelete(DeleteBehavior.Cascade);

                entity.HasMany(e => e.WholeHouseDryerList)
                      .WithOne(v => v.Ventilation)
                      .HasForeignKey(v => v.VentilationId)
                      .OnDelete(DeleteBehavior.Cascade);

                entity.HasMany(e => e.WholeHouseVentilators)
                      .WithOne(v => v.Ventilation)
                      .HasForeignKey(v => v.VentilationId)
                      .OnDelete(DeleteBehavior.Cascade);

                entity.HasMany(e => e.SupplementalHrvList)
                      .WithOne(v => v.Ventilation)
                      .HasForeignKey(v => v.VentilationId)
                      .OnDelete(DeleteBehavior.Cascade);

                entity.HasMany(e => e.SupplementalDryerList)
                      .WithOne(v => v.Ventilation)
                      .HasForeignKey(v => v.VentilationId)
                      .OnDelete(DeleteBehavior.Cascade);

                entity.HasMany(e => e.SupplementalVentilators)
                      .WithOne(v => v.Ventilation)
                      .HasForeignKey(v => v.VentilationId)
                      .OnDelete(DeleteBehavior.Cascade);

                // Ignore the polymorphic list properties from EF mapping
                // These are computed properties that aggregate the individual type collections
                entity.Ignore(e => e.WholeHouseVentilatorList);
                entity.Ignore(e => e.SupplementalVentilatorList);
            });

            // Rooms configuration
            modelBuilder.Entity<Rooms>(entity =>
            {
                entity.HasKey(e => e.Id);

                // Configure room count properties
                entity.Property(e => e.Living);
                entity.Property(e => e.Bedrooms);
                entity.Property(e => e.Bathrooms);
                entity.Property(e => e.Utility);
                entity.Property(e => e.OtherHabitable);

                // Resource entities as owned entities (following HotWaterService pattern)
                entity.OwnsOne(e => e.VentilationRate, vr =>
                {
                    vr.Property(v => v.Code).HasMaxLength(10);
                    vr.Property(v => v.English).HasMaxLength(100);
                    vr.Property(v => v.French).HasMaxLength(100);
                    vr.Property(v => v.IsUserSpecified);
                    vr.Property(v => v.Value).HasPrecision(18, 2);
                });

                entity.OwnsOne(e => e.DepressurizationLimit, dl =>
                {
                    dl.Property(d => d.Code).HasMaxLength(10);
                    dl.Property(d => d.English).HasMaxLength(100);
                    dl.Property(d => d.French).HasMaxLength(100);
                    dl.Property(d => d.IsUserSpecified);
                });
            });

            // Requirements configuration
            modelBuilder.Entity<Requirements>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.Ach).HasPrecision(18, 2);
                entity.Property(e => e.Supply).HasPrecision(18, 2);
                entity.Property(e => e.Exhaust).HasPrecision(18, 2);

                // Resource entities as owned entities (following HotWaterService pattern)
                entity.OwnsOne(e => e.Use, u =>
                {
                    u.Property(us => us.Code).HasMaxLength(10);
                    u.Property(us => us.English).HasMaxLength(100);
                    u.Property(us => us.French).HasMaxLength(100);
                    u.Property(us => us.IsUserSpecified);
                });
            });

            // WholeHouseParameters configuration
            modelBuilder.Entity<WholeHouseParameters>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.TemperatureControlLower).HasPrecision(18, 2);
                entity.Property(e => e.TemperatureControlUpper).HasPrecision(18, 2);
                entity.Property(e => e.HviHrvErvInMurb);

                // Resource entities as owned entities (following HotWaterService pattern)
                entity.OwnsOne(e => e.AirDistributionType, adt =>
                {
                    adt.Property(a => a.Code).HasMaxLength(10);
                    adt.Property(a => a.English).HasMaxLength(100);
                    adt.Property(a => a.French).HasMaxLength(100);
                    adt.Property(a => a.IsUserSpecified);
                });

                entity.OwnsOne(e => e.AirDistributionFanPower, adfp =>
                {
                    adfp.Property(a => a.Code).HasMaxLength(10);
                    adfp.Property(a => a.English).HasMaxLength(100);
                    adfp.Property(a => a.French).HasMaxLength(100);
                    adfp.Property(a => a.IsUserSpecified);
                    adfp.Property(a => a.Value).HasPrecision(18, 2);
                });

                entity.OwnsOne(e => e.OperationSchedule, os =>
                {
                    os.Property(o => o.Code).HasMaxLength(10);
                    os.Property(o => o.English).HasMaxLength(100);
                    os.Property(o => o.French).HasMaxLength(100);
                    os.Property(o => o.IsUserSpecified);
                });
            });

            // WholeHouseHrv configuration
            modelBuilder.Entity<WholeHouseHrv>(entity =>
            {
                entity.HasKey(e => e.Id);

                // Base class properties from VentilatorObjects
                entity.Property(e => e.SupplyFlowrate).HasPrecision(18, 2);
                entity.Property(e => e.ExhaustFlowrate).HasPrecision(18, 2);
                entity.Property(e => e.FanPower1).HasPrecision(18, 2);
                entity.Property(e => e.IsDefaultFanpower);
                entity.Property(e => e.IsEnergyStar);
                entity.Property(e => e.IsHomeVentilatingInstituteCertified);
                entity.Property(e => e.IsSupplemental);

                // HRV-specific properties
                entity.Property(e => e.TemperatureCondition1).HasPrecision(18, 2);
                entity.Property(e => e.TemperatureCondition2).HasPrecision(18, 2);
                entity.Property(e => e.FanPower2).HasPrecision(18, 2);
                entity.Property(e => e.Efficiency1).HasPrecision(18, 2);
                entity.Property(e => e.Efficiency2).HasPrecision(18, 2);
                entity.Property(e => e.PreheaterCapacity).HasPrecision(18, 2);
                entity.Property(e => e.LowTempVentReduction).HasPrecision(18, 2);
                entity.Property(e => e.CoolingEfficiency).HasPrecision(18, 2);

                // Owned entities (following HotWaterService pattern)
                entity.OwnsOne(e => e.EquipmentInformation);

                // Resource entities as owned entities
                entity.OwnsOne(e => e.VentilatorType, vt =>
                {
                    vt.Property(v => v.Code).HasMaxLength(10);
                    vt.Property(v => v.English).HasMaxLength(100);
                    vt.Property(v => v.French).HasMaxLength(100);
                    vt.Property(v => v.IsUserSpecified);
                });

                entity.OwnsOne(e => e.OperationSchedule, os =>
                {
                    os.Property(o => o.Code).HasMaxLength(10);
                    os.Property(o => o.English).HasMaxLength(100);
                    os.Property(o => o.French).HasMaxLength(100);
                    os.Property(o => o.IsUserSpecified);
                });

                // Configure HrvDucts as owned entity with nested Supply and Exhaust
                entity.OwnsOne(e => e.HrvDucts, hd =>
                {
                    hd.OwnsOne(h => h.Supply, s =>
                    {
                        s.Property(p => p.Length).HasPrecision(18, 2);
                        s.Property(p => p.Diameter).HasPrecision(18, 2);
                        s.Property(p => p.Insulation).HasPrecision(18, 2);

                        s.OwnsOne(sp => sp.Location, l =>
                        {
                            l.Property(p => p.Code).HasMaxLength(10);
                            l.Property(p => p.English).HasMaxLength(100);
                            l.Property(p => p.French).HasMaxLength(100);
                            l.Property(p => p.IsUserSpecified);
                        });

                        s.OwnsOne(sp => sp.Type, t =>
                        {
                            t.Property(p => p.Code).HasMaxLength(10);
                            t.Property(p => p.English).HasMaxLength(100);
                            t.Property(p => p.French).HasMaxLength(100);
                            t.Property(p => p.IsUserSpecified);
                        });

                        s.OwnsOne(sp => sp.Sealing, se =>
                        {
                            se.Property(p => p.Code).HasMaxLength(10);
                            se.Property(p => p.English).HasMaxLength(100);
                            se.Property(p => p.French).HasMaxLength(100);
                            se.Property(p => p.IsUserSpecified);
                        });
                    });

                    hd.OwnsOne(h => h.Exhaust, e =>
                    {
                        e.Property(p => p.Length).HasPrecision(18, 2);
                        e.Property(p => p.Diameter).HasPrecision(18, 2);
                        e.Property(p => p.Insulation).HasPrecision(18, 2);

                        e.OwnsOne(ep => ep.Location, l =>
                        {
                            l.Property(p => p.Code).HasMaxLength(10);
                            l.Property(p => p.English).HasMaxLength(100);
                            l.Property(p => p.French).HasMaxLength(100);
                            l.Property(p => p.IsUserSpecified);
                        });

                        e.OwnsOne(ep => ep.Type, t =>
                        {
                            t.Property(p => p.Code).HasMaxLength(10);
                            t.Property(p => p.English).HasMaxLength(100);
                            t.Property(p => p.French).HasMaxLength(100);
                            t.Property(p => p.IsUserSpecified);
                        });

                        e.OwnsOne(ep => ep.Sealing, se =>
                        {
                            se.Property(p => p.Code).HasMaxLength(10);
                            se.Property(p => p.English).HasMaxLength(100);
                            se.Property(p => p.French).HasMaxLength(100);
                            se.Property(p => p.IsUserSpecified);
                        });
                    });
                });
            });

            // SupplementalHrv configuration
            modelBuilder.Entity<SupplementalHrv>(entity =>
            {
                entity.HasKey(e => e.Id);

                // Base class properties from VentilatorObjects
                entity.Property(e => e.SupplyFlowrate).HasPrecision(18, 2);
                entity.Property(e => e.ExhaustFlowrate).HasPrecision(18, 2);
                entity.Property(e => e.FanPower1).HasPrecision(18, 2);
                entity.Property(e => e.IsDefaultFanpower);
                entity.Property(e => e.IsEnergyStar);
                entity.Property(e => e.IsHomeVentilatingInstituteCertified);
                entity.Property(e => e.IsSupplemental);

                // HRV-specific properties
                entity.Property(e => e.TemperatureCondition1).HasPrecision(18, 2);
                entity.Property(e => e.TemperatureCondition2).HasPrecision(18, 2);
                entity.Property(e => e.FanPower2).HasPrecision(18, 2);
                entity.Property(e => e.Efficiency1).HasPrecision(18, 2);
                entity.Property(e => e.Efficiency2).HasPrecision(18, 2);
                entity.Property(e => e.PreheaterCapacity).HasPrecision(18, 2);
                entity.Property(e => e.LowTempVentReduction).HasPrecision(18, 2);
                entity.Property(e => e.CoolingEfficiency).HasPrecision(18, 2);

                // Owned entities (following HotWaterService pattern)
                entity.OwnsOne(e => e.EquipmentInformation);

                // Resource entities as owned entities
                entity.OwnsOne(e => e.VentilatorType, vt =>
                {
                    vt.Property(v => v.Code).HasMaxLength(10);
                    vt.Property(v => v.English).HasMaxLength(100);
                    vt.Property(v => v.French).HasMaxLength(100);
                    vt.Property(v => v.IsUserSpecified);
                });

                entity.OwnsOne(e => e.OperationSchedule, os =>
                {
                    os.Property(o => o.Code).HasMaxLength(10);
                    os.Property(o => o.English).HasMaxLength(100);
                    os.Property(o => o.French).HasMaxLength(100);
                    os.Property(o => o.IsUserSpecified);
                });

                // Configure HrvDucts as owned entity with nested Supply and Exhaust
                entity.OwnsOne(e => e.HrvDucts, hd =>
                {
                    hd.OwnsOne(h => h.Supply, s =>
                    {
                        s.Property(p => p.Length).HasPrecision(18, 2);
                        s.Property(p => p.Diameter).HasPrecision(18, 2);
                        s.Property(p => p.Insulation).HasPrecision(18, 2);

                        s.OwnsOne(sp => sp.Location, l =>
                        {
                            l.Property(p => p.Code).HasMaxLength(10);
                            l.Property(p => p.English).HasMaxLength(100);
                            l.Property(p => p.French).HasMaxLength(100);
                            l.Property(p => p.IsUserSpecified);
                        });

                        s.OwnsOne(sp => sp.Type, t =>
                        {
                            t.Property(p => p.Code).HasMaxLength(10);
                            t.Property(p => p.English).HasMaxLength(100);
                            t.Property(p => p.French).HasMaxLength(100);
                            t.Property(p => p.IsUserSpecified);
                        });

                        s.OwnsOne(sp => sp.Sealing, se =>
                        {
                            se.Property(p => p.Code).HasMaxLength(10);
                            se.Property(p => p.English).HasMaxLength(100);
                            se.Property(p => p.French).HasMaxLength(100);
                            se.Property(p => p.IsUserSpecified);
                        });
                    });

                    hd.OwnsOne(h => h.Exhaust, e =>
                    {
                        e.Property(p => p.Length).HasPrecision(18, 2);
                        e.Property(p => p.Diameter).HasPrecision(18, 2);
                        e.Property(p => p.Insulation).HasPrecision(18, 2);

                        e.OwnsOne(ep => ep.Location, l =>
                        {
                            l.Property(p => p.Code).HasMaxLength(10);
                            l.Property(p => p.English).HasMaxLength(100);
                            l.Property(p => p.French).HasMaxLength(100);
                            l.Property(p => p.IsUserSpecified);
                        });

                        e.OwnsOne(ep => ep.Type, t =>
                        {
                            t.Property(p => p.Code).HasMaxLength(10);
                            t.Property(p => p.English).HasMaxLength(100);
                            t.Property(p => p.French).HasMaxLength(100);
                            t.Property(p => p.IsUserSpecified);
                        });

                        e.OwnsOne(ep => ep.Sealing, se =>
                        {
                            se.Property(p => p.Code).HasMaxLength(10);
                            se.Property(p => p.English).HasMaxLength(100);
                            se.Property(p => p.French).HasMaxLength(100);
                            se.Property(p => p.IsUserSpecified);
                        });
                    });
                });
            });

            // WholeHouseDryer configuration
            modelBuilder.Entity<WholeHouseDryer>(entity =>
            {
                entity.HasKey(e => e.Id);

                // Base class properties from VentilatorObjects
                entity.Property(e => e.SupplyFlowrate).HasPrecision(18, 2);
                entity.Property(e => e.ExhaustFlowrate).HasPrecision(18, 2);
                entity.Property(e => e.FanPower1).HasPrecision(18, 2);
                entity.Property(e => e.IsDefaultFanpower);
                entity.Property(e => e.IsEnergyStar);
                entity.Property(e => e.IsHomeVentilatingInstituteCertified);
                entity.Property(e => e.IsSupplemental);

                // Owned entities (following HotWaterService pattern)
                entity.OwnsOne(e => e.EquipmentInformation);

                // Resource entities as owned entities
                entity.OwnsOne(e => e.VentilatorType, vt =>
                {
                    vt.Property(v => v.Code).HasMaxLength(10);
                    vt.Property(v => v.English).HasMaxLength(100);
                    vt.Property(v => v.French).HasMaxLength(100);
                    vt.Property(v => v.IsUserSpecified);
                });

                entity.OwnsOne(e => e.OperationSchedule, os =>
                {
                    os.Property(o => o.Code).HasMaxLength(10);
                    os.Property(o => o.English).HasMaxLength(100);
                    os.Property(o => o.French).HasMaxLength(100);
                    os.Property(o => o.IsUserSpecified);
                });

                entity.OwnsOne(e => e.Exhaust, ex =>
                {
                    ex.Property(e => e.Code).HasMaxLength(10);
                    ex.Property(e => e.English).HasMaxLength(100);
                    ex.Property(e => e.French).HasMaxLength(100);
                    ex.Property(e => e.IsUserSpecified);
                });
            });

            // WholeHouseVentilator configuration
            modelBuilder.Entity<WholeHouseVentilator>(entity =>
            {
                entity.HasKey(e => e.Id);

                // Base class properties from VentilatorObjects
                entity.Property(e => e.SupplyFlowrate).HasPrecision(18, 2);
                entity.Property(e => e.ExhaustFlowrate).HasPrecision(18, 2);
                entity.Property(e => e.FanPower1).HasPrecision(18, 2);
                entity.Property(e => e.IsDefaultFanpower);
                entity.Property(e => e.IsEnergyStar);
                entity.Property(e => e.IsHomeVentilatingInstituteCertified);
                entity.Property(e => e.IsSupplemental);

                // Owned entities (following HotWaterService pattern)
                entity.OwnsOne(e => e.EquipmentInformation);

                // Resource entities as owned entities
                entity.OwnsOne(e => e.VentilatorType, vt =>
                {
                    vt.Property(v => v.Code).HasMaxLength(10);
                    vt.Property(v => v.English).HasMaxLength(100);
                    vt.Property(v => v.French).HasMaxLength(100);
                    vt.Property(v => v.IsUserSpecified);
                });

                entity.OwnsOne(e => e.OperationSchedule, os =>
                {
                    os.Property(o => o.Code).HasMaxLength(10);
                    os.Property(o => o.English).HasMaxLength(100);
                    os.Property(o => o.French).HasMaxLength(100);
                    os.Property(o => o.IsUserSpecified);
                });
            });

            // SupplementalDryer configuration
            modelBuilder.Entity<SupplementalDryer>(entity =>
            {
                entity.HasKey(e => e.Id);

                // Base class properties from VentilatorObjects
                entity.Property(e => e.SupplyFlowrate).HasPrecision(18, 2);
                entity.Property(e => e.ExhaustFlowrate).HasPrecision(18, 2);
                entity.Property(e => e.FanPower1).HasPrecision(18, 2);
                entity.Property(e => e.IsDefaultFanpower);
                entity.Property(e => e.IsEnergyStar);
                entity.Property(e => e.IsHomeVentilatingInstituteCertified);
                entity.Property(e => e.IsSupplemental);

                // Owned entities (following HotWaterService pattern)
                entity.OwnsOne(e => e.EquipmentInformation);

                // Resource entities as owned entities
                entity.OwnsOne(e => e.VentilatorType, vt =>
                {
                    vt.Property(v => v.Code).HasMaxLength(10);
                    vt.Property(v => v.English).HasMaxLength(100);
                    vt.Property(v => v.French).HasMaxLength(100);
                    vt.Property(v => v.IsUserSpecified);
                });

                entity.OwnsOne(e => e.OperationSchedule, os =>
                {
                    os.Property(o => o.Code).HasMaxLength(10);
                    os.Property(o => o.English).HasMaxLength(100);
                    os.Property(o => o.French).HasMaxLength(100);
                    os.Property(o => o.IsUserSpecified);
                });

                entity.OwnsOne(e => e.Exhaust, ex =>
                {
                    ex.Property(e => e.Code).HasMaxLength(10);
                    ex.Property(e => e.English).HasMaxLength(100);
                    ex.Property(e => e.French).HasMaxLength(100);
                    ex.Property(e => e.IsUserSpecified);
                });
            });

            // SupplementalVentilator configuration
            modelBuilder.Entity<SupplementalVentilator>(entity =>
            {
                entity.HasKey(e => e.Id);

                // Base class properties from VentilatorObjects
                entity.Property(e => e.SupplyFlowrate).HasPrecision(18, 2);
                entity.Property(e => e.ExhaustFlowrate).HasPrecision(18, 2);
                entity.Property(e => e.FanPower1).HasPrecision(18, 2);
                entity.Property(e => e.IsDefaultFanpower);
                entity.Property(e => e.IsEnergyStar);
                entity.Property(e => e.IsHomeVentilatingInstituteCertified);
                entity.Property(e => e.IsSupplemental);

                // Owned entities (following HotWaterService pattern)
                entity.OwnsOne(e => e.EquipmentInformation);

                // Resource entities as owned entities
                entity.OwnsOne(e => e.VentilatorType, vt =>
                {
                    vt.Property(v => v.Code).HasMaxLength(10);
                    vt.Property(v => v.English).HasMaxLength(100);
                    vt.Property(v => v.French).HasMaxLength(100);
                    vt.Property(v => v.IsUserSpecified);
                });

                entity.OwnsOne(e => e.OperationSchedule, os =>
                {
                    os.Property(o => o.Code).HasMaxLength(10);
                    os.Property(o => o.English).HasMaxLength(100);
                    os.Property(o => o.French).HasMaxLength(100);
                    os.Property(o => o.IsUserSpecified);
                });
            });
        }
    }
}