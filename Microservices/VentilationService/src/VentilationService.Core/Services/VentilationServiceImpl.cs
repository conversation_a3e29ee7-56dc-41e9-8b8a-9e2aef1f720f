using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using VentilationService.Core.Interfaces;
using VentilationService.Core.Models;
using Microsoft.Extensions.Logging;

namespace VentilationService.Core.Services
{
    public class VentilationServiceImpl : IVentilationService
    {
        private readonly IVentilationRepository _repository;
        private readonly ILogger<VentilationServiceImpl> _logger;

        public VentilationServiceImpl(
            IVentilationRepository repository,
            ILogger<VentilationServiceImpl> logger)
        {
            _repository = repository;
            _logger = logger;
        }

        public async Task<IEnumerable<Ventilation>> GetAllVentilationSystemsAsync()
        {
            _logger.LogInformation("Getting all ventilation systems from service");
            return await _repository.GetAllVentilationSystemsAsync();
        }

        public async Task<Ventilation?> GetVentilationSystemByHouseIdAsync(Guid houseId)
        {
            _logger.LogInformation("Getting ventilation system for house ID: {HouseId} from service", houseId);
            return await _repository.GetVentilationSystemByHouseIdAsync(houseId);
        }

        public async Task<Ventilation?> GetVentilationSystemByIdAsync(Guid id)
        {
            _logger.LogInformation("Getting ventilation system with ID: {Id} from service", id);
            return await _repository.GetVentilationSystemByIdAsync(id);
        }

        public async Task<Ventilation> CreateVentilationSystemAsync(Ventilation ventilation)
        {
            _logger.LogInformation("Creating new ventilation system for house ID: {HouseId} from service", ventilation.HouseId);

            // Ensure the ventilation system has an ID
            if (ventilation.Id == Guid.Empty)
            {
                ventilation.Id = Guid.NewGuid();
            }

            // Process resource codes to populate text fields
            ProcessResourceCodes(ventilation);

            // Set up relationships
            SetupVentilationRelationships(ventilation);

            return await _repository.CreateVentilationSystemAsync(ventilation);
        }

        public async Task UpdateVentilationSystemAsync(Ventilation ventilation)
        {
            _logger.LogInformation("Updating ventilation system with ID: {Id} from service", ventilation.Id);

            // Check if the ventilation system exists
            var existingVentilation = await _repository.GetVentilationSystemByIdAsync(ventilation.Id);
            if (existingVentilation == null)
            {
                throw new InvalidOperationException($"Ventilation system with ID {ventilation.Id} not found");
            }

            // Process resource codes to populate text fields
            ProcessResourceCodes(ventilation);

            // Set up relationships
            SetupVentilationRelationships(ventilation);

            await _repository.UpdateVentilationSystemAsync(ventilation);
        }

        public async Task DeleteVentilationSystemAsync(Guid id)
        {
            _logger.LogInformation("Deleting ventilation system with ID: {Id} from service", id);
            await _repository.DeleteVentilationSystemAsync(id);
        }

        public async Task<Ventilation> SetDefaultVentilationSystemAsync(Ventilation ventilation)
        {
            _logger.LogInformation("Setting default ventilation system configuration");

            // Set default values for ventilation system
            SetDefaultValues(ventilation);

            return ventilation;
        }

        public async Task<Ventilation> CalculateVentilationSystemAsync(Ventilation ventilation, bool restoreDefaults = false)
        {
            _logger.LogInformation("Calculating ventilation system performance");

            if (restoreDefaults)
            {
                SetDefaultValues(ventilation);
            }

            // Calculate minimum ventilation rate
            if (ventilation.Rooms != null)
            {
                CalculateMinimumVentilationRate(ventilation.Rooms);
            }

            return ventilation;
        }

        /// <summary>
        /// Process resource codes to populate English and French text values
        /// </summary>
        private void ProcessResourceCodes(Ventilation ventilation)
        {
            // Process Rooms resource codes
            if (ventilation.Rooms != null)
            {
                ProcessRoomsResourceCodes(ventilation.Rooms);
            }

            // Process Requirements resource codes
            if (ventilation.Requirements != null)
            {
                ProcessRequirementsResourceCodes(ventilation.Requirements);
            }

            // Process WholeHouseParameters resource codes
            if (ventilation.SupplyAndExhaust != null)
            {
                ProcessWholeHouseParametersResourceCodes(ventilation.SupplyAndExhaust);
            }

            // Process ventilator resource codes
            ProcessVentilatorResourceCodes(ventilation);
        }

        /// <summary>
        /// Set up entity relationships and IDs
        /// </summary>
        private void SetupVentilationRelationships(Ventilation ventilation)
        {
            // Set up Rooms relationships
            if (ventilation.Rooms != null)
            {
                if (ventilation.Rooms.Id == Guid.Empty)
                {
                    ventilation.Rooms.Id = Guid.NewGuid();
                }
                ventilation.Rooms.VentilationId = ventilation.Id;
            }

            // Set up Requirements relationships
            if (ventilation.Requirements != null)
            {
                if (ventilation.Requirements.Id == Guid.Empty)
                {
                    ventilation.Requirements.Id = Guid.NewGuid();
                }
                ventilation.Requirements.VentilationId = ventilation.Id;
            }

            // Set up WholeHouseParameters relationships
            if (ventilation.SupplyAndExhaust != null)
            {
                if (ventilation.SupplyAndExhaust.Id == Guid.Empty)
                {
                    ventilation.SupplyAndExhaust.Id = Guid.NewGuid();
                }
                ventilation.SupplyAndExhaust.VentilationId = ventilation.Id;
            }

            // Set up ventilator relationships
            SetupVentilatorRelationships(ventilation);
        }

        /// <summary>
        /// Process resource codes for Rooms
        /// </summary>
        private void ProcessRoomsResourceCodes(Rooms rooms)
        {
            // Process VentilationRate resource codes
            if (rooms.VentilationRate != null && !string.IsNullOrEmpty(rooms.VentilationRate.Code))
            {
                // Populate text from resource definitions
                // This would typically lookup from H2kResources
                PopulateResourceText(rooms.VentilationRate);
            }

            // Process DepressurizationLimit resource codes
            if (rooms.DepressurizationLimit != null && !string.IsNullOrEmpty(rooms.DepressurizationLimit.Code))
            {
                PopulateResourceText(rooms.DepressurizationLimit);
            }
        }

        /// <summary>
        /// Process resource codes for Requirements
        /// </summary>
        private void ProcessRequirementsResourceCodes(Requirements requirements)
        {
            // Process Use resource codes
            if (requirements.Use != null && !string.IsNullOrEmpty(requirements.Use.Code))
            {
                PopulateResourceText(requirements.Use);
            }
        }

        /// <summary>
        /// Process resource codes for WholeHouseParameters
        /// </summary>
        private void ProcessWholeHouseParametersResourceCodes(WholeHouseParameters parameters)
        {
            // Process AirDistributionType resource codes
            if (parameters.AirDistributionType != null && !string.IsNullOrEmpty(parameters.AirDistributionType.Code))
            {
                PopulateResourceText(parameters.AirDistributionType);
            }

            // Process AirDistributionFanPower resource codes
            if (parameters.AirDistributionFanPower != null && !string.IsNullOrEmpty(parameters.AirDistributionFanPower.Code))
            {
                PopulateResourceText(parameters.AirDistributionFanPower);
            }

            // Process OperationSchedule resource codes
            if (parameters.OperationSchedule != null && !string.IsNullOrEmpty(parameters.OperationSchedule.Code))
            {
                PopulateResourceText(parameters.OperationSchedule);
            }
        }

        /// <summary>
        /// Process resource codes for all ventilators
        /// </summary>
        private void ProcessVentilatorResourceCodes(Ventilation ventilation)
        {
            // Process ventilator resource codes using polymorphic lists
            ProcessVentilatorList(ventilation.WholeHouseVentilatorList);
            ProcessVentilatorList(ventilation.SupplementalVentilatorList);
        }

        /// <summary>
        /// Process resource codes for a list of ventilators
        /// </summary>
        private void ProcessVentilatorList<T>(IEnumerable<T> ventilators) where T : VentilatorObjects
        {
            if (ventilators == null) return;

            foreach (var ventilator in ventilators)
            {
                // Process VentilatorType resource codes
                if (ventilator.VentilatorType != null && !string.IsNullOrEmpty(ventilator.VentilatorType.Code))
                {
                    PopulateResourceText(ventilator.VentilatorType);
                }

                // Process OperationSchedule resource codes
                if (ventilator.OperationSchedule != null && !string.IsNullOrEmpty(ventilator.OperationSchedule.Code))
                {
                    PopulateResourceText(ventilator.OperationSchedule);
                }

                // Process specific ventilator type resources
                switch (ventilator)
                {
                    case WholeHouseHrv hrv:
                        ProcessHrvResourceCodes(hrv);
                        break;
                    case SupplementalHrv suppHrv:
                        ProcessHrvResourceCodes(suppHrv);
                        break;
                    case WholeHouseDryer dryer:
                        ProcessDryerResourceCodes(dryer);
                        break;
                    case SupplementalDryer suppDryer:
                        ProcessDryerResourceCodes(suppDryer);
                        break;
                }
            }
        }

        /// <summary>
        /// Process resource codes for HRV ventilators
        /// </summary>
        private void ProcessHrvResourceCodes(WholeHouseHrv hrv)
        {
            if (hrv.HrvDucts != null)
            {
                ProcessHrvDuctResourceCodes(hrv.HrvDucts.Supply);
                ProcessHrvDuctResourceCodes(hrv.HrvDucts.Exhaust);
            }
        }

        /// <summary>
        /// Process resource codes for Supplemental HRV ventilators
        /// </summary>
        private void ProcessHrvResourceCodes(SupplementalHrv hrv)
        {
            if (hrv.HrvDucts != null)
            {
                ProcessHrvDuctResourceCodes(hrv.HrvDucts.Supply);
                ProcessHrvDuctResourceCodes(hrv.HrvDucts.Exhaust);
            }
        }

        /// <summary>
        /// Process resource codes for Dryer ventilators
        /// </summary>
        private void ProcessDryerResourceCodes(WholeHouseDryer dryer)
        {
            if (dryer.Exhaust != null && !string.IsNullOrEmpty(dryer.Exhaust.Code))
            {
                PopulateResourceText(dryer.Exhaust);
            }
        }

        /// <summary>
        /// Process resource codes for Supplemental Dryer ventilators
        /// </summary>
        private void ProcessDryerResourceCodes(SupplementalDryer dryer)
        {
            if (dryer.Exhaust != null && !string.IsNullOrEmpty(dryer.Exhaust.Code))
            {
                PopulateResourceText(dryer.Exhaust);
            }
        }

        /// <summary>
        /// Process resource codes for HRV duct specifications
        /// </summary>
        private void ProcessHrvDuctResourceCodes(HrvDuctSpec ductSpec)
        {
            if (ductSpec == null) return;

            // Process DuctType resource codes
            if (ductSpec.Type != null && !string.IsNullOrEmpty(ductSpec.Type.Code))
            {
                PopulateResourceText(ductSpec.Type);
            }

            // Process DuctLocation resource codes
            if (ductSpec.Location != null && !string.IsNullOrEmpty(ductSpec.Location.Code))
            {
                PopulateResourceText(ductSpec.Location);
            }

            // Process DuctSealing resource codes
            if (ductSpec.Sealing != null && !string.IsNullOrEmpty(ductSpec.Sealing.Code))
            {
                PopulateResourceText(ductSpec.Sealing);
            }
        }

        /// <summary>
        /// Set up relationships for all ventilators
        /// </summary>
        private void SetupVentilatorRelationships(Ventilation ventilation)
        {
            // Setup relationships using polymorphic lists
            SetupVentilatorListRelationships(ventilation.WholeHouseVentilatorList, ventilation.Id);
            SetupVentilatorListRelationships(ventilation.SupplementalVentilatorList, ventilation.Id);
        }

        /// <summary>
        /// Set up relationships for a list of ventilators
        /// </summary>
        private void SetupVentilatorListRelationships<T>(IEnumerable<T> ventilators, Guid ventilationId) where T : VentilatorObjects
        {
            if (ventilators == null) return;

            foreach (var ventilator in ventilators)
            {
                if (ventilator.Id == Guid.Empty)
                {
                    ventilator.Id = Guid.NewGuid();
                }
                ventilator.VentilationId = ventilationId;
            }
        }

        /// <summary>
        /// Populate resource text from code using VentilationService resource classes
        /// </summary>
        private void PopulateResourceText(ResourceList resource)
        {
            if (resource == null || string.IsNullOrEmpty(resource.Code)) return;

            // Only populate if English and French are empty (user provided only code)
            if (string.IsNullOrEmpty(resource.English) && string.IsNullOrEmpty(resource.French))
            {
                // Determine resource type and populate accordingly
                switch (resource)
                {
                    case VentilationRate ventRate:
                        PopulateVentilationRateText(ventRate);
                        break;
                    case DepressurizationLimits depLimit:
                        PopulateDepressurizationLimitText(depLimit);
                        break;
                    case VentilationUses ventUse:
                        PopulateVentilationUseText(ventUse);
                        break;
                    case AirDistributionTypes airDistType:
                        PopulateAirDistributionTypeText(airDistType);
                        break;
                    case AirDistributionFanPowerLevels fanPower:
                        PopulateAirDistributionFanPowerText(fanPower);
                        break;
                    case OperationSchedules opSchedule:
                        PopulateOperationScheduleText(opSchedule);
                        break;
                    case VentilatorTypes ventType:
                        PopulateVentilatorTypeText(ventType);
                        break;
                    case ExhaustLocationOptions exhaustLoc:
                        PopulateExhaustLocationText(exhaustLoc);
                        break;
                    case DuctTypes ductType:
                        PopulateDuctTypeText(ductType);
                        break;
                    case DuctLocations ductLoc:
                        PopulateDuctLocationText(ductLoc);
                        break;
                    case DuctSealingCharacteristics ductSeal:
                        PopulateDuctSealingText(ductSeal);
                        break;
                    default:
                        // For generic CodeAndText resources, preserve existing values
                        break;
                }
            }
        }

        private void PopulateVentilationRateText(VentilationRate resource)
        {
            var resourceItem = VentilationRate.All.FirstOrDefault(x => x.Code == resource.Code);
            if (resourceItem != null)
            {
                resource.English = resourceItem.English;
                resource.French = resourceItem.French;
                resource.Value = resourceItem.Value;
                resource.IsUserSpecified = resourceItem.IsUserSpecified;
            }
        }

        private void PopulateDepressurizationLimitText(DepressurizationLimits resource)
        {
            var resourceItem = DepressurizationLimits.All.FirstOrDefault(x => x.Code == resource.Code);
            if (resourceItem != null)
            {
                resource.English = resourceItem.English;
                resource.French = resourceItem.French;
                resource.Value = resourceItem.Value;
                resource.IsUserSpecified = resourceItem.IsUserSpecified;
            }
        }

        private void PopulateVentilationUseText(VentilationUses resource)
        {
            var resourceItem = VentilationUses.All.FirstOrDefault(x => x.Code == resource.Code);
            if (resourceItem != null)
            {
                resource.English = resourceItem.English;
                resource.French = resourceItem.French;
                resource.IsUserSpecified = resourceItem.IsUserSpecified;
            }
        }

        private void PopulateAirDistributionTypeText(AirDistributionTypes resource)
        {
            var resourceItem = AirDistributionTypes.All.FirstOrDefault(x => x.Code == resource.Code);
            if (resourceItem != null)
            {
                resource.English = resourceItem.English;
                resource.French = resourceItem.French;
                resource.IsUserSpecified = resourceItem.IsUserSpecified;
            }
        }

        private void PopulateAirDistributionFanPowerText(AirDistributionFanPowerLevels resource)
        {
            var resourceItem = AirDistributionFanPowerLevels.All.FirstOrDefault(x => x.Code == resource.Code);
            if (resourceItem != null)
            {
                resource.English = resourceItem.English;
                resource.French = resourceItem.French;
                resource.Value = resourceItem.Value;
                resource.IsUserSpecified = resourceItem.IsUserSpecified;
            }
        }

        private void PopulateOperationScheduleText(OperationSchedules resource)
        {
            var resourceItem = OperationSchedules.All.FirstOrDefault(x => x.Code == resource.Code);
            if (resourceItem != null)
            {
                resource.English = resourceItem.English;
                resource.French = resourceItem.French;
                resource.Value = resourceItem.Value;
                resource.IsUserSpecified = resourceItem.IsUserSpecified;
            }
        }

        private void PopulateVentilatorTypeText(VentilatorTypes resource)
        {
            var resourceItem = VentilatorTypes.All.FirstOrDefault(x => x.Code == resource.Code);
            if (resourceItem != null)
            {
                resource.English = resourceItem.English;
                resource.French = resourceItem.French;
                resource.IsUserSpecified = resourceItem.IsUserSpecified;
            }
        }

        private void PopulateExhaustLocationText(ExhaustLocationOptions resource)
        {
            var resourceItem = ExhaustLocationOptions.All.FirstOrDefault(x => x.Code == resource.Code);
            if (resourceItem != null)
            {
                resource.English = resourceItem.English;
                resource.French = resourceItem.French;
                resource.IsUserSpecified = resourceItem.IsUserSpecified;
            }
        }

        private void PopulateDuctTypeText(DuctTypes resource)
        {
            var resourceItem = DuctTypes.All.FirstOrDefault(x => x.Code == resource.Code);
            if (resourceItem != null)
            {
                resource.English = resourceItem.English;
                resource.French = resourceItem.French;
                resource.IsUserSpecified = resourceItem.IsUserSpecified;
            }
        }

        private void PopulateDuctLocationText(DuctLocations resource)
        {
            var resourceItem = DuctLocations.All.FirstOrDefault(x => x.Code == resource.Code);
            if (resourceItem != null)
            {
                resource.English = resourceItem.English;
                resource.French = resourceItem.French;
                resource.IsUserSpecified = resourceItem.IsUserSpecified;
            }
        }

        private void PopulateDuctSealingText(DuctSealingCharacteristics resource)
        {
            var resourceItem = DuctSealingCharacteristics.All.FirstOrDefault(x => x.Code == resource.Code);
            if (resourceItem != null)
            {
                resource.English = resourceItem.English;
                resource.French = resourceItem.French;
                resource.IsUserSpecified = resourceItem.IsUserSpecified;
            }
        }

        /// <summary>
        /// Set default values for ventilation system
        /// </summary>
        private void SetDefaultValues(Ventilation ventilation)
        {
            // Set default label if empty
            if (string.IsNullOrEmpty(ventilation.Label))
            {
                ventilation.Label = "Ventilation";
            }

            // Set default values for Rooms
            if (ventilation.Rooms != null)
            {
                if (ventilation.Rooms.VentilationRate == null)
                {
                    ventilation.Rooms.VentilationRate = VentilationRate.TenLitersPerSecond;
                }

                if (ventilation.Rooms.DepressurizationLimit == null)
                {
                    ventilation.Rooms.DepressurizationLimit = DepressurizationLimits.FivePa;
                }
            }

            // Set default values for Requirements
            if (ventilation.Requirements != null && ventilation.Requirements.Use == null)
            {
                ventilation.Requirements.Use = VentilationUses.NotApplicable;
            }

            // Set default values for WholeHouseParameters
            if (ventilation.SupplyAndExhaust != null)
            {
                if (ventilation.SupplyAndExhaust.AirDistributionType == null)
                {
                    ventilation.SupplyAndExhaust.AirDistributionType = AirDistributionTypes.ForcedAirHeatingDuctwork;
                }

                if (ventilation.SupplyAndExhaust.AirDistributionFanPower == null)
                {
                    ventilation.SupplyAndExhaust.AirDistributionFanPower = AirDistributionFanPowerLevels.Default;
                }

                if (ventilation.SupplyAndExhaust.OperationSchedule == null)
                {
                    ventilation.SupplyAndExhaust.OperationSchedule = OperationSchedules.Continuous;
                }
            }
        }

        /// <summary>
        /// Calculate minimum ventilation rate based on room counts
        /// </summary>
        private void CalculateMinimumVentilationRate(Rooms rooms)
        {
            // Ported from CVentData::RecalculateMinVentRate() in HOT2000
            decimal minRate = rooms.Living + rooms.Bathrooms + rooms.Utility + rooms.OtherHabitable;
            if (rooms.Bedrooms > 0) minRate += rooms.Bedrooms;
            minRate *= 5m;

            if (rooms.Bedrooms > 0) minRate += 10;
            if (rooms.VentilationRate != null)
            {
                minRate += rooms.VentilationRate.Value;
            }

            // The calculated value would be used for validation or display purposes
            _logger.LogDebug("Calculated minimum ventilation rate: {MinRate} L/s", minRate);
        }

        public async Task<Ventilation?> GetVentilationByEnergyUpgradeIdAsync(Guid energyUpgradeId)
        {
            return await _repository.GetVentilationByEnergyUpgradeIdAsync(energyUpgradeId);
        }

        public async Task<Ventilation> DuplicateVentilationForEnergyUpgradeAsync(Ventilation baseVentilation, Guid energyUpgradeId)
        {
            return await _repository.DuplicateVentilationForEnergyUpgradeAsync(baseVentilation, energyUpgradeId);
        }
    }
}