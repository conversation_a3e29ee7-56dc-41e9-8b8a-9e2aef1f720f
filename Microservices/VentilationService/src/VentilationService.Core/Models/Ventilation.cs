using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;

namespace VentilationService.Core.Models
{
    /// <summary>
    /// Represents the ventilation system information for a house
    /// Migrated from original XML-based approach to new microservice approach following HvacService pattern
    /// </summary>
    public class Ventilation
    {
        public Guid Id { get; set; }

        public Guid HouseId { get; set; }
        public Guid? EnergyUpgradeId { get; set; }

        public string Label { get; set; } = "Ventilation";

        // Navigation properties matching original structure exactly
        public Rooms Rooms { get; set; } = new Rooms();

        public Requirements Requirements { get; set; } = new Requirements();

        public WholeHouseParameters SupplyAndExhaust { get; set; } = new WholeHouseParameters();

        // Individual type collections (mapped to database)
        public List<WholeHouseHrv> WholeHouseHrvList { get; set; } = new List<WholeHouseHrv>();
        public List<WholeHouseDryer> WholeHouseDryerList { get; set; } = new List<WholeHouseDryer>();
        public List<WholeHouseVentilator> WholeHouseVentilators { get; set; } = new List<WholeHouseVentilator>();
        public List<SupplementalHrv> SupplementalHrvList { get; set; } = new List<SupplementalHrv>();
        public List<SupplementalDryer> SupplementalDryerList { get; set; } = new List<SupplementalDryer>();
        public List<SupplementalVentilator> SupplementalVentilators { get; set; } = new List<SupplementalVentilator>();

        // Polymorphic collections (computed properties for API compatibility)
        // These aggregate the individual type collections into polymorphic lists
        public List<VentilatorObjects> WholeHouseVentilatorList
        {
            get
            {
                var result = new List<VentilatorObjects>();
                result.AddRange(WholeHouseHrvList);
                result.AddRange(WholeHouseDryerList);
                result.AddRange(WholeHouseVentilators);
                return result;
            }
            set
            {
                // Clear existing lists
                WholeHouseHrvList.Clear();
                WholeHouseDryerList.Clear();
                WholeHouseVentilators.Clear();

                // Distribute items to appropriate type-specific lists
                foreach (var item in value)
                {
                    switch (item)
                    {
                        case WholeHouseHrv hrv:
                            WholeHouseHrvList.Add(hrv);
                            break;
                        case WholeHouseDryer dryer:
                            WholeHouseDryerList.Add(dryer);
                            break;
                        case WholeHouseVentilator ventilator:
                            WholeHouseVentilators.Add(ventilator);
                            break;
                    }
                }
            }
        }

        public List<VentilatorObjects> SupplementalVentilatorList
        {
            get
            {
                var result = new List<VentilatorObjects>();
                result.AddRange(SupplementalHrvList);
                result.AddRange(SupplementalDryerList);
                result.AddRange(SupplementalVentilators);
                return result;
            }
            set
            {
                // Clear existing lists
                SupplementalHrvList.Clear();
                SupplementalDryerList.Clear();
                SupplementalVentilators.Clear();

                // Distribute items to appropriate type-specific lists
                foreach (var item in value)
                {
                    switch (item)
                    {
                        case SupplementalHrv hrv:
                            SupplementalHrvList.Add(hrv);
                            break;
                        case SupplementalDryer dryer:
                            SupplementalDryerList.Add(dryer);
                            break;
                        case SupplementalVentilator ventilator:
                            SupplementalVentilators.Add(ventilator);
                            break;
                    }
                }
            }
        }

        // Calculated properties (similar to original XmlIgnore properties)
        public decimal WholeHouseVentilatorSupplyTotal => WholeHouseVentilatorList.Sum(v => v.SupplyFlowrate);
        public decimal WholeHouseVentilatorExhaustTotal => WholeHouseVentilatorList.Sum(v => v.ExhaustFlowrate);
        public decimal SupplementalVentilatorSupplyTotal => SupplementalVentilatorList.Sum(v => v.SupplyFlowrate);
        public decimal SupplementalVentilatorExhaustTotal => SupplementalVentilatorList.Sum(v => v.ExhaustFlowrate);

        public void AddDryer()
        {
            // TODO: implement?
        }

        public int GetDryerIndex()
        {
            // TODO: implement?
            return -1;
        }

        public void SetDryerParams()
        {
            // TODO: implement?
        }

        public Ventilation()
        {
            Label = "Ventilation";
            Rooms = new Rooms();
            Requirements = new Requirements();
            SupplyAndExhaust = new WholeHouseParameters();
            WholeHouseHrvList = new List<WholeHouseHrv>();
            WholeHouseDryerList = new List<WholeHouseDryer>();
            WholeHouseVentilators = new List<WholeHouseVentilator>();
            SupplementalHrvList = new List<SupplementalHrv>();
            SupplementalDryerList = new List<SupplementalDryer>();
            SupplementalVentilators = new List<SupplementalVentilator>();
        }
    }
}