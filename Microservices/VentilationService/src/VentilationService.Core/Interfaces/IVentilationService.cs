using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using VentilationService.Core.Models;

namespace VentilationService.Core.Interfaces
{
    /// <summary>
    /// Service interface for ventilation operations
    /// Following the BaseLoadService pattern
    /// </summary>
    public interface IVentilationService
    {
        /// <summary>
        /// Gets all ventilation systems
        /// </summary>
        Task<IEnumerable<Ventilation>> GetAllVentilationSystemsAsync();

        /// <summary>
        /// Gets ventilation system for a specific house
        /// </summary>
        Task<Ventilation?> GetVentilationSystemByHouseIdAsync(Guid houseId);

        /// <summary>
        /// Gets a specific ventilation system by ID
        /// </summary>
        Task<Ventilation?> GetVentilationSystemByIdAsync(Guid id);

        /// <summary>
        /// Creates a new ventilation system
        /// Ensures only one ventilation system per house
        /// </summary>
        Task<Ventilation> CreateVentilationSystemAsync(Ventilation ventilation);

        /// <summary>
        /// Updates an existing ventilation system
        /// </summary>
        Task UpdateVentilationSystemAsync(Ventilation ventilation);

        /// <summary>
        /// Deletes a ventilation system
        /// </summary>
        Task DeleteVentilationSystemAsync(Guid id);

        /// <summary>
        /// Sets default ventilation system configuration
        /// </summary>
        Task<Ventilation> SetDefaultVentilationSystemAsync(Ventilation ventilation);

        /// <summary>
        /// Calculates ventilation system performance
        /// </summary>
        Task<Ventilation> CalculateVentilationSystemAsync(Ventilation ventilation, bool restoreDefaults = false);

        // Energy upgrade operations
        Task<Ventilation?> GetVentilationByEnergyUpgradeIdAsync(Guid energyUpgradeId);
        Task<Ventilation> DuplicateVentilationForEnergyUpgradeAsync(Ventilation baseVentilation, Guid energyUpgradeId);
    }
}