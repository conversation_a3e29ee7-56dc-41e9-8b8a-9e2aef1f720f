using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using VentilationService.Core.Models;

namespace VentilationService.Core.Interfaces
{
    /// <summary>
    /// Repository interface for ventilation operations
    /// Following the BaseLoadService pattern
    /// </summary>
    public interface IVentilationRepository
    {
        /// <summary>
        /// Gets all ventilation systems with their related entities
        /// </summary>
        Task<IEnumerable<Ventilation>> GetAllVentilationSystemsAsync();

        /// <summary>
        /// Gets ventilation system for a specific house with its related entities
        /// </summary>
        Task<Ventilation?> GetVentilationSystemByHouseIdAsync(Guid houseId);

        /// <summary>
        /// Gets a specific ventilation system by ID with its related entities
        /// </summary>
        Task<Ventilation?> GetVentilationSystemByIdAsync(Guid id);

        /// <summary>
        /// Creates a new ventilation system with its related entities
        /// </summary>
        Task<Ventilation> CreateVentilationSystemAsync(Ventilation ventilation);

        /// <summary>
        /// Updates an existing ventilation system and its related entities
        /// </summary>
        Task UpdateVentilationSystemAsync(Ventilation ventilation);

        /// <summary>
        /// Deletes a ventilation system and its related entities
        /// </summary>
        Task DeleteVentilationSystemAsync(Guid id);

        /// <summary>
        /// Checks if a ventilation system exists by ID
        /// </summary>
        Task<bool> ExistsAsync(Guid id);

        // Energy upgrade operations
        Task<Ventilation?> GetVentilationByEnergyUpgradeIdAsync(Guid energyUpgradeId);
        Task<Ventilation> DuplicateVentilationForEnergyUpgradeAsync(Ventilation baseVentilation, Guid energyUpgradeId);
    }
}