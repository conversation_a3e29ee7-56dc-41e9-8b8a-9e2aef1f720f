using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using AutoMapper;
using VentilationService.API.Models;
using VentilationService.Core.Interfaces;
using VentilationService.Core.Models;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;

namespace VentilationService.API.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class VentilationController : ControllerBase
    {
        private readonly IVentilationService _ventilationService;
        private readonly IMapper _mapper;
        private readonly ILogger<VentilationController> _logger;

        public VentilationController(
            IVentilationService ventilationService,
            IMapper mapper,
            ILogger<VentilationController> logger)
        {
            _ventilationService = ventilationService;
            _mapper = mapper;
            _logger = logger;
        }

        /// <summary>
        /// Gets all ventilation systems
        /// </summary>
        [HttpGet]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<ActionResult<IEnumerable<VentilationDto>>> GetAllVentilationSystems()
        {
            _logger.LogInformation("Getting all ventilation systems");

            var ventilationSystems = await _ventilationService.GetAllVentilationSystemsAsync();

            if (ventilationSystems == null)
            {
                _logger.LogWarning("No ventilation systems found");
                return NotFound();
            }

            var ventilationDtos = _mapper.Map<IEnumerable<VentilationDto>>(ventilationSystems);

            return Ok(ventilationDtos);
        }

        /// <summary>
        /// Gets ventilation system for a specific house
        /// </summary>
        [HttpGet("house/{houseId}")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<ActionResult<VentilationDto>> GetVentilationSystemByHouseId(Guid houseId)
        {
            _logger.LogInformation("Getting ventilation system for house ID: {HouseId}", houseId);

            var ventilation = await _ventilationService.GetVentilationSystemByHouseIdAsync(houseId);

            if (ventilation == null)
            {
                _logger.LogWarning("No ventilation system found for house ID: {HouseId}", houseId);
                return NotFound();
            }

            var ventilationDto = _mapper.Map<VentilationDto>(ventilation);

            return Ok(ventilationDto);
        }

        /// <summary>
        /// Gets a specific ventilation system by ID
        /// </summary>
        [HttpGet("{id}")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<ActionResult<VentilationDto>> GetVentilationSystemById(Guid id)
        {
            _logger.LogInformation("Getting ventilation system with ID: {Id}", id);

            var ventilation = await _ventilationService.GetVentilationSystemByIdAsync(id);

            if (ventilation == null)
            {
                _logger.LogWarning("No ventilation system found with ID: {Id}", id);
                return NotFound();
            }

            var ventilationDto = _mapper.Map<VentilationDto>(ventilation);

            return Ok(ventilationDto);
        }

        /// <summary>
        /// Creates a new ventilation system
        /// </summary>
        [HttpPost]
        [ProducesResponseType(StatusCodes.Status201Created)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        public async Task<ActionResult<VentilationDto>> CreateVentilationSystem([FromBody] VentilationDto ventilationDto)
        {
            if (ventilationDto == null)
            {
                _logger.LogWarning("Ventilation system data is null");
                return BadRequest("Ventilation system data is required");
            }

            _logger.LogInformation("Creating new ventilation system for house ID: {HouseId}", ventilationDto.HouseId);

            var ventilation = _mapper.Map<Ventilation>(ventilationDto);

            var createdVentilation = await _ventilationService.CreateVentilationSystemAsync(ventilation);
            var createdVentilationDto = _mapper.Map<VentilationDto>(createdVentilation);

            return CreatedAtAction(
                nameof(GetVentilationSystemById),
                new { id = createdVentilationDto.Id },
                createdVentilationDto);
        }

        /// <summary>
        /// Updates an existing ventilation system
        /// </summary>
        [HttpPut("{id}")]
        [ProducesResponseType(StatusCodes.Status204NoContent)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<IActionResult> UpdateVentilationSystem(Guid id, [FromBody] VentilationDto ventilationDto)
        {
            if (ventilationDto == null)
            {
                _logger.LogWarning("Ventilation system data is null");
                return BadRequest("Ventilation system data is required");
            }

            if (id != ventilationDto.Id)
            {
                _logger.LogWarning("ID mismatch: {Id} != {DtoId}", id, ventilationDto.Id);
                return BadRequest("ID mismatch");
            }

            _logger.LogInformation("Updating ventilation system with ID: {Id}", id);

            var ventilation = _mapper.Map<Ventilation>(ventilationDto);

            try
            {
                await _ventilationService.UpdateVentilationSystemAsync(ventilation);
            }
            catch (InvalidOperationException ex) when (ex.Message.Contains("not found"))
            {
                _logger.LogWarning("Ventilation system not found with ID: {Id}", id);
                return NotFound($"Ventilation system not found with ID: {id}");
            }

            return NoContent();
        }

        /// <summary>
        /// Deletes a ventilation system
        /// </summary>
        [HttpDelete("{id}")]
        [ProducesResponseType(StatusCodes.Status204NoContent)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<IActionResult> DeleteVentilationSystem(Guid id)
        {
            var existingVentilation = await _ventilationService.GetVentilationSystemByIdAsync(id);
            if (existingVentilation == null)
            {
                _logger.LogWarning("Ventilation system with ID: {Id} not found for deletion", id);
                return NotFound();
            }

            _logger.LogInformation("Deleting ventilation system with ID: {Id}", id);

            await _ventilationService.DeleteVentilationSystemAsync(id);

            return NoContent();
        }

        // GET: api/ventilation/energy-upgrades/{energyUpgradeId}/ventilations
        [HttpGet("energy-upgrades/{energyUpgradeId}/ventilations")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<ActionResult<VentilationDto>> GetVentilationsByEnergyUpgradeId(Guid energyUpgradeId)
        {
            try
            {
                _logger.LogInformation("Getting ventilation by energy upgrade ID: {EnergyUpgradeId}", energyUpgradeId);

                var ventilation = await _ventilationService.GetVentilationByEnergyUpgradeIdAsync(energyUpgradeId);

                if (ventilation == null)
                {
                    return NotFound(new { Error = "Ventilation not found for energy upgrade", EnergyUpgradeId = energyUpgradeId });
                }

                var ventilationDto = _mapper.Map<VentilationDto>(ventilation);
                return Ok(ventilationDto);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting ventilation by energy upgrade ID: {EnergyUpgradeId}", energyUpgradeId);
                return StatusCode(500, new { Error = "Internal server error" });
            }
        }

        // POST: api/ventilation/{baseVentilationId}/duplicate-for-energy-upgrade
        [HttpPost("{baseVentilationId}/duplicate-for-energy-upgrade")]
        [ProducesResponseType(StatusCodes.Status201Created)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<ActionResult<VentilationDto>> DuplicateVentilationForEnergyUpgrade(
            Guid baseVentilationId,
            [FromQuery] Guid energyUpgradeId)
        {
            try
            {
                _logger.LogInformation("Duplicating ventilation {BaseVentilationId} for energy upgrade {EnergyUpgradeId}",
                    baseVentilationId, energyUpgradeId);

                var baseVentilation = await _ventilationService.GetVentilationSystemByIdAsync(baseVentilationId);

                if (baseVentilation == null)
                {
                    return NotFound(new { Error = "Base ventilation not found", BaseVentilationId = baseVentilationId });
                }

                var duplicatedVentilation = await _ventilationService.DuplicateVentilationForEnergyUpgradeAsync(
                    baseVentilation, energyUpgradeId);

                var ventilationDto = _mapper.Map<VentilationDto>(duplicatedVentilation);

                return CreatedAtAction(
                    nameof(GetVentilationSystemById),
                    new { id = ventilationDto.Id },
                    ventilationDto);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error duplicating ventilation {BaseVentilationId} for energy upgrade {EnergyUpgradeId}",
                    baseVentilationId, energyUpgradeId);
                return StatusCode(500, new { Error = "Internal server error" });
            }
        }

    }
}