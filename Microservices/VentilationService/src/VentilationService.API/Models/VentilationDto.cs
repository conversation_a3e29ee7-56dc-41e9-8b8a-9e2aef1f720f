using System;
using System.Collections.Generic;

namespace VentilationService.API.Models
{
    /// <summary>
    /// DTO for Ventilation system following HvacService pattern
    /// </summary>
    public class VentilationDto
    {
        public Guid Id { get; set; }
        public Guid HouseId { get; set; }
        public Guid? EnergyUpgradeId { get; set; }
        public string Label { get; set; } = "Ventilation";

        // Navigation properties
        public RoomsDto Rooms { get; set; } = new RoomsDto();
        public RequirementsDto Requirements { get; set; } = new RequirementsDto();
        public WholeHouseParametersDto SupplyAndExhaust { get; set; } = new WholeHouseParametersDto();

        // Separate collections for different ventilator types (following HvacService pattern)
        public List<WholeHouseHrvDto> WholeHouseHrvList { get; set; } = new List<WholeHouseHrvDto>();
        public List<WholeHouseDryerDto> WholeHouseDryerList { get; set; } = new List<WholeHouseDryerDto>();
        public List<WholeHouseVentilatorDto> WholeHouseVentilatorList { get; set; } = new List<WholeHouseVentilatorDto>();
        public List<SupplementalHrvDto> SupplementalHrvList { get; set; } = new List<SupplementalHrvDto>();
        public List<SupplementalDryerDto> SupplementalDryerList { get; set; } = new List<SupplementalDryerDto>();
        public List<SupplementalVentilatorDto> SupplementalVentilatorList { get; set; } = new List<SupplementalVentilatorDto>();

        // Polymorphic collections (computed properties for API compatibility)
        // These aggregate the individual type collections into polymorphic lists
        public List<object> WholeHouseVentilatorListCombined
        {
            get
            {
                var result = new List<object>();
                result.AddRange(WholeHouseHrvList.Cast<object>());
                result.AddRange(WholeHouseDryerList.Cast<object>());
                result.AddRange(WholeHouseVentilatorList.Cast<object>());
                return result;
            }
        }

        public List<object> SupplementalVentilatorListCombined
        {
            get
            {
                var result = new List<object>();
                result.AddRange(SupplementalHrvList.Cast<object>());
                result.AddRange(SupplementalDryerList.Cast<object>());
                result.AddRange(SupplementalVentilatorList.Cast<object>());
                return result;
            }
        }

        // Calculated properties (read-only)
        public decimal WholeHouseVentilatorSupplyTotal { get; set; }
        public decimal WholeHouseVentilatorExhaustTotal { get; set; }
        public decimal SupplementalVentilatorSupplyTotal { get; set; }
        public decimal SupplementalVentilatorExhaustTotal { get; set; }
    }
}
