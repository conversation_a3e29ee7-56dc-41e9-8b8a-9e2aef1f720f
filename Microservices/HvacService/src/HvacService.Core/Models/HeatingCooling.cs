using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace HvacService.Core.Models
{
    /// <summary>
    /// Represents the heating and cooling system information for a house
    /// Matches the structure of the original HeatingCooling.cs
    /// </summary>
    public class HeatingCooling
    {
        public Guid Id { get; set; }
        
        public Guid HouseId { get; set; }

        public Guid? EnergyUpgradeId { get; set; }

        public string Label { get; set; } = "HeatingCooling";

        [Timestamp]  // Add this for optimistic concurrency
        public byte[] RowVersion { get; set; }

        // System selection properties (similar to old m_heatType, m_pumpType, etc.)
        public int HeatType { get; set; } = 1; // 1=Baseboards, 2=Furnace, 3=Boiler, 4=Combo, 5=IMS, 6=P9
        public int PumpType { get; set; } = 0; // 0=N/A, 1=ASHP, 2=WSHP, 3=GSHP, 4=AC
        public bool HasRadiantHeating { get; set; } = false;
        public bool HasMultipleSystems { get; set; } = false;
        public int SupplementarySystemsCount { get; set; } = 0;
        public bool HasAdditionalOpenings { get; set; } = false;

        // Navigation properties matching original structure exactly
        public CoolingSeason CoolingSeason { get; set; } = new CoolingSeason();
        
        public Type1 Type1 { get; set; } = new Type1();
        
        public Type2 Type2 { get; set; } = new Type2();
        
        public MultipleSystems? MultipleSystems { get; set; }
        
        public RadiantHeating? RadiantHeating { get; set; }
        
        public List<AdditionalOpening>? AdditionalOpenings { get; set; }
        
        public List<SupplementaryHeat>? SupplementaryHeating { get; set; }
        
        public HeatingCooling()
        {
            Label = "HeatingCooling";
        }

        // Helper methods to determine which systems are selected (similar to old HasBoiler(), HasFurnace(), etc.)
        public bool HasBaseboards() => HeatType == 1;
        public bool HasFurnace() => HeatType == 2;
        public bool HasBoiler() => HeatType == 3;
        public bool HasCombo() => HeatType == 4;
        public bool HasIMS() => HeatType == 5;
        public bool HasP9() => HeatType == 6;

        public bool HasHeatPump() => PumpType > 0 && PumpType <= 3;
        public bool HasAirConditioning() => PumpType == 4;
        public bool HasCooling() => HasHeatPump() || HasAirConditioning();

        // Method to determine which Type1 system should be saved
        public Type1SystemType GetSelectedType1System()
        {
            return HeatType switch
            {
                1 => Type1SystemType.Baseboards,
                2 => Type1SystemType.Furnace,
                3 => Type1SystemType.Boiler,
                4 => Type1SystemType.ComboHeatDhw,
                5 => Type1SystemType.IMS,
                6 => Type1SystemType.P9,
                _ => Type1SystemType.None
            };
        }

        // Method to determine which Type2 system should be saved
        public Type2SystemType GetSelectedType2System()
        {
            return PumpType switch
            {
                0 => Type2SystemType.None,
                1 => Type2SystemType.AirSourceHeatPump,
                2 => Type2SystemType.WaterSourceHeatPump,
                3 => Type2SystemType.GroundSourceHeatPump,
                4 => Type2SystemType.AirConditioning,
                _ => Type2SystemType.None
            };
        }
    }

    // Enums to define system types
    public enum Type1SystemType
    {
        None = 0,
        Baseboards = 1,
        Furnace = 2,
        Boiler = 3,
        ComboHeatDhw = 4,
        IMS = 5,
        P9 = 6
    }

    public enum Type2SystemType
    {
        None = 0,
        AirSourceHeatPump = 1,
        WaterSourceHeatPump = 2,
        GroundSourceHeatPump = 3,
        AirConditioning = 4
    }
}
