using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using HvacService.Core.Models;

namespace HvacService.Core.Interfaces
{
    /// <summary>
    /// Repository interface for heating cooling operations
    /// Following BaseLoadService pattern
    /// </summary>
    public interface IHeatingCoolingRepository
    {
        /// <summary>
        /// Gets all heating cooling systems with their related entities
        /// </summary>
        Task<IEnumerable<HeatingCooling>> GetAllHeatingCoolingsAsync();
        
        /// <summary>
        /// Gets heating cooling system for a specific house with their related entities
        /// </summary>
        Task<HeatingCooling?> GetHeatingCoolingByHouseIdAsync(Guid houseId);
        
        /// <summary>
        /// Gets a specific heating cooling system by ID with its related entities
        /// </summary>
        Task<HeatingCooling?> GetHeatingCoolingByIdAsync(Guid id);
        
        /// <summary>
        /// Creates a new heating cooling system with its related entities
        /// </summary>
        Task<HeatingCooling> CreateHeatingCoolingAsync(HeatingCooling heatingCooling);
        
        /// <summary>
        /// Updates an existing heating cooling system and its related entities
        /// </summary>
        Task UpdateHeatingCoolingAsync(HeatingCooling heatingCooling);
        
        /// <summary>
        /// Deletes a heating cooling system and its related entities
        /// </summary>
        Task DeleteHeatingCoolingAsync(Guid id);

        /// <summary>
        /// Gets heating cooling system by energy upgrade ID
        /// </summary>
        Task<HeatingCooling?> GetHeatingCoolingByEnergyUpgradeIdAsync(Guid energyUpgradeId);

        /// <summary>
        /// Duplicates a heating cooling system for energy upgrade
        /// </summary>
        Task<HeatingCooling> DuplicateHeatingCoolingForEnergyUpgradeAsync(HeatingCooling baseHeatingCooling, Guid energyUpgradeId);
    }
}
