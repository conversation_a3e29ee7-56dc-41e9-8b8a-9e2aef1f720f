using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using HvacService.Core.Interfaces;
using HvacService.Core.Models;
using Microsoft.Extensions.Logging;

namespace HvacService.Core.Services
{
    public class HeatingCoolingService : IHeatingCoolingService
    {
        private readonly IHeatingCoolingRepository _repository;
        private readonly ILogger<HeatingCoolingService> _logger;

        public HeatingCoolingService(
            IHeatingCoolingRepository repository,
            ILogger<HeatingCoolingService> logger)
        {
            _repository = repository;
            _logger = logger;
        }

        public async Task<IEnumerable<HeatingCooling>> GetAllHeatingCoolingsAsync()
        {
            _logger.LogInformation("Getting all heating cooling systems from service");
            return await _repository.GetAllHeatingCoolingsAsync();
        }

        public async Task<HeatingCooling?> GetHeatingCoolingByHouseIdAsync(Guid houseId)
        {
            _logger.LogInformation("Getting heating cooling system for house ID: {HouseId} from service", houseId);
            return await _repository.GetHeatingCoolingByHouseIdAsync(houseId);
        }

        public async Task<HeatingCooling?> GetHeatingCoolingByIdAsync(Guid id)
        {
            _logger.LogInformation("Getting heating cooling system with ID: {Id} from service", id);
            return await _repository.GetHeatingCoolingByIdAsync(id);
        }

        public async Task<HeatingCooling> CreateHeatingCoolingAsync(HeatingCooling heatingCooling)
        {
            _logger.LogInformation("Creating new heating cooling system for house ID: {HouseId} from service", heatingCooling.HouseId);
            return await _repository.CreateHeatingCoolingAsync(heatingCooling);
        }

        public async Task UpdateHeatingCoolingAsync(HeatingCooling heatingCooling)
        {
            _logger.LogInformation("Updating heating cooling system with ID: {Id} from service", heatingCooling.Id);
            await _repository.UpdateHeatingCoolingAsync(heatingCooling);
        }

        public async Task DeleteHeatingCoolingAsync(Guid id)
        {
            _logger.LogInformation("Deleting heating cooling system with ID: {Id} from service", id);
            await _repository.DeleteHeatingCoolingAsync(id);
        }

        public async Task<HeatingCooling?> GetHeatingCoolingByEnergyUpgradeIdAsync(Guid energyUpgradeId)
        {
            return await _repository.GetHeatingCoolingByEnergyUpgradeIdAsync(energyUpgradeId);
        }

        public async Task<HeatingCooling> DuplicateHeatingCoolingForEnergyUpgradeAsync(HeatingCooling baseHeatingCooling, Guid energyUpgradeId)
        {
            return await _repository.DuplicateHeatingCoolingForEnergyUpgradeAsync(baseHeatingCooling, energyUpgradeId);
        }
    }
}
