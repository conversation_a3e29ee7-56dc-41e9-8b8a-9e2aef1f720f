using System;
using System.Collections.Generic;

namespace HvacService.API.Models
{
    /// <summary>
    /// DTO for HeatingCooling system
    /// </summary>
    public class HeatingCoolingDto
    {
        public Guid Id { get; set; }
        public Guid HouseId { get; set; }
        public Guid? EnergyUpgradeId { get; set; }
        public string Label { get; set; } = "HeatingCooling";

        // System selection properties (determines which systems to save)
        public int HeatType { get; set; } = 1; // 1=Baseboards, 2=Furnace, 3=Boiler, 4=Combo, 5=IMS, 6=P9
        public int PumpType { get; set; } = 0; // 0=N/A, 1=ASHP, 2=WSHP, 3=GSHP, 4=AC
        public bool HasRadiantHeating { get; set; } = false;
        public bool HasMultipleSystems { get; set; } = false;
        public int SupplementarySystemsCount { get; set; } = 0;
        public bool HasAdditionalOpenings { get; set; } = false;

        // Navigation properties
        public CoolingSeasonDto CoolingSeason { get; set; } = new CoolingSeasonDto();
        public Type1Dto Type1 { get; set; } = new Type1Dto();
        public Type2Dto Type2 { get; set; } = new Type2Dto();
        public MultipleSystemsDto? MultipleSystems { get; set; }
        public RadiantHeatingDto? RadiantHeating { get; set; }
        public List<AdditionalOpeningDto>? AdditionalOpenings { get; set; }
        public List<SupplementaryHeatDto>? SupplementaryHeating { get; set; }
    }
}
