using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using AutoMapper;
using HvacService.API.Models;
using HvacService.Core.Interfaces;
using HvacService.Core.Models;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;

namespace HvacService.API.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class HeatingCoolingController : ControllerBase
    {
        private readonly IHeatingCoolingService _heatingCoolingService;
        private readonly IMapper _mapper;
        private readonly ILogger<HeatingCoolingController> _logger;

        public HeatingCoolingController(
            IHeatingCoolingService heatingCoolingService,
            IMapper mapper,
            ILogger<HeatingCoolingController> logger)
        {
            _heatingCoolingService = heatingCoolingService;
            _mapper = mapper;
            _logger = logger;
        }

        /// <summary>
        /// Gets all heating cooling systems
        /// </summary>
        [HttpGet]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<ActionResult<IEnumerable<HeatingCoolingDto>>> GetAllHeatingCoolings()
        {
            _logger.LogInformation("Getting all heating cooling systems");

            var heatingCoolings = await _heatingCoolingService.GetAllHeatingCoolingsAsync();

            if (heatingCoolings == null)
            {
                _logger.LogWarning("No heating cooling systems found");
                return NotFound("No heating cooling systems found");
            }

            var heatingCoolingDtos = _mapper.Map<IEnumerable<HeatingCoolingDto>>(heatingCoolings);
            return Ok(heatingCoolingDtos);
        }

        /// <summary>
        /// Gets heating cooling system for a specific house
        /// </summary>
        [HttpGet("house/{houseId}")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<ActionResult<HeatingCoolingDto>> GetHeatingCoolingByHouseId(Guid houseId)
        {
            _logger.LogInformation("Getting heating cooling system for house ID: {HouseId}", houseId);

            var heatingCooling = await _heatingCoolingService.GetHeatingCoolingByHouseIdAsync(houseId);

            if (heatingCooling == null)
            {
                _logger.LogWarning("Heating cooling system not found for house ID: {HouseId}", houseId);
                return NotFound($"Heating cooling system not found for house ID: {houseId}");
            }

            var heatingCoolingDto = _mapper.Map<HeatingCoolingDto>(heatingCooling);
            return Ok(heatingCoolingDto);
        }

        /// <summary>
        /// Gets a specific heating cooling system by ID
        /// </summary>
        [HttpGet("{id}")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<ActionResult<HeatingCoolingDto>> GetHeatingCoolingById(Guid id)
        {
            _logger.LogInformation("Getting heating cooling system with ID: {Id}", id);

            var heatingCooling = await _heatingCoolingService.GetHeatingCoolingByIdAsync(id);

            if (heatingCooling == null)
            {
                _logger.LogWarning("Heating cooling system not found with ID: {Id}", id);
                return NotFound($"Heating cooling system not found with ID: {id}");
            }

            var heatingCoolingDto = _mapper.Map<HeatingCoolingDto>(heatingCooling);
            return Ok(heatingCoolingDto);
        }
        
        /// <summary>
        /// Creates a new heating cooling system
        /// </summary>
        [HttpPost]
        [ProducesResponseType(StatusCodes.Status201Created)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        public async Task<ActionResult<HeatingCoolingDto>> CreateHeatingCooling([FromBody] HeatingCoolingDto heatingCoolingDto)
        {
            if (heatingCoolingDto == null)
            {
                _logger.LogWarning("Heating cooling system data is null");
                return BadRequest("Heating cooling system data is required");
            }

            _logger.LogInformation("Creating new heating cooling system for house ID: {HouseId}", heatingCoolingDto.HouseId);

            var heatingCooling = _mapper.Map<HeatingCooling>(heatingCoolingDto);

            // Populate equipment type text from resource classes
            PopulateEquipmentTypeText(heatingCooling);

            var createdHeatingCooling = await _heatingCoolingService.CreateHeatingCoolingAsync(heatingCooling);
            var createdHeatingCoolingDto = _mapper.Map<HeatingCoolingDto>(createdHeatingCooling);

            return CreatedAtAction(
                nameof(GetHeatingCoolingById),
                new { id = createdHeatingCoolingDto.Id },
                createdHeatingCoolingDto);
        }
        
        /// <summary>
        /// Updates an existing heating cooling system
        /// </summary>
        [HttpPut("{id}")]
        [ProducesResponseType(StatusCodes.Status204NoContent)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<IActionResult> UpdateHeatingCooling(Guid id, [FromBody] HeatingCoolingDto heatingCoolingDto)
        {
            if (heatingCoolingDto == null)
            {
                _logger.LogWarning("Heating cooling system data is null");
                return BadRequest("Heating cooling system data is required");
            }

            if (id != heatingCoolingDto.Id)
            {
                _logger.LogWarning("ID mismatch: {Id} != {DtoId}", id, heatingCoolingDto.Id);
                return BadRequest("ID mismatch");
            }

            _logger.LogInformation("Updating heating cooling system with ID: {Id}", id);

            var heatingCooling = _mapper.Map<HeatingCooling>(heatingCoolingDto);

            // SIMPLIFIED: Follow Type1 approach exactly - let repository handle all entity relationships
            // No complex foreign key management needed in controller

            // Populate equipment type text from resource classes
            PopulateEquipmentTypeText(heatingCooling);

            try
            {
                await _heatingCoolingService.UpdateHeatingCoolingAsync(heatingCooling);
            }
            catch (InvalidOperationException ex) when (ex.Message.Contains("not found"))
            {
                _logger.LogWarning("Heating cooling system not found with ID: {Id}", id);
                return NotFound($"Heating cooling system not found with ID: {id}");
            }

            return NoContent();
        }
        
        /// <summary>
        /// Deletes a heating cooling system
        /// </summary>
        [HttpDelete("{id}")]
        [ProducesResponseType(StatusCodes.Status204NoContent)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<IActionResult> DeleteHeatingCooling(Guid id)
        {
            var existingHeatingCooling = await _heatingCoolingService.GetHeatingCoolingByIdAsync(id);
            if (existingHeatingCooling == null)
            {
                _logger.LogWarning("Heating cooling system not found with ID: {Id}", id);
                return NotFound($"Heating cooling system not found with ID: {id}");
            }

            _logger.LogInformation("Deleting heating cooling system with ID: {Id}", id);
            await _heatingCoolingService.DeleteHeatingCoolingAsync(id);

            return NoContent();
        }

        /// <summary>
        /// Populates equipment type text from resource classes based on energy source
        /// </summary>
        private void PopulateEquipmentTypeText(HeatingCooling heatingCooling)
        {
            // Populate Type1 equipment types
            if (heatingCooling.Type1 != null)
            {
                // Boiler equipment
                if (heatingCooling.Type1.Boiler?.Equipment != null)
                {
                    heatingCooling.Type1.Boiler.Equipment.LoadEquipmentTypeFromData();
                    if (heatingCooling.Type1.Boiler.Equipment.EquipmentType != null)
                    {
                        heatingCooling.Type1.Boiler.Equipment.SetEquipmentType(heatingCooling.Type1.Boiler.Equipment.EquipmentType);
                    }
                }

                // Furnace equipment
                if (heatingCooling.Type1.Furnace?.Equipment != null)
                {
                    heatingCooling.Type1.Furnace.Equipment.LoadEquipmentTypeFromData();
                    if (heatingCooling.Type1.Furnace.Equipment.EquipmentType != null)
                    {
                        heatingCooling.Type1.Furnace.Equipment.SetEquipmentType(heatingCooling.Type1.Furnace.Equipment.EquipmentType);
                    }
                }

                // ComboHeatDhw equipment
                if (heatingCooling.Type1.ComboHeatDhw?.Equipment != null)
                {
                    heatingCooling.Type1.ComboHeatDhw.Equipment.LoadEquipmentTypeFromData();
                    if (heatingCooling.Type1.ComboHeatDhw.Equipment.EquipmentType != null)
                    {
                        heatingCooling.Type1.ComboHeatDhw.Equipment.SetEquipmentType(heatingCooling.Type1.ComboHeatDhw.Equipment.EquipmentType);
                    }
                }
            }

            // Populate Type2 heat pump functions
            if (heatingCooling.Type2 != null)
            {
                // Air Heat Pump function
                if (heatingCooling.Type2.AirHeatPump?.Equipment?.Function != null)
                {
                    // Function text is already populated by the mapping, no additional action needed
                    // The mapping uses CreateHeatPumpFunctionFromDto which gets the correct HeatPumpFunctions object
                }

                // Water Heat Pump function
                if (heatingCooling.Type2.WaterHeatPump?.Equipment?.Function != null)
                {
                    // Function text is already populated by the mapping
                }

                // Ground Heat Pump function
                if (heatingCooling.Type2.GroundHeatPump?.Equipment?.Function != null)
                {
                    // Function text is already populated by the mapping
                }
            }
        }

        // GET: api/heating-cooling/energy-upgrades/{energyUpgradeId}/heating-coolings
        [HttpGet("energy-upgrades/{energyUpgradeId}/heating-coolings")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<ActionResult<HeatingCoolingDto>> GetHeatingCoolingsByEnergyUpgradeId(Guid energyUpgradeId)
        {
            try
            {
                _logger.LogInformation("Getting heating cooling by energy upgrade ID: {EnergyUpgradeId}", energyUpgradeId);

                var heatingCooling = await _heatingCoolingService.GetHeatingCoolingByEnergyUpgradeIdAsync(energyUpgradeId);

                if (heatingCooling == null)
                {
                    return NotFound(new { Error = "Heating cooling not found for energy upgrade", EnergyUpgradeId = energyUpgradeId });
                }

                var heatingCoolingDto = _mapper.Map<HeatingCoolingDto>(heatingCooling);
                PopulateEquipmentTypeText(heatingCooling);
                return Ok(heatingCoolingDto);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting heating cooling by energy upgrade ID: {EnergyUpgradeId}", energyUpgradeId);
                return StatusCode(500, new { Error = "Internal server error" });
            }
        }

        // POST: api/heating-cooling/{baseHeatingCoolingId}/duplicate-for-energy-upgrade
        [HttpPost("{baseHeatingCoolingId}/duplicate-for-energy-upgrade")]
        [ProducesResponseType(StatusCodes.Status201Created)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<ActionResult<HeatingCoolingDto>> DuplicateHeatingCoolingForEnergyUpgrade(
            Guid baseHeatingCoolingId,
            [FromQuery] Guid energyUpgradeId)
        {
            try
            {
                _logger.LogInformation("Duplicating heating cooling {BaseHeatingCoolingId} for energy upgrade {EnergyUpgradeId}",
                    baseHeatingCoolingId, energyUpgradeId);

                var baseHeatingCooling = await _heatingCoolingService.GetHeatingCoolingByIdAsync(baseHeatingCoolingId);

                if (baseHeatingCooling == null)
                {
                    return NotFound(new { Error = "Base heating cooling not found", BaseHeatingCoolingId = baseHeatingCoolingId });
                }

                var duplicatedHeatingCooling = await _heatingCoolingService.DuplicateHeatingCoolingForEnergyUpgradeAsync(
                    baseHeatingCooling, energyUpgradeId);

                var heatingCoolingDto = _mapper.Map<HeatingCoolingDto>(duplicatedHeatingCooling);

                return CreatedAtAction(
                    nameof(GetHeatingCoolingById),
                    new { id = heatingCoolingDto.Id },
                    heatingCoolingDto);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error duplicating heating cooling {BaseHeatingCoolingId} for energy upgrade {EnergyUpgradeId}",
                    baseHeatingCoolingId, energyUpgradeId);
                return StatusCode(500, new { Error = "Internal server error" });
            }
        }
    }
}
