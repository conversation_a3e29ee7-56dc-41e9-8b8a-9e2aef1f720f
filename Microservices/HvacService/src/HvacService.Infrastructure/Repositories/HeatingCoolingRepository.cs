using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using HvacService.Core.Interfaces;
using HvacService.Core.Models;
using HvacService.Infrastructure.Data;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using System.Transactions;

namespace HvacService.Infrastructure.Repositories
{
    public class HeatingCoolingRepository : IHeatingCoolingRepository
    {
        private readonly HvacDbContext _context;
        private readonly ILogger<HeatingCoolingRepository> _logger;

        public HeatingCoolingRepository(
            HvacDbContext context,
            ILogger<HeatingCoolingRepository> logger)
        {
            _context = context;
            _logger = logger;
        }

        public async Task<IEnumerable<HeatingCooling>> GetAllHeatingCoolingsAsync()
        {
            _logger.LogInformation("Getting all heating cooling systems from repository");
            return await GetHeatingCoolingWithIncludes().ToListAsync();
        }

        public async Task<HeatingCooling?> GetHeatingCoolingByHouseIdAsync(Guid houseId)
        {
            _logger.LogInformation("Getting heating cooling system for house ID: {HouseId} from repository", houseId);
            return await GetHeatingCoolingWithIncludes()
                .FirstOrDefaultAsync(h => h.HouseId == houseId);
        }

        public async Task<HeatingCooling?> GetHeatingCoolingByIdAsync(Guid id)
        {
            _logger.LogInformation("Getting heating cooling system with ID: {Id} from repository", id);
            return await GetHeatingCoolingWithIncludes()
                .FirstOrDefaultAsync(h => h.Id == id);
        }

        public async Task<HeatingCooling> CreateHeatingCoolingAsync(HeatingCooling heatingCooling)
        {
            _logger.LogInformation("Creating new heating cooling system for house ID: {HouseId} from repository", heatingCooling.HouseId);

            // Clear any existing tracking to prevent conflicts
            _context.ChangeTracker.Clear();

            // Apply selective creation logic - only create systems that are selected
            await ApplySelectiveCreationLogicAsync(heatingCooling);

            _context.HeatingCoolings.Add(heatingCooling);
            await _context.SaveChangesAsync();

            _logger.LogInformation("Successfully created heating cooling system with ID: {Id}", heatingCooling.Id);
            return heatingCooling;
        }
        public async Task DeleteHeatingCoolingAsync(Guid id)
        {
            _logger.LogInformation("Deleting heating cooling system with ID: {Id} from repository", id);

            var heatingCooling = await GetHeatingCoolingWithIncludes()
                .FirstOrDefaultAsync(h => h.Id == id);

            if (heatingCooling != null)
            {
                _context.HeatingCoolings.Remove(heatingCooling);
                await _context.SaveChangesAsync();
            }
        }

        public async Task UpdateHeatingCoolingAsync(HeatingCooling heatingCooling)
        {
            _logger.LogInformation("Updating heating cooling system with ID: {Id} from repository", heatingCooling.Id);

            var strategy = _context.Database.CreateExecutionStrategy();

            await strategy.ExecuteAsync(async () =>
            {
                await using var transaction = await _context.Database.BeginTransactionAsync();

                try
                {
                    var existingEntity = await GetHeatingCoolingWithIncludes()
                        .FirstOrDefaultAsync(h => h.Id == heatingCooling.Id);

                    if (existingEntity == null)
                    {
                        throw new InvalidOperationException($"HeatingCooling with ID {heatingCooling.Id} not found");
                    }

                    _context.Entry(existingEntity).CurrentValues.SetValues(heatingCooling);

                    UpdateCoolingSeason(existingEntity, heatingCooling);
                    await UpdateType1SelectiveAsync(existingEntity, heatingCooling);
                    await UpdateType2SelectiveAsync(existingEntity, heatingCooling);
                    await UpdateRadiantHeatingSelectiveAsync(existingEntity, heatingCooling);

                    await UpdateCollectionsSelectiveAsync(existingEntity, heatingCooling);

                    // Save once and commit
                    await _context.SaveChangesAsync();
                    await transaction.CommitAsync();

                    _logger.LogInformation("Successfully updated heating cooling system with ID: {Id}", heatingCooling.Id);
                }
                catch (DbUpdateConcurrencyException ex)
                {
                    await transaction.RollbackAsync();
                    await HandleConcurrencyException(ex);
                }
                catch (Exception ex)
                {
                    await transaction.RollbackAsync();
                    _logger.LogError(ex, "Error updating heating cooling system with ID: {Id}", heatingCooling.Id);
                    throw;
                }
            });
        }
        private async Task HandleConcurrencyException(DbUpdateConcurrencyException ex)
        {
            foreach (var entry in ex.Entries)
            {
                var databaseValues = await entry.GetDatabaseValuesAsync();

                if (databaseValues == null)
                {
                    _logger.LogError("The entity was deleted by another process");
                    throw new InvalidOperationException("The entity was deleted by another process");
                }

                _logger.LogError("Concurrency conflict detected for entity {EntityName} with key {Key}",
                    entry.Entity.GetType().Name, entry.Entity);

                // Refresh the original values
                entry.OriginalValues.SetValues(databaseValues);
            }

            throw new InvalidOperationException("The entity was modified by another process. Please refresh and try again.");
        }


        private async Task UpdateCollectionsAsync(HeatingCooling existingEntity, HeatingCooling heatingCooling)
        {
            await UpdateMultipleSystemsEquipmentAsync(existingEntity, heatingCooling);

            // Update AdditionalOpenings collection
            await UpdateAdditionalOpeningsAsync(existingEntity, heatingCooling);

            // Update SupplementaryHeating collection
            await UpdateSupplementaryHeatingAsync(existingEntity, heatingCooling);

        }

        /// <summary>
        /// Selective update for collections - only saves data for enabled features
        /// Similar to the old system's logic based on flags like m_hasMultipleSystems, etc.
        /// </summary>
        private async Task UpdateCollectionsSelectiveAsync(HeatingCooling existingEntity, HeatingCooling heatingCooling)
        {
            // Only update MultipleSystems if enabled
            if (heatingCooling.HasMultipleSystems)
            {
                _logger.LogInformation("Updating multiple systems for house {HouseId}", heatingCooling.HouseId);

                // Create MultipleSystems if it doesn't exist
                if (existingEntity.MultipleSystems == null)
                {
                    existingEntity.MultipleSystems = new MultipleSystems
                    {
                        Id = Guid.NewGuid(),
                        HeatingCoolingId = existingEntity.Id
                    };
                    // Explicitly add to context as a new entity
                    _context.Set<MultipleSystems>().Add(existingEntity.MultipleSystems);
                }

                await UpdateMultipleSystemsEquipmentAsync(existingEntity, heatingCooling);
            }
            else
            {
                _logger.LogInformation("Multiple systems not enabled for house {HouseId}, clearing MultipleSystems", heatingCooling.HouseId);
                if (existingEntity.MultipleSystems != null)
                {
                    // Safely remove MultipleSystems and related equipment
                    await SafelyRemoveMultipleSystemsAsync(existingEntity.MultipleSystems);
                    existingEntity.MultipleSystems = null;
                }
            }

            // Only update AdditionalOpenings if enabled
            if (heatingCooling.HasAdditionalOpenings)
            {
                _logger.LogInformation("Updating additional openings for house {HouseId}", heatingCooling.HouseId);
                await UpdateAdditionalOpeningsAsync(existingEntity, heatingCooling);
            }
            else
            {
                _logger.LogInformation("Additional openings not enabled for house {HouseId}, clearing AdditionalOpenings", heatingCooling.HouseId);
                if (existingEntity.AdditionalOpenings != null && existingEntity.AdditionalOpenings.Any())
                {
                    var openingsToRemove = existingEntity.AdditionalOpenings.ToList();
                    foreach (var opening in openingsToRemove)
                    {
                        _context.Set<AdditionalOpening>().Remove(opening);
                    }
                    existingEntity.AdditionalOpenings.Clear();
                }
            }

            // Only update SupplementaryHeating if there are systems
            if (heatingCooling.SupplementarySystemsCount > 0)
            {
                _logger.LogInformation("Updating {Count} supplementary heating systems for house {HouseId}",
                    heatingCooling.SupplementarySystemsCount, heatingCooling.HouseId);
                await UpdateSupplementaryHeatingAsync(existingEntity, heatingCooling);
            }
            else
            {
                _logger.LogInformation("No supplementary heating systems for house {HouseId}, clearing SupplementaryHeating", heatingCooling.HouseId);
                if (existingEntity.SupplementaryHeating != null && existingEntity.SupplementaryHeating.Any())
                {
                    var systemsToRemove = existingEntity.SupplementaryHeating.ToList();
                    foreach (var system in systemsToRemove)
                    {
                        _context.Set<SupplementaryHeat>().Remove(system);
                    }
                    existingEntity.SupplementaryHeating.Clear();
                }
            }
        }


private async Task UpdateAdditionalOpeningsAsync(HeatingCooling existingEntity, HeatingCooling heatingCooling)
{
    var existingOpenings = await _context.Set<AdditionalOpening>()
        .Where(ao => ao.HeatingCoolingId == existingEntity.Id)
        .ToListAsync();

    var newOpenings = heatingCooling.AdditionalOpenings ?? new List<AdditionalOpening>();

    // Create a mapping of existing openings by ID for quick lookup
    var existingOpeningsDict = existingOpenings.ToDictionary(o => o.Id);

    // Track which existing openings should be kept (either updated or unchanged)
    var openingsToKeep = new HashSet<Guid>();

    // First pass: Update existing openings and track which ones to keep
    foreach (var newOpening in newOpenings)
    {
        if (newOpening.Id != Guid.Empty && existingOpeningsDict.ContainsKey(newOpening.Id))
        {
            openingsToKeep.Add(newOpening.Id);
            var existingOpening = existingOpeningsDict[newOpening.Id];

            // Update existing opening
            existingOpening.Code = newOpening.Code;
            existingOpening.EnglishText = newOpening.EnglishText;
            existingOpening.FrenchText = newOpening.FrenchText;
            existingOpening.Text = newOpening.Text;
            existingOpening.Rank = newOpening.Rank;
            existingOpening.FlueDiameter = newOpening.FlueDiameter;
            existingOpening.DamperClosed = newOpening.DamperClosed;
            existingOpening.NumberOfOpenings = newOpening.NumberOfOpenings;

            // Mark as modified
            _context.Entry(existingOpening).State = EntityState.Modified;
        }
    }

    // Remove openings that are no longer needed
    var openingsToRemove = existingOpenings.Where(existing =>
        !openingsToKeep.Contains(existing.Id)).ToList();

    if (openingsToRemove.Any())
    {
        _context.RemoveRange(openingsToRemove);
    }

    // Second pass: Add new openings (those with empty GUID or not found in existing)
    foreach (var newOpening in newOpenings)
    {
        if (newOpening.Id == Guid.Empty || !existingOpeningsDict.ContainsKey(newOpening.Id))
        {
            // Add new opening
            var opening = new AdditionalOpening
            {
                HeatingCoolingId = existingEntity.Id,
                Code = newOpening.Code,
                EnglishText = newOpening.EnglishText,
                FrenchText = newOpening.FrenchText,
                Text = newOpening.Text,
                Rank = newOpening.Rank,
                FlueDiameter = newOpening.FlueDiameter,
                DamperClosed = newOpening.DamperClosed,
                NumberOfOpenings = newOpening.NumberOfOpenings
            };

            // Only set Id if it's provided and not empty (for existing entities being moved)
            if (newOpening.Id != Guid.Empty)
            {
                opening.Id = newOpening.Id;
            }

            _context.Add(opening);
        }
    }
}

private async Task UpdateSupplementaryHeatingAsync(HeatingCooling existingEntity, HeatingCooling heatingCooling)
{
    var existingSupplementary = await _context.Set<SupplementaryHeat>()
        .Where(sh => sh.HeatingCoolingId == existingEntity.Id)
        .ToListAsync();

    var newSupplementary = heatingCooling.SupplementaryHeating ?? new List<SupplementaryHeat>();

    // Create a mapping of existing supplementary heating by ID for quick lookup
    var existingSupplementaryDict = existingSupplementary.ToDictionary(s => s.Id);

    // Track which existing supplementary heating should be kept (either updated or unchanged)
    var supplementaryToKeep = new HashSet<Guid>();

    // First pass: Update existing supplementary heating and track which ones to keep
    foreach (var newSupp in newSupplementary)
    {
        if (newSupp.Id != Guid.Empty && existingSupplementaryDict.ContainsKey(newSupp.Id))
        {
            supplementaryToKeep.Add(newSupp.Id);
            var existingSupp = existingSupplementaryDict[newSupp.Id];

            // Update existing supplementary heating - copy all properties
            UpdateSupplementaryHeatProperties(existingSupp, newSupp);

            // Mark as modified
            _context.Entry(existingSupp).State = EntityState.Modified;
        }
    }

    // Remove supplementary heating that are no longer needed
    var supplementaryToRemove = existingSupplementary.Where(existing =>
        !supplementaryToKeep.Contains(existing.Id)).ToList();

    if (supplementaryToRemove.Any())
    {
        _context.RemoveRange(supplementaryToRemove);
    }

    // Second pass: Add new supplementary heating (those with empty GUID or not found in existing)
    foreach (var newSupp in newSupplementary)
    {
        if (newSupp.Id == Guid.Empty || !existingSupplementaryDict.ContainsKey(newSupp.Id))
        {
            // Add new supplementary heating
            var supplementary = CreateSupplementaryHeat(newSupp, existingEntity.Id);
            _context.Add(supplementary);
        }
    }
}

private async Task UpdateMultipleSystemsEquipmentAsync(HeatingCooling existingEntity, HeatingCooling heatingCooling)
{
    // Handle MultipleSystems creation and properties
    if (heatingCooling.MultipleSystems != null)
    {
        if (existingEntity.MultipleSystems == null)
        {
            existingEntity.MultipleSystems = new MultipleSystems
            {
                Id = Guid.NewGuid(),
                HeatingCoolingId = existingEntity.Id
            };
        }

        // Update Summary
        if (heatingCooling.MultipleSystems.Summary != null)
        {
            if (existingEntity.MultipleSystems.Summary == null)
            {
                existingEntity.MultipleSystems.Summary = new MultipleSystemsSummary();
            }

            existingEntity.MultipleSystems.Summary.EnergySaverHeatingSystems = heatingCooling.MultipleSystems.Summary.EnergySaverHeatingSystems;
            existingEntity.MultipleSystems.Summary.EnergySaverAirSourceHeatPump = heatingCooling.MultipleSystems.Summary.EnergySaverAirSourceHeatPump;
            existingEntity.MultipleSystems.Summary.WoodAppliances = heatingCooling.MultipleSystems.Summary.WoodAppliances;
            existingEntity.MultipleSystems.Summary.EpaCsaHeatingSystems = heatingCooling.MultipleSystems.Summary.EpaCsaHeatingSystems;
        }
    }

    // Handle EquipmentInformation collection
    if (existingEntity.MultipleSystems != null)
    {
        var existingEquipment = await _context.Set<MultipleSystemsEquipment>()
            .Where(mse => mse.MultipleSystemsId == existingEntity.MultipleSystems.Id)
            .ToListAsync();

        var newEquipment = heatingCooling.MultipleSystems?.EquipmentInformation ?? new List<MultipleSystemsEquipment>();

        // Create a mapping of existing equipment by ID for quick lookup
        var existingEquipmentDict = existingEquipment.ToDictionary(e => e.Id);

        // Track which existing equipment should be kept (either updated or unchanged)
        var equipmentToKeep = new HashSet<Guid>();

        // First pass: Update existing equipment and track which ones to keep
        foreach (var newEquipInfo in newEquipment)
        {
            if (newEquipInfo.Id != Guid.Empty && existingEquipmentDict.ContainsKey(newEquipInfo.Id))
            {
                equipmentToKeep.Add(newEquipInfo.Id);
                var existingEquip = existingEquipmentDict[newEquipInfo.Id];

                // Update existing equipment
                existingEquip.Rank = newEquipInfo.Rank;
                existingEquip.Efficiency = newEquipInfo.Efficiency;
                existingEquip.FlueDiameter = newEquipInfo.FlueDiameter;
                existingEquip.HeatingCapacitykW = newEquipInfo.HeatingCapacitykW;
                existingEquip.HeatingCapacityUiUnits = newEquipInfo.HeatingCapacityUiUnits;
                existingEquip.PilotLight = newEquipInfo.PilotLight;
                existingEquip.EnergyStar = newEquipInfo.EnergyStar;
                existingEquip.EnergyEfficientMotor = newEquipInfo.EnergyEfficientMotor;
                existingEquip.IdenticalSystems = newEquipInfo.IdenticalSystems;
                existingEquip.EnergySource = newEquipInfo.EnergySource;
                existingEquip.EfficiencyType = newEquipInfo.EfficiencyType;
                existingEquip.EquipmentTypeData = newEquipInfo.EquipmentTypeData;
                existingEquip.Manufacturer = newEquipInfo.Manufacturer;
                existingEquip.Model = newEquipInfo.Model;

                // Mark as modified
                _context.Entry(existingEquip).State = EntityState.Modified;
            }
        }

        // Remove equipment that are no longer needed
        var equipmentToRemove = existingEquipment.Where(existing =>
            !equipmentToKeep.Contains(existing.Id)).ToList();

        if (equipmentToRemove.Any())
        {
            _context.RemoveRange(equipmentToRemove);
        }

        // Second pass: Add new equipment (those with empty GUID or not found in existing)
        foreach (var newEquipInfo in newEquipment)
        {
            if (newEquipInfo.Id == Guid.Empty || !existingEquipmentDict.ContainsKey(newEquipInfo.Id))
            {
                // Add new equipment
                var equipInfo = new MultipleSystemsEquipment
                {
                    MultipleSystemsId = existingEntity.MultipleSystems.Id,
                    Rank = newEquipInfo.Rank,
                    Efficiency = newEquipInfo.Efficiency,
                    FlueDiameter = newEquipInfo.FlueDiameter,
                    HeatingCapacitykW = newEquipInfo.HeatingCapacitykW,
                    HeatingCapacityUiUnits = newEquipInfo.HeatingCapacityUiUnits,
                    PilotLight = newEquipInfo.PilotLight,
                    EnergyStar = newEquipInfo.EnergyStar,
                    EnergyEfficientMotor = newEquipInfo.EnergyEfficientMotor,
                    IdenticalSystems = newEquipInfo.IdenticalSystems,
                    EnergySource = newEquipInfo.EnergySource,
                    EfficiencyType = newEquipInfo.EfficiencyType,
                    EquipmentTypeData = newEquipInfo.EquipmentTypeData,
                    Manufacturer = newEquipInfo.Manufacturer,
                    Model = newEquipInfo.Model
                };

                // Only set Id if it's provided and not empty (for existing entities being moved)
                if (newEquipInfo.Id != Guid.Empty)
                {
                    equipInfo.Id = newEquipInfo.Id;
                }

                _context.Add(equipInfo);
            }
        }
    }
}

private void UpdateSupplementaryHeatProperties(SupplementaryHeat existing, SupplementaryHeat newSupp)
{
    existing.Rank = newSupp.Rank;

    // Update Equipment
    if (existing.Equipment == null) existing.Equipment = new SupplementaryHeatEquipment();
    existing.Equipment.EnergySource = newSupp.Equipment?.EnergySource ?? GetValidEnergySource();
    existing.Equipment.TypeData = newSupp.Equipment?.TypeData ?? new CodeAndText();

    // Update EquipmentInformation
    if (existing.EquipmentInformation == null) existing.EquipmentInformation = new SupplementaryHeatEquipmentInformation();
    existing.EquipmentInformation.Manufacturer = newSupp.EquipmentInformation?.Manufacturer;
    existing.EquipmentInformation.Model = newSupp.EquipmentInformation?.Model;
    existing.EquipmentInformation.Description = newSupp.EquipmentInformation?.Description;
    existing.EquipmentInformation.CsaEpa = newSupp.EquipmentInformation?.CsaEpa ?? false;

    // Update Specifications
    if (existing.Specifications == null) existing.Specifications = new SupplementaryHeatSpecifications();
    existing.Specifications.Efficiency = newSupp.Specifications?.Efficiency ?? 0m;
    existing.Specifications.PilotLight = newSupp.Specifications?.PilotLight ?? 0m;
    existing.Specifications.DamperClosed = newSupp.Specifications?.DamperClosed ?? false;
    existing.Specifications.YearMade = newSupp.Specifications?.YearMade ?? 0;
    existing.Specifications.LocationHeated = newSupp.Specifications?.LocationHeated ?? GetValidHeatingLocation();
    existing.Specifications.Usage = newSupp.Specifications?.Usage ?? GetValidHeatingUsage();
    existing.Specifications.OutputCapacity = newSupp.Specifications?.OutputCapacity;
    existing.Specifications.MonthlyUsage = newSupp.Specifications?.MonthlyUsage;
    existing.Specifications.Flue = newSupp.Specifications?.Flue;
}

private SupplementaryHeat CreateSupplementaryHeat(SupplementaryHeat newSupp, Guid heatingCoolingId)
{
    var supplementary = new SupplementaryHeat
    {
        HeatingCoolingId = heatingCoolingId,
        Rank = newSupp.Rank
    };

    // Only set Id if it's provided and not empty (for existing entities being moved)
    if (newSupp.Id != Guid.Empty)
    {
        supplementary.Id = newSupp.Id;
    }

    // Add EquipmentInformation
    if (newSupp.EquipmentInformation != null)
    {
        supplementary.EquipmentInformation = new SupplementaryHeatEquipmentInformation
        {
            Manufacturer = newSupp.EquipmentInformation.Manufacturer,
            Model = newSupp.EquipmentInformation.Model,
            Description = newSupp.EquipmentInformation.Description,
            CsaEpa = newSupp.EquipmentInformation.CsaEpa
        };
    }

    // Add Equipment - ensure it's never null and has valid EnergySource
    supplementary.Equipment = new SupplementaryHeatEquipment();

    if (newSupp.Equipment != null)
    {
        supplementary.Equipment.EnergySource = newSupp.Equipment.EnergySource ?? GetValidEnergySource();
        supplementary.Equipment.TypeData = newSupp.Equipment.TypeData ?? new CodeAndText();
    }
    else
    {
        supplementary.Equipment.EnergySource = GetValidEnergySource();
        supplementary.Equipment.TypeData = new CodeAndText();
    }

    // Add Specifications - ensure it's never null
    if (newSupp.Specifications != null)
    {
        supplementary.Specifications = CreateSupplementaryHeatSpecifications(newSupp.Specifications);
    }
    else
    {
        // Create default specifications if none provided
        supplementary.Specifications = new SupplementaryHeatSpecifications
        {
            Efficiency = 80.0m,
            PilotLight = 0.0m,
            DamperClosed = false,
            YearMade = 0,
            Usage = GetValidHeatingUsage(),
            LocationHeated = GetValidHeatingLocation()
        };
    }

    return supplementary;
}

        private SupplementaryHeatSpecifications CreateSupplementaryHeatSpecifications(SupplementaryHeatSpecifications newSpecs)
        {
            // Ensure we always have valid Usage and LocationHeated
            var validUsage = newSpecs.Usage ?? GetValidHeatingUsage();
            var validLocationHeated = newSpecs.LocationHeated ?? GetValidHeatingLocation();

            var specifications = new SupplementaryHeatSpecifications
            {
                Efficiency = newSpecs.Efficiency,
                PilotLight = newSpecs.PilotLight,
                DamperClosed = newSpecs.DamperClosed,
                YearMade = newSpecs.YearMade,
                Usage = validUsage,
                LocationHeated = validLocationHeated
            };

            // Add nested objects
            if (newSpecs.Flue != null)
            {
                specifications.Flue = new Flue
                {
                    IsInterior = newSpecs.Flue.IsInterior,
                    Diameter = newSpecs.Flue.Diameter,
                    Area = newSpecs.Flue.Area,
                    Type = newSpecs.Flue.Type
                };
            }

            if (newSpecs.MonthlyUsage != null)
            {
                specifications.MonthlyUsage = new MonthlyData
                {
                    January = newSpecs.MonthlyUsage.January,
                    February = newSpecs.MonthlyUsage.February,
                    March = newSpecs.MonthlyUsage.March,
                    April = newSpecs.MonthlyUsage.April,
                    May = newSpecs.MonthlyUsage.May,
                    June = newSpecs.MonthlyUsage.June,
                    July = newSpecs.MonthlyUsage.July,
                    August = newSpecs.MonthlyUsage.August,
                    September = newSpecs.MonthlyUsage.September,
                    October = newSpecs.MonthlyUsage.October,
                    November = newSpecs.MonthlyUsage.November,
                    December = newSpecs.MonthlyUsage.December
                };
            }

            if (newSpecs.OutputCapacity != null)
            {
                specifications.OutputCapacity = new OutputCapacity
                {
                    Code = newSpecs.OutputCapacity.Code,
                    EnglishText = newSpecs.OutputCapacity.EnglishText,
                    FrenchText = newSpecs.OutputCapacity.FrenchText,
                    Text = newSpecs.OutputCapacity.Text,
                    Value = newSpecs.OutputCapacity.Value,
                    UiUnits = newSpecs.OutputCapacity.UiUnits,
                    ValueInUiUnits = newSpecs.OutputCapacity.ValueInUiUnits
                };
            }

            return specifications;
        }

private void UpdateCoolingSeason(HeatingCooling existingEntity, HeatingCooling heatingCooling)
{
    if (heatingCooling.CoolingSeason == null)
        return;

    if (existingEntity.CoolingSeason == null)
    {
        existingEntity.CoolingSeason = new CoolingSeason
        {
            Id = Guid.NewGuid(),
            HeatingCoolingId = existingEntity.Id
        };
    }

    // Update Start month
    if (heatingCooling.CoolingSeason.Start != null)
    {
        if (existingEntity.CoolingSeason.Start == null)
        {
            existingEntity.CoolingSeason.Start = new Months();
        }
        existingEntity.CoolingSeason.Start.Code = heatingCooling.CoolingSeason.Start.Code;
        existingEntity.CoolingSeason.Start.English = heatingCooling.CoolingSeason.Start.English;
        existingEntity.CoolingSeason.Start.French = heatingCooling.CoolingSeason.Start.French;
        existingEntity.CoolingSeason.Start.IsUserSpecified = heatingCooling.CoolingSeason.Start.IsUserSpecified;
    }

    // Update End month
    if (heatingCooling.CoolingSeason.End != null)
    {
        if (existingEntity.CoolingSeason.End == null)
        {
            existingEntity.CoolingSeason.End = new Months();
        }
        existingEntity.CoolingSeason.End.Code = heatingCooling.CoolingSeason.End.Code;
        existingEntity.CoolingSeason.End.English = heatingCooling.CoolingSeason.End.English;
        existingEntity.CoolingSeason.End.French = heatingCooling.CoolingSeason.End.French;
        existingEntity.CoolingSeason.End.IsUserSpecified = heatingCooling.CoolingSeason.End.IsUserSpecified;
    }

    // Update Design month
    if (heatingCooling.CoolingSeason.Design != null)
    {
        if (existingEntity.CoolingSeason.Design == null)
        {
            existingEntity.CoolingSeason.Design = new Months();
        }
        existingEntity.CoolingSeason.Design.Code = heatingCooling.CoolingSeason.Design.Code;
        existingEntity.CoolingSeason.Design.English = heatingCooling.CoolingSeason.Design.English;
        existingEntity.CoolingSeason.Design.French = heatingCooling.CoolingSeason.Design.French;
        existingEntity.CoolingSeason.Design.IsUserSpecified = heatingCooling.CoolingSeason.Design.IsUserSpecified;
    }
}

private void UpdateType1(HeatingCooling existingEntity, HeatingCooling heatingCooling)
{
    if (heatingCooling.Type1 == null)
        return;

    if (existingEntity.Type1 == null)
    {
        existingEntity.Type1 = new Type1
        {
            Id = Guid.NewGuid(),
            HeatingCoolingId = existingEntity.Id
        };
    }

    // Update FansAndPump
    UpdateFansAndPump(existingEntity.Type1, heatingCooling.Type1);

    // Update Baseboards
    UpdateBaseboards(existingEntity.Type1, heatingCooling.Type1);

    // Update Boiler
    UpdateBoiler(existingEntity.Type1, heatingCooling.Type1);

    // Update ComboHeatDhw
    UpdateComboHeatDhw(existingEntity.Type1, heatingCooling.Type1);

    // Update Furnace
    UpdateFurnace(existingEntity.Type1, heatingCooling.Type1);

    // Update P9
    UpdateP9(existingEntity.Type1, heatingCooling.Type1);
}

/// <summary>
/// Selective update for Type1 - only saves data for the selected heating system type
/// Similar to the old system's switch statement based on m_heatType
/// </summary>
private async Task UpdateType1SelectiveAsync(HeatingCooling existingEntity, HeatingCooling heatingCooling)
{
    if (heatingCooling.Type1 == null)
        return;

    if (existingEntity.Type1 == null)
    {
        existingEntity.Type1 = new Type1
        {
            Id = Guid.NewGuid(),
            HeatingCoolingId = existingEntity.Id
        };
    }

    // Always update FansAndPump (needed for all systems)
    UpdateFansAndPump(existingEntity.Type1, heatingCooling.Type1);

    // Only update the selected system type based on HeatType
    var selectedSystemType = heatingCooling.GetSelectedType1System();

    switch (selectedSystemType)
    {
        case Type1SystemType.Baseboards:
            _logger.LogInformation("Updating Baseboards system for house {HouseId}", heatingCooling.HouseId);
            // Clear other systems first
            ClearUnselectedType1SystemsInMemory(existingEntity.Type1, selectedSystemType);
            UpdateBaseboards(existingEntity.Type1, heatingCooling.Type1);
            break;

        case Type1SystemType.Furnace:
            _logger.LogInformation("Updating Furnace system for house {HouseId}", heatingCooling.HouseId);
            ClearUnselectedType1SystemsInMemory(existingEntity.Type1, selectedSystemType);
            UpdateFurnace(existingEntity.Type1, heatingCooling.Type1);
            break;

        case Type1SystemType.Boiler:
            _logger.LogInformation("Updating Boiler system for house {HouseId}", heatingCooling.HouseId);
            ClearUnselectedType1SystemsInMemory(existingEntity.Type1, selectedSystemType);
            UpdateBoiler(existingEntity.Type1, heatingCooling.Type1);
            break;

        case Type1SystemType.ComboHeatDhw:
            _logger.LogInformation("Updating ComboHeatDhw system for house {HouseId}", heatingCooling.HouseId);
            ClearUnselectedType1SystemsInMemory(existingEntity.Type1, selectedSystemType);
            UpdateComboHeatDhw(existingEntity.Type1, heatingCooling.Type1);
            break;

        case Type1SystemType.P9:
            _logger.LogInformation("Updating P9 system for house {HouseId}", heatingCooling.HouseId);
            ClearUnselectedType1SystemsInMemory(existingEntity.Type1, selectedSystemType);
            UpdateP9(existingEntity.Type1, heatingCooling.Type1);
            break;

        case Type1SystemType.IMS:
            _logger.LogInformation("Updating IMS system for house {HouseId}", heatingCooling.HouseId);
            ClearUnselectedType1SystemsInMemory(existingEntity.Type1, selectedSystemType);
            // IMS would be handled here when implemented
            break;

        case Type1SystemType.None:
        default:
            _logger.LogInformation("No Type1 system selected for house {HouseId}", heatingCooling.HouseId);
            ClearUnselectedType1SystemsInMemory(existingEntity.Type1, selectedSystemType);
            break;
    }
}

/// <summary>
/// Clear unselected Type1 systems in memory - sets them to null so only the selected one gets saved
/// This approach avoids entity removal conflicts during updates
/// </summary>
private void ClearUnselectedType1SystemsInMemory(Type1 type1, Type1SystemType selectedType)
{
    // Set unselected systems to null - they won't be updated
    if (selectedType != Type1SystemType.Baseboards)
        type1.Baseboards = null;

    if (selectedType != Type1SystemType.Furnace)
        type1.Furnace = null;

    if (selectedType != Type1SystemType.Boiler)
        type1.Boiler = null;

    if (selectedType != Type1SystemType.ComboHeatDhw)
        type1.ComboHeatDhw = null;

    if (selectedType != Type1SystemType.P9)
        type1.P9 = null;

    // IMS would be added here when implemented
}

private void UpdateFansAndPump(Type1 existingType1, Type1 newType1)
{
    if (newType1.FansAndPump == null)
        return;

    if (existingType1.FansAndPump == null)
    {
        existingType1.FansAndPump = new FansAndPumpsHeating
        {
            Id = Guid.NewGuid(),
            Type1Id = existingType1.Id
        };
    }

    // Update FansAndPump properties
    existingType1.FansAndPump.HasEnergyEfficientMotor = newType1.FansAndPump.HasEnergyEfficientMotor;

    // Update Mode
    if (newType1.FansAndPump.Mode != null)
    {
        if (existingType1.FansAndPump.Mode == null)
        {
            existingType1.FansAndPump.Mode = new HeatingFanModes();
        }
        existingType1.FansAndPump.Mode.Code = newType1.FansAndPump.Mode.Code;
        existingType1.FansAndPump.Mode.English = newType1.FansAndPump.Mode.English;
        existingType1.FansAndPump.Mode.French = newType1.FansAndPump.Mode.French;
        existingType1.FansAndPump.Mode.IsUserSpecified = newType1.FansAndPump.Mode.IsUserSpecified;
    }

    // Update Power
    if (newType1.FansAndPump.Power != null)
    {
        if (existingType1.FansAndPump.Power == null)
        {
            existingType1.FansAndPump.Power = new FansAndPumpPowerHeating();
        }
        existingType1.FansAndPump.Power.IsCalculated = newType1.FansAndPump.Power.IsCalculated;
        existingType1.FansAndPump.Power.Low = newType1.FansAndPump.Power.Low;
        existingType1.FansAndPump.Power.High = newType1.FansAndPump.Power.High;
    }
}

private void UpdateBaseboards(Type1 existingType1, Type1 newType1)
{
    if (newType1.Baseboards == null)
        return;

    if (existingType1.Baseboards == null)
    {
        existingType1.Baseboards = new Baseboards
        {
            Id = Guid.NewGuid(),
            Type1Id = existingType1.Id
        };
        // Explicitly add to context as a new entity
        _context.Set<Baseboards>().Add(existingType1.Baseboards);
    }

    // Update EquipmentInformation
    if (newType1.Baseboards.EquipmentInformation != null)
    {
        if (existingType1.Baseboards.EquipmentInformation == null)
        {
            existingType1.Baseboards.EquipmentInformation = new BaseboardsEquipmentInformation();
        }

        existingType1.Baseboards.EquipmentInformation.Manufacturer = newType1.Baseboards.EquipmentInformation.Manufacturer;
        existingType1.Baseboards.EquipmentInformation.Model = newType1.Baseboards.EquipmentInformation.Model;
        existingType1.Baseboards.EquipmentInformation.Description = newType1.Baseboards.EquipmentInformation.Description;
        existingType1.Baseboards.EquipmentInformation.NumberOfElectronicThermostats = newType1.Baseboards.EquipmentInformation.NumberOfElectronicThermostats;
    }

    // Update Specifications
    if (newType1.Baseboards.Specifications != null)
    {
        if (existingType1.Baseboards.Specifications == null)
        {
            existingType1.Baseboards.Specifications = new BaseboardsSpecifications();
        }

        existingType1.Baseboards.Specifications.SizingFactor = newType1.Baseboards.Specifications.SizingFactor;
        existingType1.Baseboards.Specifications.Efficiency = newType1.Baseboards.Specifications.Efficiency;

        // Update OutputCapacity
        if (newType1.Baseboards.Specifications.OutputCapacity != null)
        {
            if (existingType1.Baseboards.Specifications.OutputCapacity == null)
            {
                existingType1.Baseboards.Specifications.OutputCapacity = new OutputCapacity();
            }

            existingType1.Baseboards.Specifications.OutputCapacity.Code = newType1.Baseboards.Specifications.OutputCapacity.Code;
            existingType1.Baseboards.Specifications.OutputCapacity.EnglishText = newType1.Baseboards.Specifications.OutputCapacity.EnglishText;
            existingType1.Baseboards.Specifications.OutputCapacity.FrenchText = newType1.Baseboards.Specifications.OutputCapacity.FrenchText;
            existingType1.Baseboards.Specifications.OutputCapacity.Text = newType1.Baseboards.Specifications.OutputCapacity.Text;
            existingType1.Baseboards.Specifications.OutputCapacity.Value = newType1.Baseboards.Specifications.OutputCapacity.Value;
            existingType1.Baseboards.Specifications.OutputCapacity.UiUnits = newType1.Baseboards.Specifications.OutputCapacity.UiUnits;
            existingType1.Baseboards.Specifications.OutputCapacity.ValueInUiUnits = newType1.Baseboards.Specifications.OutputCapacity.ValueInUiUnits;
        }
    }
}

private void UpdateBoiler(Type1 existingType1, Type1 newType1)
{
    if (newType1.Boiler == null)
        return;

    if (existingType1.Boiler == null)
    {
        existingType1.Boiler = new Boiler
        {
            Id = Guid.NewGuid(),
            Type1Id = existingType1.Id
        };
        // Explicitly add to context as a new entity
        _context.Set<Boiler>().Add(existingType1.Boiler);
    }

    // Update Type1EquipmentInformation
    if (newType1.Boiler.Type1EquipmentInformation != null)
    {
        if (existingType1.Boiler.Type1EquipmentInformation == null)
        {
            existingType1.Boiler.Type1EquipmentInformation = new Type1EquipmentInformation();
        }

        existingType1.Boiler.Type1EquipmentInformation.Manufacturer = newType1.Boiler.Type1EquipmentInformation.Manufacturer;
        existingType1.Boiler.Type1EquipmentInformation.Model = newType1.Boiler.Type1EquipmentInformation.Model;
        existingType1.Boiler.Type1EquipmentInformation.Description = newType1.Boiler.Type1EquipmentInformation.Description;
        existingType1.Boiler.Type1EquipmentInformation.EnergyStar = newType1.Boiler.Type1EquipmentInformation.EnergyStar;
        existingType1.Boiler.Type1EquipmentInformation.AHRI = newType1.Boiler.Type1EquipmentInformation.AHRI;
        existingType1.Boiler.Type1EquipmentInformation.EpaCsa = newType1.Boiler.Type1EquipmentInformation.EpaCsa;
    }

    // Update Specifications
    if (newType1.Boiler.Specifications != null)
    {
        if (existingType1.Boiler.Specifications == null)
        {
            existingType1.Boiler.Specifications = new CommonSpecifications();
        }

        existingType1.Boiler.Specifications.SizingFactor = newType1.Boiler.Specifications.SizingFactor;
        existingType1.Boiler.Specifications.Efficiency = newType1.Boiler.Specifications.Efficiency;
        existingType1.Boiler.Specifications.IsSteadyState = newType1.Boiler.Specifications.IsSteadyState;
        existingType1.Boiler.Specifications.PilotLight = newType1.Boiler.Specifications.PilotLight;
        existingType1.Boiler.Specifications.FlueDiameter = newType1.Boiler.Specifications.FlueDiameter;

        // Update OutputCapacity
        if (newType1.Boiler.Specifications.OutputCapacity != null)
        {
            if (existingType1.Boiler.Specifications.OutputCapacity == null)
            {
                existingType1.Boiler.Specifications.OutputCapacity = new OutputCapacity();
            }

            existingType1.Boiler.Specifications.OutputCapacity.Code = newType1.Boiler.Specifications.OutputCapacity.Code;
            existingType1.Boiler.Specifications.OutputCapacity.EnglishText = newType1.Boiler.Specifications.OutputCapacity.EnglishText;
            existingType1.Boiler.Specifications.OutputCapacity.FrenchText = newType1.Boiler.Specifications.OutputCapacity.FrenchText;
            existingType1.Boiler.Specifications.OutputCapacity.Text = newType1.Boiler.Specifications.OutputCapacity.Text;
            existingType1.Boiler.Specifications.OutputCapacity.Value = newType1.Boiler.Specifications.OutputCapacity.Value;
            existingType1.Boiler.Specifications.OutputCapacity.UiUnits = newType1.Boiler.Specifications.OutputCapacity.UiUnits;
            existingType1.Boiler.Specifications.OutputCapacity.ValueInUiUnits = newType1.Boiler.Specifications.OutputCapacity.ValueInUiUnits;
        }
    }

    // Update ComboTankAndPump
    UpdateComboTankAndPump(existingType1.Boiler, newType1.Boiler);

    // Update Equipment
    UpdateBoilerEquipment(existingType1.Boiler, newType1.Boiler);
}

private void UpdateComboTankAndPump(dynamic existingParent, dynamic newParent)
{
    if (newParent.ComboTankAndPump == null)
        return;

    if (existingParent.ComboTankAndPump == null)
    {
        existingParent.ComboTankAndPump = new ComboTankAndPump();
    }

    existingParent.ComboTankAndPump.WaterTemperature = newParent.ComboTankAndPump.WaterTemperature;
    existingParent.ComboTankAndPump.TankCapacity = newParent.ComboTankAndPump.TankCapacity;

    // Update EnergyFactor
    if (newParent.ComboTankAndPump.EnergyFactor != null)
    {
        if (existingParent.ComboTankAndPump.EnergyFactor == null)
        {
            existingParent.ComboTankAndPump.EnergyFactor = new ComboEnergyFactor();
        }

        existingParent.ComboTankAndPump.EnergyFactor.UseDefaults = newParent.ComboTankAndPump.EnergyFactor.UseDefaults;
        existingParent.ComboTankAndPump.EnergyFactor.Value = newParent.ComboTankAndPump.EnergyFactor.Value;
    }

    // Update TankLocation
    if (newParent.ComboTankAndPump.TankLocation != null)
    {
        if (existingParent.ComboTankAndPump.TankLocation == null)
        {
            existingParent.ComboTankAndPump.TankLocation = new CodeAndText();
        }

        existingParent.ComboTankAndPump.TankLocation.Code = newParent.ComboTankAndPump.TankLocation.Code;
        existingParent.ComboTankAndPump.TankLocation.EnglishText = newParent.ComboTankAndPump.TankLocation.EnglishText;
        existingParent.ComboTankAndPump.TankLocation.FrenchText = newParent.ComboTankAndPump.TankLocation.FrenchText;
        existingParent.ComboTankAndPump.TankLocation.Text = newParent.ComboTankAndPump.TankLocation.Text;
    }

    // Update CirculationPump
    if (newParent.ComboTankAndPump.CirculationPump != null)
    {
        if (existingParent.ComboTankAndPump.CirculationPump == null)
        {
            existingParent.ComboTankAndPump.CirculationPump = new CirculationPump();
        }

        existingParent.ComboTankAndPump.CirculationPump.IsCalculated = newParent.ComboTankAndPump.CirculationPump.IsCalculated;
        existingParent.ComboTankAndPump.CirculationPump.Value = newParent.ComboTankAndPump.CirculationPump.Value;
        existingParent.ComboTankAndPump.CirculationPump.HasEnergyEfficientMotor = newParent.ComboTankAndPump.CirculationPump.HasEnergyEfficientMotor;
    }
}

private void UpdateBoilerEquipment(Boiler existingBoiler, Boiler newBoiler)
{
    if (newBoiler.Equipment == null)
        return;

    if (existingBoiler.Equipment == null)
    {
        existingBoiler.Equipment = new BoilerEquipment();
    }

    existingBoiler.Equipment.IsBiEnergy = newBoiler.Equipment.IsBiEnergy;
    existingBoiler.Equipment.SwitchoverTemperature = newBoiler.Equipment.SwitchoverTemperature;

    // Update EnergySource
    if (newBoiler.Equipment.EnergySource != null)
    {
        if (existingBoiler.Equipment.EnergySource == null)
        {
            existingBoiler.Equipment.EnergySource = new HeatingEnergySources();
        }

        existingBoiler.Equipment.EnergySource.Code = newBoiler.Equipment.EnergySource.Code;
        existingBoiler.Equipment.EnergySource.English = newBoiler.Equipment.EnergySource.English;
        existingBoiler.Equipment.EnergySource.French = newBoiler.Equipment.EnergySource.French;
        existingBoiler.Equipment.EnergySource.IsUserSpecified = newBoiler.Equipment.EnergySource.IsUserSpecified;
    }

    // Update EquipmentTypeData
    if (newBoiler.Equipment.EquipmentTypeData != null)
    {
        if (existingBoiler.Equipment.EquipmentTypeData == null)
        {
            existingBoiler.Equipment.EquipmentTypeData = new CodeAndText();
        }

        existingBoiler.Equipment.EquipmentTypeData.Code = newBoiler.Equipment.EquipmentTypeData.Code;
        existingBoiler.Equipment.EquipmentTypeData.EnglishText = newBoiler.Equipment.EquipmentTypeData.EnglishText;
        existingBoiler.Equipment.EquipmentTypeData.FrenchText = newBoiler.Equipment.EquipmentTypeData.FrenchText;
        existingBoiler.Equipment.EquipmentTypeData.Text = newBoiler.Equipment.EquipmentTypeData.Text;
    }
}

private void UpdateComboHeatDhw(Type1 existingType1, Type1 newType1)
{
    if (newType1.ComboHeatDhw == null)
        return;

    if (existingType1.ComboHeatDhw == null)
    {
        existingType1.ComboHeatDhw = new ComboHeatDhw
        {
            Id = Guid.NewGuid(),
            Type1Id = existingType1.Id
        };
        // Explicitly add to context as a new entity
        _context.Set<ComboHeatDhw>().Add(existingType1.ComboHeatDhw);
    }

    // Update Type1EquipmentInformation
    if (newType1.ComboHeatDhw.Type1EquipmentInformation != null)
    {
        if (existingType1.ComboHeatDhw.Type1EquipmentInformation == null)
        {
            existingType1.ComboHeatDhw.Type1EquipmentInformation = new Type1EquipmentInformation();
        }

        existingType1.ComboHeatDhw.Type1EquipmentInformation.Manufacturer = newType1.ComboHeatDhw.Type1EquipmentInformation.Manufacturer;
        existingType1.ComboHeatDhw.Type1EquipmentInformation.Model = newType1.ComboHeatDhw.Type1EquipmentInformation.Model;
        existingType1.ComboHeatDhw.Type1EquipmentInformation.Description = newType1.ComboHeatDhw.Type1EquipmentInformation.Description;
        existingType1.ComboHeatDhw.Type1EquipmentInformation.EnergyStar = newType1.ComboHeatDhw.Type1EquipmentInformation.EnergyStar;
        existingType1.ComboHeatDhw.Type1EquipmentInformation.AHRI = newType1.ComboHeatDhw.Type1EquipmentInformation.AHRI;
        existingType1.ComboHeatDhw.Type1EquipmentInformation.EpaCsa = newType1.ComboHeatDhw.Type1EquipmentInformation.EpaCsa;
    }

    // Update Specifications
    if (newType1.ComboHeatDhw.Specifications != null)
    {
        if (existingType1.ComboHeatDhw.Specifications == null)
        {
            existingType1.ComboHeatDhw.Specifications = new CommonSpecifications();
        }

        existingType1.ComboHeatDhw.Specifications.SizingFactor = newType1.ComboHeatDhw.Specifications.SizingFactor;
        existingType1.ComboHeatDhw.Specifications.Efficiency = newType1.ComboHeatDhw.Specifications.Efficiency;
        existingType1.ComboHeatDhw.Specifications.IsSteadyState = newType1.ComboHeatDhw.Specifications.IsSteadyState;
        existingType1.ComboHeatDhw.Specifications.PilotLight = newType1.ComboHeatDhw.Specifications.PilotLight;
        existingType1.ComboHeatDhw.Specifications.FlueDiameter = newType1.ComboHeatDhw.Specifications.FlueDiameter;

        // Update OutputCapacity
        if (newType1.ComboHeatDhw.Specifications.OutputCapacity != null)
        {
            if (existingType1.ComboHeatDhw.Specifications.OutputCapacity == null)
            {
                existingType1.ComboHeatDhw.Specifications.OutputCapacity = new OutputCapacity();
            }

            existingType1.ComboHeatDhw.Specifications.OutputCapacity.Code = newType1.ComboHeatDhw.Specifications.OutputCapacity.Code;
            existingType1.ComboHeatDhw.Specifications.OutputCapacity.EnglishText = newType1.ComboHeatDhw.Specifications.OutputCapacity.EnglishText;
            existingType1.ComboHeatDhw.Specifications.OutputCapacity.FrenchText = newType1.ComboHeatDhw.Specifications.OutputCapacity.FrenchText;
            existingType1.ComboHeatDhw.Specifications.OutputCapacity.Text = newType1.ComboHeatDhw.Specifications.OutputCapacity.Text;
            existingType1.ComboHeatDhw.Specifications.OutputCapacity.Value = newType1.ComboHeatDhw.Specifications.OutputCapacity.Value;
            existingType1.ComboHeatDhw.Specifications.OutputCapacity.UiUnits = newType1.ComboHeatDhw.Specifications.OutputCapacity.UiUnits;
            existingType1.ComboHeatDhw.Specifications.OutputCapacity.ValueInUiUnits = newType1.ComboHeatDhw.Specifications.OutputCapacity.ValueInUiUnits;
        }
    }

    // Update ComboTankAndPump
    UpdateComboTankAndPump(existingType1.ComboHeatDhw, newType1.ComboHeatDhw);

    // Update Equipment
    UpdateComboHeatDhwEquipment(existingType1.ComboHeatDhw, newType1.ComboHeatDhw);
}

private void UpdateComboHeatDhwEquipment(ComboHeatDhw existingCombo, ComboHeatDhw newCombo)
{
    if (newCombo.Equipment == null)
        return;

    if (existingCombo.Equipment == null)
    {
        existingCombo.Equipment = new ComboHeatDhwEquipment();
    }

    existingCombo.Equipment.IsBiEnergy = newCombo.Equipment.IsBiEnergy;
    existingCombo.Equipment.SwitchoverTemperature = newCombo.Equipment.SwitchoverTemperature;

    // Update EnergySource
    if (newCombo.Equipment.EnergySource != null)
    {
        if (existingCombo.Equipment.EnergySource == null)
        {
            existingCombo.Equipment.EnergySource = new HeatingEnergySources();
        }

        existingCombo.Equipment.EnergySource.Code = newCombo.Equipment.EnergySource.Code;
        existingCombo.Equipment.EnergySource.English = newCombo.Equipment.EnergySource.English;
        existingCombo.Equipment.EnergySource.French = newCombo.Equipment.EnergySource.French;
        existingCombo.Equipment.EnergySource.IsUserSpecified = newCombo.Equipment.EnergySource.IsUserSpecified;
    }

    // Update EquipmentTypeData
    if (newCombo.Equipment.EquipmentTypeData != null)
    {
        if (existingCombo.Equipment.EquipmentTypeData == null)
        {
            existingCombo.Equipment.EquipmentTypeData = new CodeAndText();
        }

        existingCombo.Equipment.EquipmentTypeData.Code = newCombo.Equipment.EquipmentTypeData.Code;
        existingCombo.Equipment.EquipmentTypeData.EnglishText = newCombo.Equipment.EquipmentTypeData.EnglishText;
        existingCombo.Equipment.EquipmentTypeData.FrenchText = newCombo.Equipment.EquipmentTypeData.FrenchText;
        existingCombo.Equipment.EquipmentTypeData.Text = newCombo.Equipment.EquipmentTypeData.Text;
    }
}

private void UpdateFurnace(Type1 existingType1, Type1 newType1)
{
    if (newType1.Furnace == null)
        return;

    if (existingType1.Furnace == null)
    {
        existingType1.Furnace = new Furnace
        {
            Id = Guid.NewGuid(),
            Type1Id = existingType1.Id
        };
        // Explicitly add to context as a new entity
        _context.Set<Furnace>().Add(existingType1.Furnace);
    }

    // Update Type1EquipmentInformation
    if (newType1.Furnace.Type1EquipmentInformation != null)
    {
        if (existingType1.Furnace.Type1EquipmentInformation == null)
        {
            existingType1.Furnace.Type1EquipmentInformation = new Type1EquipmentInformation();
        }

        existingType1.Furnace.Type1EquipmentInformation.Manufacturer = newType1.Furnace.Type1EquipmentInformation.Manufacturer;
        existingType1.Furnace.Type1EquipmentInformation.Model = newType1.Furnace.Type1EquipmentInformation.Model;
        existingType1.Furnace.Type1EquipmentInformation.Description = newType1.Furnace.Type1EquipmentInformation.Description;
        existingType1.Furnace.Type1EquipmentInformation.EnergyStar = newType1.Furnace.Type1EquipmentInformation.EnergyStar;
        existingType1.Furnace.Type1EquipmentInformation.AHRI = newType1.Furnace.Type1EquipmentInformation.AHRI;
        existingType1.Furnace.Type1EquipmentInformation.EpaCsa = newType1.Furnace.Type1EquipmentInformation.EpaCsa;
    }

    // Update Specifications
    if (newType1.Furnace.Specifications != null)
    {
        if (existingType1.Furnace.Specifications == null)
        {
            existingType1.Furnace.Specifications = new CommonSpecifications();
        }

        existingType1.Furnace.Specifications.SizingFactor = newType1.Furnace.Specifications.SizingFactor;
        existingType1.Furnace.Specifications.Efficiency = newType1.Furnace.Specifications.Efficiency;
        existingType1.Furnace.Specifications.IsSteadyState = newType1.Furnace.Specifications.IsSteadyState;
        existingType1.Furnace.Specifications.PilotLight = newType1.Furnace.Specifications.PilotLight;
        existingType1.Furnace.Specifications.FlueDiameter = newType1.Furnace.Specifications.FlueDiameter;

        // Update OutputCapacity
        if (newType1.Furnace.Specifications.OutputCapacity != null)
        {
            if (existingType1.Furnace.Specifications.OutputCapacity == null)
            {
                existingType1.Furnace.Specifications.OutputCapacity = new OutputCapacity();
            }

            existingType1.Furnace.Specifications.OutputCapacity.Code = newType1.Furnace.Specifications.OutputCapacity.Code;
            existingType1.Furnace.Specifications.OutputCapacity.EnglishText = newType1.Furnace.Specifications.OutputCapacity.EnglishText;
            existingType1.Furnace.Specifications.OutputCapacity.FrenchText = newType1.Furnace.Specifications.OutputCapacity.FrenchText;
            existingType1.Furnace.Specifications.OutputCapacity.Text = newType1.Furnace.Specifications.OutputCapacity.Text;
            existingType1.Furnace.Specifications.OutputCapacity.Value = newType1.Furnace.Specifications.OutputCapacity.Value;
            existingType1.Furnace.Specifications.OutputCapacity.UiUnits = newType1.Furnace.Specifications.OutputCapacity.UiUnits;
            existingType1.Furnace.Specifications.OutputCapacity.ValueInUiUnits = newType1.Furnace.Specifications.OutputCapacity.ValueInUiUnits;
        }
    }

    // Update ComboTankAndPump
    UpdateComboTankAndPump(existingType1.Furnace, newType1.Furnace);

    // Update Equipment
    UpdateFurnaceEquipment(existingType1.Furnace, newType1.Furnace);
}

private void UpdateFurnaceEquipment(Furnace existingFurnace, Furnace newFurnace)
{
    if (newFurnace.Equipment == null)
        return;

    if (existingFurnace.Equipment == null)
    {
        existingFurnace.Equipment = new FurnaceEquipment();
    }

    existingFurnace.Equipment.IsBiEnergy = newFurnace.Equipment.IsBiEnergy;
    existingFurnace.Equipment.SwitchoverTemperature = newFurnace.Equipment.SwitchoverTemperature;

    // Update EnergySource
    if (newFurnace.Equipment.EnergySource != null)
    {
        if (existingFurnace.Equipment.EnergySource == null)
        {
            existingFurnace.Equipment.EnergySource = new HeatingEnergySources();
        }

        existingFurnace.Equipment.EnergySource.Code = newFurnace.Equipment.EnergySource.Code;
        existingFurnace.Equipment.EnergySource.English = newFurnace.Equipment.EnergySource.English;
        existingFurnace.Equipment.EnergySource.French = newFurnace.Equipment.EnergySource.French;
        existingFurnace.Equipment.EnergySource.IsUserSpecified = newFurnace.Equipment.EnergySource.IsUserSpecified;
    }

    // Update EquipmentTypeData
    if (newFurnace.Equipment.EquipmentTypeData != null)
    {
        if (existingFurnace.Equipment.EquipmentTypeData == null)
        {
            existingFurnace.Equipment.EquipmentTypeData = new CodeAndText();
        }

        existingFurnace.Equipment.EquipmentTypeData.Code = newFurnace.Equipment.EquipmentTypeData.Code;
        existingFurnace.Equipment.EquipmentTypeData.EnglishText = newFurnace.Equipment.EquipmentTypeData.EnglishText;
        existingFurnace.Equipment.EquipmentTypeData.FrenchText = newFurnace.Equipment.EquipmentTypeData.FrenchText;
        existingFurnace.Equipment.EquipmentTypeData.Text = newFurnace.Equipment.EquipmentTypeData.Text;
    }
}

private void UpdateP9(Type1 existingType1, Type1 newType1)
{
    if (newType1.P9 == null)
        return;

    if (existingType1.P9 == null)
    {
        existingType1.P9 = new P9
        {
            Id = Guid.NewGuid(),
            Type1Id = existingType1.Id
        };
    }

    // Update P9 properties
    existingType1.P9.NumberOfSystems = newType1.P9.NumberOfSystems;
    existingType1.P9.ThermalPerformanceFactor = newType1.P9.ThermalPerformanceFactor;
    existingType1.P9.AnnualElectricity = newType1.P9.AnnualElectricity;
    existingType1.P9.SpaceHeatingCapacity = newType1.P9.SpaceHeatingCapacity;
    existingType1.P9.SpaceHeatingEfficiency = newType1.P9.SpaceHeatingEfficiency;
    existingType1.P9.WaterHeatingPerformanceFactor = newType1.P9.WaterHeatingPerformanceFactor;
    existingType1.P9.BurnerInput = newType1.P9.BurnerInput;
    existingType1.P9.RecoveryEfficiency = newType1.P9.RecoveryEfficiency;
    existingType1.P9.IsUserSpecified = newType1.P9.IsUserSpecified;

    // Update EquipmentInformation
    if (newType1.P9.EquipmentInformation != null)
    {
        if (existingType1.P9.EquipmentInformation == null)
        {
            existingType1.P9.EquipmentInformation = new EquipmentInformation();
        }

        existingType1.P9.EquipmentInformation.Manufacturer = newType1.P9.EquipmentInformation.Manufacturer;
        existingType1.P9.EquipmentInformation.Model = newType1.P9.EquipmentInformation.Model;
        existingType1.P9.EquipmentInformation.Description = newType1.P9.EquipmentInformation.Description;
    }

    // Update TestData
    if (newType1.P9.TestData != null)
    {
        if (existingType1.P9.TestData == null)
        {
            existingType1.P9.TestData = new TestData();
        }

        existingType1.P9.TestData.ControlsPower = newType1.P9.TestData.ControlsPower;
        existingType1.P9.TestData.CirculationPower = newType1.P9.TestData.CirculationPower;
        existingType1.P9.TestData.DailyUse = newType1.P9.TestData.DailyUse;
        existingType1.P9.TestData.StandbyLossWithFan = newType1.P9.TestData.StandbyLossWithFan;
        existingType1.P9.TestData.StandbyLossWithoutFan = newType1.P9.TestData.StandbyLossWithoutFan;
        existingType1.P9.TestData.OneHourRatingHotWater = newType1.P9.TestData.OneHourRatingHotWater;
        existingType1.P9.TestData.OneHourRatingConcurrent = newType1.P9.TestData.OneHourRatingConcurrent;
        existingType1.P9.TestData.EnergySource = newType1.P9.TestData.EnergySource;

        // Update NetEfficiency
        if (newType1.P9.TestData.NetEfficiency != null)
        {
            if (existingType1.P9.TestData.NetEfficiency == null)
            {
                existingType1.P9.TestData.NetEfficiency = new P9LoadPerformance();
            }

            existingType1.P9.TestData.NetEfficiency.Load10 = newType1.P9.TestData.NetEfficiency.Load10;
            existingType1.P9.TestData.NetEfficiency.Load40 = newType1.P9.TestData.NetEfficiency.Load40;
            existingType1.P9.TestData.NetEfficiency.Load100 = newType1.P9.TestData.NetEfficiency.Load100;
        }

        // Update ElectricalUse
        if (newType1.P9.TestData.ElectricalUse != null)
        {
            if (existingType1.P9.TestData.ElectricalUse == null)
            {
                existingType1.P9.TestData.ElectricalUse = new P9LoadPerformance();
            }

            existingType1.P9.TestData.ElectricalUse.Load10 = newType1.P9.TestData.ElectricalUse.Load10;
            existingType1.P9.TestData.ElectricalUse.Load40 = newType1.P9.TestData.ElectricalUse.Load40;
            existingType1.P9.TestData.ElectricalUse.Load100 = newType1.P9.TestData.ElectricalUse.Load100;
        }

        // Update BlowerPower
        if (newType1.P9.TestData.BlowerPower != null)
        {
            if (existingType1.P9.TestData.BlowerPower == null)
            {
                existingType1.P9.TestData.BlowerPower = new P9LoadPerformance();
            }

            existingType1.P9.TestData.BlowerPower.Load10 = newType1.P9.TestData.BlowerPower.Load10;
            existingType1.P9.TestData.BlowerPower.Load40 = newType1.P9.TestData.BlowerPower.Load40;
            existingType1.P9.TestData.BlowerPower.Load100 = newType1.P9.TestData.BlowerPower.Load100;
        }
    }
}

private void UpdateType2(HeatingCooling existingEntity, HeatingCooling heatingCooling)
{
    if (heatingCooling.Type2 == null)
        return;

    if (existingEntity.Type2 == null)
    {
        existingEntity.Type2 = new Type2
        {
            Id = Guid.NewGuid(),
            HeatingCoolingId = existingEntity.Id
        };
    }

    // Update Type2 properties
    existingEntity.Type2.ShadingInF280Cooling = heatingCooling.Type2.ShadingInF280Cooling;
    existingEntity.Type2.ShadingInF280CoolingIsAccountedFor = heatingCooling.Type2.ShadingInF280CoolingIsAccountedFor;

    // Update nested components
    UpdateAirHeatPump(existingEntity.Type2, heatingCooling.Type2);
    UpdateAirConditioning(existingEntity.Type2, heatingCooling.Type2);
    UpdateWaterHeatPump(existingEntity.Type2, heatingCooling.Type2);
    UpdateGroundHeatPump(existingEntity.Type2, heatingCooling.Type2);
}

/// <summary>
/// Selective update for Type2 - only saves data for the selected cooling/heat pump system type
/// Similar to the old system's logic based on m_pumpType
/// </summary>
private async Task UpdateType2SelectiveAsync(HeatingCooling existingEntity, HeatingCooling heatingCooling)
{
    // FIXED: Follow Type1 approach exactly - if Type2 data is sent, create Type2
    if (heatingCooling.Type2 == null)
        return;

    // Create Type2 if it doesn't exist (following Type1 approach exactly)
    if (existingEntity.Type2 == null)
    {
        _logger.LogInformation("Creating new Type2 system for house {HouseId}", heatingCooling.HouseId);
        existingEntity.Type2 = new Type2
        {
            Id = Guid.NewGuid(),
            HeatingCoolingId = existingEntity.Id
        };
    }

    var selectedSystemType = heatingCooling.GetSelectedType2System();

    // Create Type2 if it doesn't exist (following Type1 approach exactly)
    if (existingEntity.Type2 == null)
    {
        _logger.LogInformation("Creating new Type2 system for house {HouseId}", heatingCooling.HouseId);
        existingEntity.Type2 = new Type2
        {
            Id = Guid.NewGuid(),
            HeatingCoolingId = existingEntity.Id
        };
        // FIXED: Follow Type1 approach exactly - explicitly add to context
        _context.Set<Type2>().Add(existingEntity.Type2);
    }

    // Always update common Type2 properties
    existingEntity.Type2.ShadingInF280Cooling = heatingCooling.Type2.ShadingInF280Cooling;
    existingEntity.Type2.ShadingInF280CoolingIsAccountedFor = heatingCooling.Type2.ShadingInF280CoolingIsAccountedFor;

    // Only update the selected system type based on PumpType (if any)
    switch (selectedSystemType)
    {
        case Type2SystemType.None:
            _logger.LogInformation("Type2 created with basic properties only (no heat pump/AC) for house {HouseId}", heatingCooling.HouseId);
            // Clear all heat pump/AC systems but keep Type2 for shading properties
            ClearUnselectedType2SystemsInMemory(existingEntity.Type2, selectedSystemType);
            break;

        case Type2SystemType.AirSourceHeatPump:
            _logger.LogInformation("Updating Air Source Heat Pump for house {HouseId}", heatingCooling.HouseId);
            ClearUnselectedType2SystemsInMemory(existingEntity.Type2, selectedSystemType);
            UpdateAirHeatPump(existingEntity.Type2, heatingCooling.Type2);
            break;

        case Type2SystemType.WaterSourceHeatPump:
            _logger.LogInformation("Updating Water Source Heat Pump for house {HouseId}", heatingCooling.HouseId);
            ClearUnselectedType2SystemsInMemory(existingEntity.Type2, selectedSystemType);
            UpdateWaterHeatPump(existingEntity.Type2, heatingCooling.Type2);
            break;

        case Type2SystemType.GroundSourceHeatPump:
            _logger.LogInformation("Updating Ground Source Heat Pump for house {HouseId}", heatingCooling.HouseId);
            ClearUnselectedType2SystemsInMemory(existingEntity.Type2, selectedSystemType);
            UpdateGroundHeatPump(existingEntity.Type2, heatingCooling.Type2);
            break;

        case Type2SystemType.AirConditioning:
            _logger.LogInformation("Updating Air Conditioning for house {HouseId}", heatingCooling.HouseId);
            ClearUnselectedType2SystemsInMemory(existingEntity.Type2, selectedSystemType);
            UpdateAirConditioning(existingEntity.Type2, heatingCooling.Type2);
            break;

        default:
            _logger.LogInformation("Unknown Type2 system type for house {HouseId}", heatingCooling.HouseId);
            ClearUnselectedType2SystemsInMemory(existingEntity.Type2, selectedSystemType);
            break;
    }
}

/// <summary>
/// Clear unselected Type2 systems in memory - sets them to null so only the selected one gets saved
/// This approach avoids entity removal conflicts during updates
/// </summary>
private void ClearUnselectedType2SystemsInMemory(Type2 type2, Type2SystemType selectedType)
{
    // Set unselected systems to null - they won't be updated
    if (selectedType != Type2SystemType.AirSourceHeatPump)
        type2.AirHeatPump = null;

    if (selectedType != Type2SystemType.WaterSourceHeatPump)
        type2.WaterHeatPump = null;

    if (selectedType != Type2SystemType.GroundSourceHeatPump)
        type2.GroundHeatPump = null;

    if (selectedType != Type2SystemType.AirConditioning)
        type2.AirConditioning = null;
}

private void UpdateAirHeatPump(Type2 existingType2, Type2 newType2)
{
    if (newType2.AirHeatPump == null)
        return;

    if (existingType2.AirHeatPump == null)
    {
        existingType2.AirHeatPump = new AirHeatPump
        {
            Id = Guid.NewGuid(),
            Type2Id = existingType2.Id
        };
        // FIXED: Follow Type1 approach exactly - explicitly add to context
        _context.Set<AirHeatPump>().Add(existingType2.AirHeatPump);
    }

    // Update EquipmentInformation
    if (newType2.AirHeatPump.EquipmentInformation != null)
    {
        if (existingType2.AirHeatPump.EquipmentInformation == null)
        {
            existingType2.AirHeatPump.EquipmentInformation = new HeatPumpEquipmentInformation();
        }

        existingType2.AirHeatPump.EquipmentInformation.Manufacturer = newType2.AirHeatPump.EquipmentInformation.Manufacturer;
        existingType2.AirHeatPump.EquipmentInformation.Model = newType2.AirHeatPump.EquipmentInformation.Model;
        existingType2.AirHeatPump.EquipmentInformation.Description = newType2.AirHeatPump.EquipmentInformation.Description;
        existingType2.AirHeatPump.EquipmentInformation.EnergyStar = newType2.AirHeatPump.EquipmentInformation.EnergyStar;
        existingType2.AirHeatPump.EquipmentInformation.AHRI = newType2.AirHeatPump.EquipmentInformation.AHRI;
        existingType2.AirHeatPump.EquipmentInformation.CanCsaC448 = newType2.AirHeatPump.EquipmentInformation.CanCsaC448;
    }

    // Update Equipment
    if (newType2.AirHeatPump.Equipment != null)
    {
        if (existingType2.AirHeatPump.Equipment == null)
        {
            existingType2.AirHeatPump.Equipment = new HeatPumpEquipment();
        }

        existingType2.AirHeatPump.Equipment.CrankcaseHeater = newType2.AirHeatPump.Equipment.CrankcaseHeater;
        existingType2.AirHeatPump.Equipment.NumberOfHeads = newType2.AirHeatPump.Equipment.NumberOfHeads;
        existingType2.AirHeatPump.Equipment.Type = newType2.AirHeatPump.Equipment.Type;
        existingType2.AirHeatPump.Equipment.Function = newType2.AirHeatPump.Equipment.Function;
    }

    // Update Specifications
    if (newType2.AirHeatPump.Specifications != null)
    {
        if (existingType2.AirHeatPump.Specifications == null)
        {
            existingType2.AirHeatPump.Specifications = new HeatPumpSpecifications();
        }

        // Update OutputCapacity
        if (newType2.AirHeatPump.Specifications.OutputCapacity != null)
        {
            if (existingType2.AirHeatPump.Specifications.OutputCapacity == null)
            {
                existingType2.AirHeatPump.Specifications.OutputCapacity = new OutputCapacity();
            }

            existingType2.AirHeatPump.Specifications.OutputCapacity.Code = newType2.AirHeatPump.Specifications.OutputCapacity.Code;
            existingType2.AirHeatPump.Specifications.OutputCapacity.EnglishText = newType2.AirHeatPump.Specifications.OutputCapacity.EnglishText;
            existingType2.AirHeatPump.Specifications.OutputCapacity.FrenchText = newType2.AirHeatPump.Specifications.OutputCapacity.FrenchText;
            existingType2.AirHeatPump.Specifications.OutputCapacity.Text = newType2.AirHeatPump.Specifications.OutputCapacity.Text;
            existingType2.AirHeatPump.Specifications.OutputCapacity.Value = newType2.AirHeatPump.Specifications.OutputCapacity.Value;
            existingType2.AirHeatPump.Specifications.OutputCapacity.UiUnits = newType2.AirHeatPump.Specifications.OutputCapacity.UiUnits;
            existingType2.AirHeatPump.Specifications.OutputCapacity.ValueInUiUnits = newType2.AirHeatPump.Specifications.OutputCapacity.ValueInUiUnits;
        }

        // Update HeatingEfficiency
        if (newType2.AirHeatPump.Specifications.HeatingEfficiency != null)
        {
            if (existingType2.AirHeatPump.Specifications.HeatingEfficiency == null)
            {
                existingType2.AirHeatPump.Specifications.HeatingEfficiency = new CopSeerValue();
            }

            existingType2.AirHeatPump.Specifications.HeatingEfficiency.IsCop = newType2.AirHeatPump.Specifications.HeatingEfficiency.IsCop;
            existingType2.AirHeatPump.Specifications.HeatingEfficiency.Unit = newType2.AirHeatPump.Specifications.HeatingEfficiency.Unit;
            existingType2.AirHeatPump.Specifications.HeatingEfficiency.Value = newType2.AirHeatPump.Specifications.HeatingEfficiency.Value;
        }

        // Update CoolingEfficiency
        if (newType2.AirHeatPump.Specifications.CoolingEfficiency != null)
        {
            if (existingType2.AirHeatPump.Specifications.CoolingEfficiency == null)
            {
                existingType2.AirHeatPump.Specifications.CoolingEfficiency = new CopSeerValue();
            }

            existingType2.AirHeatPump.Specifications.CoolingEfficiency.IsCop = newType2.AirHeatPump.Specifications.CoolingEfficiency.IsCop;
            existingType2.AirHeatPump.Specifications.CoolingEfficiency.Unit = newType2.AirHeatPump.Specifications.CoolingEfficiency.Unit;
            existingType2.AirHeatPump.Specifications.CoolingEfficiency.Value = newType2.AirHeatPump.Specifications.CoolingEfficiency.Value;
        }
    }

    // Update Temperature
    if (newType2.AirHeatPump.Temperature != null)
    {
        if (existingType2.AirHeatPump.Temperature == null)
        {
            existingType2.AirHeatPump.Temperature = new HeatPumpTemperature();
        }

        existingType2.AirHeatPump.Temperature.CutoffType = newType2.AirHeatPump.Temperature.CutoffType;
        existingType2.AirHeatPump.Temperature.RatingType = newType2.AirHeatPump.Temperature.RatingType;
    }

    // Update SourceTemperature
    if (newType2.AirHeatPump.SourceTemperature != null)
    {
        if (existingType2.AirHeatPump.SourceTemperature == null)
        {
            existingType2.AirHeatPump.SourceTemperature = new SourceTemperature();
        }

        existingType2.AirHeatPump.SourceTemperature.Depth = newType2.AirHeatPump.SourceTemperature.Depth;
        existingType2.AirHeatPump.SourceTemperature.Use = newType2.AirHeatPump.SourceTemperature.Use;

        // Update Temperatures
        if (newType2.AirHeatPump.SourceTemperature.Temperatures != null)
        {
            if (existingType2.AirHeatPump.SourceTemperature.Temperatures == null)
            {
                existingType2.AirHeatPump.SourceTemperature.Temperatures = new MonthlyData();
            }

            existingType2.AirHeatPump.SourceTemperature.Temperatures.January = newType2.AirHeatPump.SourceTemperature.Temperatures.January;
            existingType2.AirHeatPump.SourceTemperature.Temperatures.February = newType2.AirHeatPump.SourceTemperature.Temperatures.February;
            existingType2.AirHeatPump.SourceTemperature.Temperatures.March = newType2.AirHeatPump.SourceTemperature.Temperatures.March;
            existingType2.AirHeatPump.SourceTemperature.Temperatures.April = newType2.AirHeatPump.SourceTemperature.Temperatures.April;
            existingType2.AirHeatPump.SourceTemperature.Temperatures.May = newType2.AirHeatPump.SourceTemperature.Temperatures.May;
            existingType2.AirHeatPump.SourceTemperature.Temperatures.June = newType2.AirHeatPump.SourceTemperature.Temperatures.June;
            existingType2.AirHeatPump.SourceTemperature.Temperatures.July = newType2.AirHeatPump.SourceTemperature.Temperatures.July;
            existingType2.AirHeatPump.SourceTemperature.Temperatures.August = newType2.AirHeatPump.SourceTemperature.Temperatures.August;
            existingType2.AirHeatPump.SourceTemperature.Temperatures.September = newType2.AirHeatPump.SourceTemperature.Temperatures.September;
            existingType2.AirHeatPump.SourceTemperature.Temperatures.October = newType2.AirHeatPump.SourceTemperature.Temperatures.October;
            existingType2.AirHeatPump.SourceTemperature.Temperatures.November = newType2.AirHeatPump.SourceTemperature.Temperatures.November;
            existingType2.AirHeatPump.SourceTemperature.Temperatures.December = newType2.AirHeatPump.SourceTemperature.Temperatures.December;
        }
    }

    // Update CoolingParameters
    UpdateCoolingParameters(existingType2.AirHeatPump, newType2.AirHeatPump);

    // Update ColdClimateHeatPump
    if (newType2.AirHeatPump.ColdClimateHeatPump != null)
    {
        if (existingType2.AirHeatPump.ColdClimateHeatPump == null)
        {
            existingType2.AirHeatPump.ColdClimateHeatPump = new ColdClimateHeatPump();
        }

        existingType2.AirHeatPump.ColdClimateHeatPump.HeatingEfficiency = newType2.AirHeatPump.ColdClimateHeatPump.HeatingEfficiency;
        existingType2.AirHeatPump.ColdClimateHeatPump.CoolingEfficiency = newType2.AirHeatPump.ColdClimateHeatPump.CoolingEfficiency;
        existingType2.AirHeatPump.ColdClimateHeatPump.HeatingEfficiencyUnit = newType2.AirHeatPump.ColdClimateHeatPump.HeatingEfficiencyUnit;
        existingType2.AirHeatPump.ColdClimateHeatPump.CoolingEfficiencyUnit = newType2.AirHeatPump.ColdClimateHeatPump.CoolingEfficiencyUnit;
        existingType2.AirHeatPump.ColdClimateHeatPump.Capacity = newType2.AirHeatPump.ColdClimateHeatPump.Capacity;
        existingType2.AirHeatPump.ColdClimateHeatPump.Cop = newType2.AirHeatPump.ColdClimateHeatPump.Cop;
        existingType2.AirHeatPump.ColdClimateHeatPump.CapacityMaintenance = newType2.AirHeatPump.ColdClimateHeatPump.CapacityMaintenance;
        existingType2.AirHeatPump.ColdClimateHeatPump.UiUnits = newType2.AirHeatPump.ColdClimateHeatPump.UiUnits;
    }
}

private void UpdateCoolingParameters(dynamic existingHeatPump, dynamic newHeatPump)
{
    if (newHeatPump.CoolingParameters == null)
        return;

    if (existingHeatPump.CoolingParameters == null)
    {
        existingHeatPump.CoolingParameters = new HeatPumpCoolingType();
    }

    existingHeatPump.CoolingParameters.SensibleHeatRatio = newHeatPump.CoolingParameters.SensibleHeatRatio;
    existingHeatPump.CoolingParameters.OpenableWindowArea = newHeatPump.CoolingParameters.OpenableWindowArea;

    // Update FansAndPump
    if (newHeatPump.CoolingParameters.FansAndPump != null)
    {
        if (existingHeatPump.CoolingParameters.FansAndPump == null)
        {
            existingHeatPump.CoolingParameters.FansAndPump = new CoolingFansAndPumps();
        }

        existingHeatPump.CoolingParameters.FansAndPump.FlowRate = newHeatPump.CoolingParameters.FansAndPump.FlowRate;
        existingHeatPump.CoolingParameters.FansAndPump.HasEnergyEfficientMotor = newHeatPump.CoolingParameters.FansAndPump.HasEnergyEfficientMotor;

        // Update Mode
        if (newHeatPump.CoolingParameters.FansAndPump.Mode != null)
        {
            if (existingHeatPump.CoolingParameters.FansAndPump.Mode == null)
            {
                existingHeatPump.CoolingParameters.FansAndPump.Mode = CoolingFanModes.Auto;
            }

            existingHeatPump.CoolingParameters.FansAndPump.Mode.Code = newHeatPump.CoolingParameters.FansAndPump.Mode.Code;
            existingHeatPump.CoolingParameters.FansAndPump.Mode.English = newHeatPump.CoolingParameters.FansAndPump.Mode.English;
            existingHeatPump.CoolingParameters.FansAndPump.Mode.French = newHeatPump.CoolingParameters.FansAndPump.Mode.French;
            existingHeatPump.CoolingParameters.FansAndPump.Mode.IsUserSpecified = newHeatPump.CoolingParameters.FansAndPump.Mode.IsUserSpecified;
        }

        // Update Power
        if (newHeatPump.CoolingParameters.FansAndPump.Power != null)
        {
            if (existingHeatPump.CoolingParameters.FansAndPump.Power == null)
            {
                existingHeatPump.CoolingParameters.FansAndPump.Power = new FansAndPumpPowerCooling();
            }

            existingHeatPump.CoolingParameters.FansAndPump.Power.IsCalculated = newHeatPump.CoolingParameters.FansAndPump.Power.IsCalculated;
            existingHeatPump.CoolingParameters.FansAndPump.Power.Value = newHeatPump.CoolingParameters.FansAndPump.Power.Value;
        }
    }
}

private void UpdateAirConditioning(Type2 existingType2, Type2 newType2)
{
    if (newType2.AirConditioning == null)
        return;

    if (existingType2.AirConditioning == null)
    {
        existingType2.AirConditioning = new AirConditioning
        {
            Id = Guid.NewGuid(),
            Type2Id = existingType2.Id
        };
    }

    // Update EquipmentInformation
    if (newType2.AirConditioning.EquipmentInformation != null)
    {
        if (existingType2.AirConditioning.EquipmentInformation == null)
        {
            existingType2.AirConditioning.EquipmentInformation = new EnergyStarEquipmentInformation();
        }

        existingType2.AirConditioning.EquipmentInformation.Manufacturer = newType2.AirConditioning.EquipmentInformation.Manufacturer;
        existingType2.AirConditioning.EquipmentInformation.Model = newType2.AirConditioning.EquipmentInformation.Model;
        existingType2.AirConditioning.EquipmentInformation.Description = newType2.AirConditioning.EquipmentInformation.Description;
        existingType2.AirConditioning.EquipmentInformation.EnergyStar = newType2.AirConditioning.EquipmentInformation.EnergyStar;
        existingType2.AirConditioning.EquipmentInformation.AHRI = newType2.AirConditioning.EquipmentInformation.AHRI;
    }

    // Update Equipment
    if (newType2.AirConditioning.Equipment != null)
    {
        if (existingType2.AirConditioning.Equipment == null)
        {
            existingType2.AirConditioning.Equipment = new AirConditioningEquipment();
        }

        existingType2.AirConditioning.Equipment.CrankcaseHeater = newType2.AirConditioning.Equipment.CrankcaseHeater;
        existingType2.AirConditioning.Equipment.NumberOfHeads = newType2.AirConditioning.Equipment.NumberOfHeads;
        existingType2.AirConditioning.Equipment.Type = newType2.AirConditioning.Equipment.Type;
        existingType2.AirConditioning.Equipment.CentralType = newType2.AirConditioning.Equipment.CentralType;

        // Update WindowUnits
        if (newType2.AirConditioning.Equipment.WindowUnits != null)
        {
            if (existingType2.AirConditioning.Equipment.WindowUnits == null)
            {
                existingType2.AirConditioning.Equipment.WindowUnits = new WindowUnits();
            }

            existingType2.AirConditioning.Equipment.WindowUnits.TotalCount = newType2.AirConditioning.Equipment.WindowUnits.TotalCount;
            existingType2.AirConditioning.Equipment.WindowUnits.NumberOfEnergyStarUnits = newType2.AirConditioning.Equipment.WindowUnits.NumberOfEnergyStarUnits;
        }
    }

    // Update Specifications
    if (newType2.AirConditioning.Specifications != null)
    {
        if (existingType2.AirConditioning.Specifications == null)
        {
            existingType2.AirConditioning.Specifications = new AirConditioningSpecifications();
        }

        existingType2.AirConditioning.Specifications.SizingFactor = newType2.AirConditioning.Specifications.SizingFactor;

        // Update RatedCapacity
        if (newType2.AirConditioning.Specifications.RatedCapacity != null)
        {
            if (existingType2.AirConditioning.Specifications.RatedCapacity == null)
            {
                existingType2.AirConditioning.Specifications.RatedCapacity = new OutputCapacity();
            }

            existingType2.AirConditioning.Specifications.RatedCapacity.Code = newType2.AirConditioning.Specifications.RatedCapacity.Code;
            existingType2.AirConditioning.Specifications.RatedCapacity.EnglishText = newType2.AirConditioning.Specifications.RatedCapacity.EnglishText;
            existingType2.AirConditioning.Specifications.RatedCapacity.FrenchText = newType2.AirConditioning.Specifications.RatedCapacity.FrenchText;
            existingType2.AirConditioning.Specifications.RatedCapacity.Text = newType2.AirConditioning.Specifications.RatedCapacity.Text;
            existingType2.AirConditioning.Specifications.RatedCapacity.Value = newType2.AirConditioning.Specifications.RatedCapacity.Value;
            existingType2.AirConditioning.Specifications.RatedCapacity.UiUnits = newType2.AirConditioning.Specifications.RatedCapacity.UiUnits;
            existingType2.AirConditioning.Specifications.RatedCapacity.ValueInUiUnits = newType2.AirConditioning.Specifications.RatedCapacity.ValueInUiUnits;
        }

        // Update Efficiency
        if (newType2.AirConditioning.Specifications.Efficiency != null)
        {
            if (existingType2.AirConditioning.Specifications.Efficiency == null)
            {
                existingType2.AirConditioning.Specifications.Efficiency = new CopSeerValue();
            }

            existingType2.AirConditioning.Specifications.Efficiency.IsCop = newType2.AirConditioning.Specifications.Efficiency.IsCop;
            existingType2.AirConditioning.Specifications.Efficiency.Unit = newType2.AirConditioning.Specifications.Efficiency.Unit;
            existingType2.AirConditioning.Specifications.Efficiency.Value = newType2.AirConditioning.Specifications.Efficiency.Value;
        }
    }

    // Update CoolingParameters
    UpdateCoolingParameters(existingType2.AirConditioning, newType2.AirConditioning);
}

private void UpdateWaterHeatPump(Type2 existingType2, Type2 newType2)
{
    if (newType2.WaterHeatPump == null)
        return;

    if (existingType2.WaterHeatPump == null)
    {
        existingType2.WaterHeatPump = new WaterHeatPump
        {
            Id = Guid.NewGuid(),
            Type2Id = existingType2.Id
        };
    }

    // Update EquipmentInformation
    if (newType2.WaterHeatPump.EquipmentInformation != null)
    {
        if (existingType2.WaterHeatPump.EquipmentInformation == null)
        {
            existingType2.WaterHeatPump.EquipmentInformation = new HeatPumpEquipmentInformation();
        }

        existingType2.WaterHeatPump.EquipmentInformation.Manufacturer = newType2.WaterHeatPump.EquipmentInformation.Manufacturer;
        existingType2.WaterHeatPump.EquipmentInformation.Model = newType2.WaterHeatPump.EquipmentInformation.Model;
        existingType2.WaterHeatPump.EquipmentInformation.Description = newType2.WaterHeatPump.EquipmentInformation.Description;
        existingType2.WaterHeatPump.EquipmentInformation.EnergyStar = newType2.WaterHeatPump.EquipmentInformation.EnergyStar;
        existingType2.WaterHeatPump.EquipmentInformation.AHRI = newType2.WaterHeatPump.EquipmentInformation.AHRI;
        existingType2.WaterHeatPump.EquipmentInformation.CanCsaC448 = newType2.WaterHeatPump.EquipmentInformation.CanCsaC448;
    }

    // Update Equipment
    if (newType2.WaterHeatPump.Equipment != null)
    {
        if (existingType2.WaterHeatPump.Equipment == null)
        {
            existingType2.WaterHeatPump.Equipment = new HeatPumpEquipment();
        }

        existingType2.WaterHeatPump.Equipment.CrankcaseHeater = newType2.WaterHeatPump.Equipment.CrankcaseHeater;
        existingType2.WaterHeatPump.Equipment.NumberOfHeads = newType2.WaterHeatPump.Equipment.NumberOfHeads;
        existingType2.WaterHeatPump.Equipment.Function = newType2.WaterHeatPump.Equipment.Function;
        existingType2.WaterHeatPump.Equipment.Type = newType2.WaterHeatPump.Equipment.Type;
    }

    // Update Specifications (similar to AirHeatPump)
    if (newType2.WaterHeatPump.Specifications != null)
    {
        if (existingType2.WaterHeatPump.Specifications == null)
        {
            existingType2.WaterHeatPump.Specifications = new HeatPumpSpecifications();
        }

        // Update OutputCapacity
        if (newType2.WaterHeatPump.Specifications.OutputCapacity != null)
        {
            if (existingType2.WaterHeatPump.Specifications.OutputCapacity == null)
            {
                existingType2.WaterHeatPump.Specifications.OutputCapacity = new OutputCapacity();
            }

            existingType2.WaterHeatPump.Specifications.OutputCapacity.Code = newType2.WaterHeatPump.Specifications.OutputCapacity.Code;
            existingType2.WaterHeatPump.Specifications.OutputCapacity.EnglishText = newType2.WaterHeatPump.Specifications.OutputCapacity.EnglishText;
            existingType2.WaterHeatPump.Specifications.OutputCapacity.FrenchText = newType2.WaterHeatPump.Specifications.OutputCapacity.FrenchText;
            existingType2.WaterHeatPump.Specifications.OutputCapacity.Text = newType2.WaterHeatPump.Specifications.OutputCapacity.Text;
            existingType2.WaterHeatPump.Specifications.OutputCapacity.Value = newType2.WaterHeatPump.Specifications.OutputCapacity.Value;
            existingType2.WaterHeatPump.Specifications.OutputCapacity.UiUnits = newType2.WaterHeatPump.Specifications.OutputCapacity.UiUnits;
            existingType2.WaterHeatPump.Specifications.OutputCapacity.ValueInUiUnits = newType2.WaterHeatPump.Specifications.OutputCapacity.ValueInUiUnits;
        }

        // Update HeatingEfficiency
        if (newType2.WaterHeatPump.Specifications.HeatingEfficiency != null)
        {
            if (existingType2.WaterHeatPump.Specifications.HeatingEfficiency == null)
            {
                existingType2.WaterHeatPump.Specifications.HeatingEfficiency = new CopSeerValue();
            }

            existingType2.WaterHeatPump.Specifications.HeatingEfficiency.IsCop = newType2.WaterHeatPump.Specifications.HeatingEfficiency.IsCop;
            existingType2.WaterHeatPump.Specifications.HeatingEfficiency.Unit = newType2.WaterHeatPump.Specifications.HeatingEfficiency.Unit;
            existingType2.WaterHeatPump.Specifications.HeatingEfficiency.Value = newType2.WaterHeatPump.Specifications.HeatingEfficiency.Value;
        }

        // Update CoolingEfficiency
        if (newType2.WaterHeatPump.Specifications.CoolingEfficiency != null)
        {
            if (existingType2.WaterHeatPump.Specifications.CoolingEfficiency == null)
            {
                existingType2.WaterHeatPump.Specifications.CoolingEfficiency = new CopSeerValue();
            }

            existingType2.WaterHeatPump.Specifications.CoolingEfficiency.IsCop = newType2.WaterHeatPump.Specifications.CoolingEfficiency.IsCop;
            existingType2.WaterHeatPump.Specifications.CoolingEfficiency.Unit = newType2.WaterHeatPump.Specifications.CoolingEfficiency.Unit;
            existingType2.WaterHeatPump.Specifications.CoolingEfficiency.Value = newType2.WaterHeatPump.Specifications.CoolingEfficiency.Value;
        }
    }

    // Update other properties similar to AirHeatPump
    UpdateCoolingParameters(existingType2.WaterHeatPump, newType2.WaterHeatPump);
}

private void UpdateGroundHeatPump(Type2 existingType2, Type2 newType2)
{
    if (newType2.GroundHeatPump == null)
        return;

    if (existingType2.GroundHeatPump == null)
    {
        existingType2.GroundHeatPump = new GroundHeatPump
        {
            Id = Guid.NewGuid(),
            Type2Id = existingType2.Id
        };
    }

    // Update EquipmentInformation
    if (newType2.GroundHeatPump.EquipmentInformation != null)
    {
        if (existingType2.GroundHeatPump.EquipmentInformation == null)
        {
            existingType2.GroundHeatPump.EquipmentInformation = new HeatPumpEquipmentInformation();
        }

        existingType2.GroundHeatPump.EquipmentInformation.Manufacturer = newType2.GroundHeatPump.EquipmentInformation.Manufacturer;
        existingType2.GroundHeatPump.EquipmentInformation.Model = newType2.GroundHeatPump.EquipmentInformation.Model;
        existingType2.GroundHeatPump.EquipmentInformation.Description = newType2.GroundHeatPump.EquipmentInformation.Description;
        existingType2.GroundHeatPump.EquipmentInformation.EnergyStar = newType2.GroundHeatPump.EquipmentInformation.EnergyStar;
        existingType2.GroundHeatPump.EquipmentInformation.AHRI = newType2.GroundHeatPump.EquipmentInformation.AHRI;
        existingType2.GroundHeatPump.EquipmentInformation.CanCsaC448 = newType2.GroundHeatPump.EquipmentInformation.CanCsaC448;
    }

    // Update Equipment
    if (newType2.GroundHeatPump.Equipment != null)
    {
        if (existingType2.GroundHeatPump.Equipment == null)
        {
            existingType2.GroundHeatPump.Equipment = new HeatPumpEquipment();
        }

        existingType2.GroundHeatPump.Equipment.CrankcaseHeater = newType2.GroundHeatPump.Equipment.CrankcaseHeater;
        existingType2.GroundHeatPump.Equipment.NumberOfHeads = newType2.GroundHeatPump.Equipment.NumberOfHeads;
        existingType2.GroundHeatPump.Equipment.Function = newType2.GroundHeatPump.Equipment.Function;
        existingType2.GroundHeatPump.Equipment.Type = newType2.GroundHeatPump.Equipment.Type;
    }

    // Update Specifications (similar to AirHeatPump)
    if (newType2.GroundHeatPump.Specifications != null)
    {
        if (existingType2.GroundHeatPump.Specifications == null)
        {
            existingType2.GroundHeatPump.Specifications = new HeatPumpSpecifications();
        }

        // Update OutputCapacity
        if (newType2.GroundHeatPump.Specifications.OutputCapacity != null)
        {
            if (existingType2.GroundHeatPump.Specifications.OutputCapacity == null)
            {
                existingType2.GroundHeatPump.Specifications.OutputCapacity = new OutputCapacity();
            }

            existingType2.GroundHeatPump.Specifications.OutputCapacity.Code = newType2.GroundHeatPump.Specifications.OutputCapacity.Code;
            existingType2.GroundHeatPump.Specifications.OutputCapacity.EnglishText = newType2.GroundHeatPump.Specifications.OutputCapacity.EnglishText;
            existingType2.GroundHeatPump.Specifications.OutputCapacity.FrenchText = newType2.GroundHeatPump.Specifications.OutputCapacity.FrenchText;
            existingType2.GroundHeatPump.Specifications.OutputCapacity.Text = newType2.GroundHeatPump.Specifications.OutputCapacity.Text;
            existingType2.GroundHeatPump.Specifications.OutputCapacity.Value = newType2.GroundHeatPump.Specifications.OutputCapacity.Value;
            existingType2.GroundHeatPump.Specifications.OutputCapacity.UiUnits = newType2.GroundHeatPump.Specifications.OutputCapacity.UiUnits;
            existingType2.GroundHeatPump.Specifications.OutputCapacity.ValueInUiUnits = newType2.GroundHeatPump.Specifications.OutputCapacity.ValueInUiUnits;
        }

        // Update HeatingEfficiency
        if (newType2.GroundHeatPump.Specifications.HeatingEfficiency != null)
        {
            if (existingType2.GroundHeatPump.Specifications.HeatingEfficiency == null)
            {
                existingType2.GroundHeatPump.Specifications.HeatingEfficiency = new CopSeerValue();
            }

            existingType2.GroundHeatPump.Specifications.HeatingEfficiency.IsCop = newType2.GroundHeatPump.Specifications.HeatingEfficiency.IsCop;
            existingType2.GroundHeatPump.Specifications.HeatingEfficiency.Unit = newType2.GroundHeatPump.Specifications.HeatingEfficiency.Unit;
            existingType2.GroundHeatPump.Specifications.HeatingEfficiency.Value = newType2.GroundHeatPump.Specifications.HeatingEfficiency.Value;
        }

        // Update CoolingEfficiency
        if (newType2.GroundHeatPump.Specifications.CoolingEfficiency != null)
        {
            if (existingType2.GroundHeatPump.Specifications.CoolingEfficiency == null)
            {
                existingType2.GroundHeatPump.Specifications.CoolingEfficiency = new CopSeerValue();
            }

            existingType2.GroundHeatPump.Specifications.CoolingEfficiency.IsCop = newType2.GroundHeatPump.Specifications.CoolingEfficiency.IsCop;
            existingType2.GroundHeatPump.Specifications.CoolingEfficiency.Unit = newType2.GroundHeatPump.Specifications.CoolingEfficiency.Unit;
            existingType2.GroundHeatPump.Specifications.CoolingEfficiency.Value = newType2.GroundHeatPump.Specifications.CoolingEfficiency.Value;
        }
    }

    // Update other properties similar to AirHeatPump
    UpdateCoolingParameters(existingType2.GroundHeatPump, newType2.GroundHeatPump);
}


private void UpdateRadiantHeating(HeatingCooling existingEntity, HeatingCooling heatingCooling)
{
    if (heatingCooling.RadiantHeating == null)
        return;

    if (existingEntity.RadiantHeating == null)
    {
        existingEntity.RadiantHeating = new RadiantHeating
        {
            Id = Guid.NewGuid(),
            HeatingCoolingId = existingEntity.Id
        };
        // Explicitly add to context as a new entity
        _context.Set<RadiantHeating>().Add(existingEntity.RadiantHeating);
    }

    // Update all radiant heating components
    UpdateRadiantComponent(existingEntity.RadiantHeating, heatingCooling.RadiantHeating, "AtticCeiling");
    UpdateRadiantComponent(existingEntity.RadiantHeating, heatingCooling.RadiantHeating, "FlatRoof");
    UpdateRadiantComponent(existingEntity.RadiantHeating, heatingCooling.RadiantHeating, "AboveCrawlspace");
    UpdateRadiantComponent(existingEntity.RadiantHeating, heatingCooling.RadiantHeating, "SlabOnGrade");
    UpdateRadiantComponent(existingEntity.RadiantHeating, heatingCooling.RadiantHeating, "AboveBasement");
    UpdateRadiantComponent(existingEntity.RadiantHeating, heatingCooling.RadiantHeating, "Basement");
}

/// <summary>
/// Selective update for RadiantHeating - only saves data if radiant heating is enabled
/// Similar to the old system's logic based on m_hasRadiantHeating
/// </summary>
private async Task UpdateRadiantHeatingSelectiveAsync(HeatingCooling existingEntity, HeatingCooling heatingCooling)
{
    // If radiant heating is not enabled, clear it
    if (!heatingCooling.HasRadiantHeating)
    {
        _logger.LogInformation("Radiant heating not enabled for house {HouseId}, clearing RadiantHeating", heatingCooling.HouseId);
        if (existingEntity.RadiantHeating != null)
        {
            // Safely remove RadiantHeating
            await SafelyRemoveRadiantHeatingAsync(existingEntity.RadiantHeating);
            existingEntity.RadiantHeating = null;
        }
        return;
    }

    // If enabled but doesn't exist, create it
    if (existingEntity.RadiantHeating == null)
    {
        _logger.LogInformation("Creating new radiant heating for house {HouseId}", heatingCooling.HouseId);
        existingEntity.RadiantHeating = new RadiantHeating
        {
            Id = Guid.NewGuid(),
            HeatingCoolingId = existingEntity.Id
        };
        // Explicitly add to context as a new entity
        _context.Set<RadiantHeating>().Add(existingEntity.RadiantHeating);
    }

    // Update normally
    _logger.LogInformation("Updating radiant heating for house {HouseId}", heatingCooling.HouseId);
    UpdateRadiantHeating(existingEntity, heatingCooling);
}

private void UpdateRadiantComponent(RadiantHeating existing, RadiantHeating updated, string componentName)
{
    var existingProperty = existing.GetType().GetProperty(componentName);
    var updatedProperty = updated.GetType().GetProperty(componentName);

    if (existingProperty == null || updatedProperty == null) return;

    var updatedValue = updatedProperty.GetValue(updated);

    if (updatedValue == null)
        return;

    var existingValue = existingProperty.GetValue(existing);
    if (existingValue == null)
    {
        // Create new instance based on component name
        var componentType = existingProperty.PropertyType;
        var newInstance = Activator.CreateInstance(componentType);
        existingProperty.SetValue(existing, newInstance);
        existingValue = newInstance;
    }

    // Update properties
    var effectiveTemperatureProp = existingValue.GetType().GetProperty("EffectiveTemperature");
    var fractionOfAreaProp = existingValue.GetType().GetProperty("FractionOfArea");

    if (effectiveTemperatureProp != null)
        effectiveTemperatureProp.SetValue(existingValue, effectiveTemperatureProp.GetValue(updatedValue));
    if (fractionOfAreaProp != null)
        fractionOfAreaProp.SetValue(existingValue, fractionOfAreaProp.GetValue(updatedValue));
}


        /// <summary>
        /// Helper method to get HeatingCooling queryable with all nested includes
        /// </summary>
        private IQueryable<HeatingCooling> GetHeatingCoolingWithIncludes()
        {
            return _context.HeatingCoolings
                .AsNoTracking()
                .Include(h => h.CoolingSeason)
                    .ThenInclude(cs => cs.Start)
                .Include(h => h.CoolingSeason)
                    .ThenInclude(cs => cs.End)
                .Include(h => h.CoolingSeason)
                    .ThenInclude(cs => cs.Design)
                // Type1 components with all sub-models
                .Include(h => h.Type1)
                    .ThenInclude(t1 => t1.FansAndPump)
                        .ThenInclude(fp => fp.Mode)
                .Include(h => h.Type1)
                    .ThenInclude(t1 => t1.FansAndPump)
                        .ThenInclude(fp => fp.Power)
                .Include(h => h.Type1)
                    .ThenInclude(t1 => t1.Baseboards)
                        .ThenInclude(b => b.EquipmentInformation)
                .Include(h => h.Type1)
                    .ThenInclude(t1 => t1.Baseboards)
                        .ThenInclude(b => b.Specifications)
                            .ThenInclude(s => s.OutputCapacity)
                .Include(h => h.Type1)
                    .ThenInclude(t1 => t1.Boiler)
                        .ThenInclude(b => b.Type1EquipmentInformation)
                .Include(h => h.Type1)
                    .ThenInclude(t1 => t1.Boiler)
                        .ThenInclude(b => b.Specifications)
                            .ThenInclude(s => s.OutputCapacity)
                .Include(h => h.Type1)
                    .ThenInclude(t1 => t1.Boiler)
                        .ThenInclude(b => b.ComboTankAndPump)
                            .ThenInclude(ctp => ctp.EnergyFactor)
                .Include(h => h.Type1)
                    .ThenInclude(t1 => t1.Boiler)
                        .ThenInclude(b => b.ComboTankAndPump)
                            .ThenInclude(ctp => ctp.TankLocation)
                .Include(h => h.Type1)
                    .ThenInclude(t1 => t1.Boiler)
                        .ThenInclude(b => b.ComboTankAndPump)
                            .ThenInclude(ctp => ctp.CirculationPump)
                .Include(h => h.Type1)
                    .ThenInclude(t1 => t1.Boiler)
                        .ThenInclude(b => b.Equipment)
                            .ThenInclude(e => e.EnergySource)
                .Include(h => h.Type1)
                    .ThenInclude(t1 => t1.Boiler)
                        .ThenInclude(b => b.Equipment)
                            .ThenInclude(e => e.EquipmentTypeData)
                .Include(h => h.Type1)
                    .ThenInclude(t1 => t1.ComboHeatDhw)
                        .ThenInclude(c => c.Type1EquipmentInformation)
                .Include(h => h.Type1)
                    .ThenInclude(t1 => t1.ComboHeatDhw)
                        .ThenInclude(c => c.Specifications)
                            .ThenInclude(s => s.OutputCapacity)
                .Include(h => h.Type1)
                    .ThenInclude(t1 => t1.ComboHeatDhw)
                        .ThenInclude(c => c.ComboTankAndPump)
                            .ThenInclude(ctp => ctp.EnergyFactor)
                .Include(h => h.Type1)
                    .ThenInclude(t1 => t1.ComboHeatDhw)
                        .ThenInclude(c => c.ComboTankAndPump)
                            .ThenInclude(ctp => ctp.TankLocation)
                .Include(h => h.Type1)
                    .ThenInclude(t1 => t1.ComboHeatDhw)
                        .ThenInclude(c => c.ComboTankAndPump)
                            .ThenInclude(ctp => ctp.CirculationPump)
                .Include(h => h.Type1)
                    .ThenInclude(t1 => t1.ComboHeatDhw)
                        .ThenInclude(c => c.Equipment)
                            .ThenInclude(e => e.EnergySource)
                .Include(h => h.Type1)
                    .ThenInclude(t1 => t1.ComboHeatDhw)
                        .ThenInclude(c => c.Equipment)
                            .ThenInclude(e => e.EquipmentTypeData)
                .Include(h => h.Type1)
                    .ThenInclude(t1 => t1.Furnace)
                        .ThenInclude(f => f.Type1EquipmentInformation)
                .Include(h => h.Type1)
                    .ThenInclude(t1 => t1.Furnace)
                        .ThenInclude(f => f.Specifications)
                            .ThenInclude(s => s.OutputCapacity)
                .Include(h => h.Type1)
                    .ThenInclude(t1 => t1.Furnace)
                        .ThenInclude(f => f.ComboTankAndPump)
                            .ThenInclude(ctp => ctp.EnergyFactor)
                .Include(h => h.Type1)
                    .ThenInclude(t1 => t1.Furnace)
                        .ThenInclude(f => f.ComboTankAndPump)
                            .ThenInclude(ctp => ctp.TankLocation)
                .Include(h => h.Type1)
                    .ThenInclude(t1 => t1.Furnace)
                        .ThenInclude(f => f.ComboTankAndPump)
                            .ThenInclude(ctp => ctp.CirculationPump)
                .Include(h => h.Type1)
                    .ThenInclude(t1 => t1.Furnace)
                        .ThenInclude(f => f.Equipment)
                            .ThenInclude(e => e.EnergySource)
                .Include(h => h.Type1)
                    .ThenInclude(t1 => t1.Furnace)
                        .ThenInclude(f => f.Equipment)
                            .ThenInclude(e => e.EquipmentTypeData)
                .Include(h => h.Type1)
                    .ThenInclude(t1 => t1.P9)
                        .ThenInclude(p => p.EquipmentInformation)
                .Include(h => h.Type1)
                    .ThenInclude(t1 => t1.P9)
                        .ThenInclude(p => p.TestData)
                // Type2 components with all sub-models
                .Include(h => h.Type2)
                    .ThenInclude(t2 => t2.AirConditioning)
                        .ThenInclude(ac => ac.EquipmentInformation)
                .Include(h => h.Type2)
                    .ThenInclude(t2 => t2.AirConditioning)
                        .ThenInclude(ac => ac.CoolingParameters)
                            .ThenInclude(cp => cp.FansAndPump)
                                .ThenInclude(fp => fp.Mode)
                .Include(h => h.Type2)
                    .ThenInclude(t2 => t2.AirConditioning)
                        .ThenInclude(ac => ac.CoolingParameters)
                            .ThenInclude(cp => cp.FansAndPump)
                                .ThenInclude(fp => fp.Power)
                .Include(h => h.Type2)
                    .ThenInclude(t2 => t2.AirConditioning)
                        .ThenInclude(ac => ac.Equipment)
                            .ThenInclude(e => e.CentralType)
                .Include(h => h.Type2)
                    .ThenInclude(t2 => t2.AirConditioning)
                        .ThenInclude(ac => ac.Equipment)
                            .ThenInclude(e => e.WindowUnits)
                .Include(h => h.Type2)
                    .ThenInclude(t2 => t2.AirConditioning)
                        .ThenInclude(ac => ac.Specifications)
                .Include(h => h.Type2)
                    .ThenInclude(t2 => t2.AirHeatPump)
                        .ThenInclude(hp => hp.EquipmentInformation)
                .Include(h => h.Type2)
                    .ThenInclude(t2 => t2.AirHeatPump)
                        .ThenInclude(hp => hp.Equipment)
                .Include(h => h.Type2)
                    .ThenInclude(t2 => t2.AirHeatPump)
                        .ThenInclude(hp => hp.Specifications)
                            .ThenInclude(s => s.OutputCapacity)
                .Include(h => h.Type2)
                    .ThenInclude(t2 => t2.AirHeatPump)
                        .ThenInclude(hp => hp.Specifications)
                            .ThenInclude(s => s.HeatingEfficiency)
                .Include(h => h.Type2)
                    .ThenInclude(t2 => t2.AirHeatPump)
                        .ThenInclude(hp => hp.Specifications)
                            .ThenInclude(s => s.CoolingEfficiency)
                .Include(h => h.Type2)
                    .ThenInclude(t2 => t2.AirHeatPump)
                        .ThenInclude(hp => hp.Temperature)
                .Include(h => h.Type2)
                    .ThenInclude(t2 => t2.AirHeatPump)
                        .ThenInclude(hp => hp.SourceTemperature)
                            .ThenInclude(st => st.Temperatures)
                .Include(h => h.Type2)
                    .ThenInclude(t2 => t2.AirHeatPump)
                        .ThenInclude(hp => hp.CoolingParameters)
                            .ThenInclude(cp => cp.FansAndPump)
                                .ThenInclude(fp => fp.Mode)
                .Include(h => h.Type2)
                    .ThenInclude(t2 => t2.AirHeatPump)
                        .ThenInclude(hp => hp.CoolingParameters)
                            .ThenInclude(cp => cp.FansAndPump)
                                .ThenInclude(fp => fp.Power)
                .Include(h => h.Type2)
                    .ThenInclude(t2 => t2.AirHeatPump)
                        .ThenInclude(hp => hp.ColdClimateHeatPump)
                .Include(h => h.Type2)
                    .ThenInclude(t2 => t2.WaterHeatPump)
                        .ThenInclude(hp => hp.EquipmentInformation)
                .Include(h => h.Type2)
                    .ThenInclude(t2 => t2.WaterHeatPump)
                        .ThenInclude(hp => hp.Equipment)
                .Include(h => h.Type2)
                    .ThenInclude(t2 => t2.WaterHeatPump)
                        .ThenInclude(hp => hp.Specifications)
                            .ThenInclude(s => s.OutputCapacity)
                .Include(h => h.Type2)
                    .ThenInclude(t2 => t2.WaterHeatPump)
                        .ThenInclude(hp => hp.Specifications)
                            .ThenInclude(s => s.HeatingEfficiency)
                .Include(h => h.Type2)
                    .ThenInclude(t2 => t2.WaterHeatPump)
                        .ThenInclude(hp => hp.Specifications)
                            .ThenInclude(s => s.CoolingEfficiency)
                .Include(h => h.Type2)
                    .ThenInclude(t2 => t2.WaterHeatPump)
                        .ThenInclude(hp => hp.Temperature)
                .Include(h => h.Type2)
                    .ThenInclude(t2 => t2.WaterHeatPump)
                        .ThenInclude(hp => hp.SourceTemperature)
                            .ThenInclude(st => st.Temperatures)
                .Include(h => h.Type2)
                    .ThenInclude(t2 => t2.WaterHeatPump)
                        .ThenInclude(hp => hp.CoolingParameters)
                            .ThenInclude(cp => cp.FansAndPump)
                                .ThenInclude(fp => fp.Mode)
                .Include(h => h.Type2)
                    .ThenInclude(t2 => t2.WaterHeatPump)
                        .ThenInclude(hp => hp.CoolingParameters)
                            .ThenInclude(cp => cp.FansAndPump)
                                .ThenInclude(fp => fp.Power)
                .Include(h => h.Type2)
                    .ThenInclude(t2 => t2.WaterHeatPump)
                        .ThenInclude(hp => hp.ColdClimateHeatPump)
                .Include(h => h.Type2)
                    .ThenInclude(t2 => t2.GroundHeatPump)
                        .ThenInclude(hp => hp.EquipmentInformation)
                .Include(h => h.Type2)
                    .ThenInclude(t2 => t2.GroundHeatPump)
                        .ThenInclude(hp => hp.Equipment)
                .Include(h => h.Type2)
                    .ThenInclude(t2 => t2.GroundHeatPump)
                        .ThenInclude(hp => hp.Specifications)
                            .ThenInclude(s => s.OutputCapacity)
                .Include(h => h.Type2)
                    .ThenInclude(t2 => t2.GroundHeatPump)
                        .ThenInclude(hp => hp.Specifications)
                            .ThenInclude(s => s.HeatingEfficiency)
                .Include(h => h.Type2)
                    .ThenInclude(t2 => t2.GroundHeatPump)
                        .ThenInclude(hp => hp.Specifications)
                            .ThenInclude(s => s.CoolingEfficiency)
                .Include(h => h.Type2)
                    .ThenInclude(t2 => t2.GroundHeatPump)
                        .ThenInclude(hp => hp.Temperature)
                .Include(h => h.Type2)
                    .ThenInclude(t2 => t2.GroundHeatPump)
                        .ThenInclude(hp => hp.SourceTemperature)
                            .ThenInclude(st => st.Temperatures)
                .Include(h => h.Type2)
                    .ThenInclude(t2 => t2.GroundHeatPump)
                        .ThenInclude(hp => hp.CoolingParameters)
                            .ThenInclude(cp => cp.FansAndPump)
                                .ThenInclude(fp => fp.Mode)
                .Include(h => h.Type2)
                    .ThenInclude(t2 => t2.GroundHeatPump)
                        .ThenInclude(hp => hp.CoolingParameters)
                            .ThenInclude(cp => cp.FansAndPump)
                                .ThenInclude(fp => fp.Power)
                .Include(h => h.Type2)
                    .ThenInclude(t2 => t2.GroundHeatPump)
                        .ThenInclude(hp => hp.ColdClimateHeatPump)
                // Other related entities with all sub-models
                .Include(h => h.MultipleSystems)
                    .ThenInclude(ms => ms.EquipmentInformation)
                        .ThenInclude(eq => eq.EnergySource)
                .Include(h => h.MultipleSystems)
                    .ThenInclude(ms => ms.EquipmentInformation)
                        .ThenInclude(eq => eq.EfficiencyType)
                .Include(h => h.MultipleSystems)
                    .ThenInclude(ms => ms.EquipmentInformation)
                        .ThenInclude(eq => eq.EquipmentTypeData)
                .Include(h => h.MultipleSystems)
                    .ThenInclude(ms => ms.Summary)
                .Include(h => h.RadiantHeating)
                    .ThenInclude(rh => rh.AtticCeiling)
                .Include(h => h.RadiantHeating)
                    .ThenInclude(rh => rh.FlatRoof)
                .Include(h => h.RadiantHeating)
                    .ThenInclude(rh => rh.AboveCrawlspace)
                .Include(h => h.RadiantHeating)
                    .ThenInclude(rh => rh.SlabOnGrade)
                .Include(h => h.RadiantHeating)
                    .ThenInclude(rh => rh.AboveBasement)
                .Include(h => h.RadiantHeating)
                    .ThenInclude(rh => rh.Basement)
                .Include(h => h.AdditionalOpenings)
                .Include(h => h.SupplementaryHeating)
                    .ThenInclude(sh => sh.EquipmentInformation)
                .Include(h => h.SupplementaryHeating)
                    .ThenInclude(sh => sh.Equipment)
                        .ThenInclude(e => e.EnergySource)
                .Include(h => h.SupplementaryHeating)
                    .ThenInclude(sh => sh.Equipment)
                        .ThenInclude(e => e.TypeData)
                .Include(h => h.SupplementaryHeating)
                    .ThenInclude(sh => sh.Specifications)
                        .ThenInclude(s => s.OutputCapacity)
                .Include(h => h.SupplementaryHeating)
                    .ThenInclude(sh => sh.Specifications)
                        .ThenInclude(s => s.MonthlyUsage)
                .Include(h => h.SupplementaryHeating)
                    .ThenInclude(sh => sh.Specifications)
                        .ThenInclude(s => s.Flue);
        }

        /// <summary>
        /// Apply selective creation logic - only create systems that are selected
        /// Similar to the old system's logic where only selected systems get saved
        /// </summary>
        private async Task ApplySelectiveCreationLogicAsync(HeatingCooling heatingCooling)
        {
            _logger.LogInformation("Applying selective creation logic for house {HouseId}", heatingCooling.HouseId);

            // Validate that the request data matches the selections
            // ValidateSelectionConsistency(heatingCooling);

            // Clear unselected Type1 systems
            if (heatingCooling.Type1 != null)
            {
                var selectedType1 = heatingCooling.GetSelectedType1System();
                ClearUnselectedType1SystemsForCreation(heatingCooling.Type1, selectedType1);
            }

            // FIXED: Follow Type1 approach - if Type2 data is sent, keep it (regardless of pumpType)
            if (heatingCooling.Type2 != null)
            {
                var selectedType2 = heatingCooling.GetSelectedType2System();
                // Always clear unselected systems, but keep Type2 if data was sent
                ClearUnselectedType2SystemsForCreation(heatingCooling.Type2, selectedType2);
            }

            // Clear RadiantHeating if not enabled
            if (!heatingCooling.HasRadiantHeating)
            {
                heatingCooling.RadiantHeating = null;
            }

            // Clear MultipleSystems if not enabled
            if (!heatingCooling.HasMultipleSystems)
            {
                heatingCooling.MultipleSystems = null;
            }

            // Clear AdditionalOpenings if not enabled
            if (!heatingCooling.HasAdditionalOpenings)
            {
                heatingCooling.AdditionalOpenings = null;
            }

            // Limit SupplementaryHeating to the specified count
            if (heatingCooling.SupplementaryHeating != null && heatingCooling.SupplementaryHeating.Count > heatingCooling.SupplementarySystemsCount)
            {
                heatingCooling.SupplementaryHeating = heatingCooling.SupplementaryHeating
                    .Take(heatingCooling.SupplementarySystemsCount)
                    .ToList();
            }
            else if (heatingCooling.SupplementarySystemsCount == 0)
            {
                heatingCooling.SupplementaryHeating = null;
            }

            _logger.LogInformation("Selective creation logic applied for house {HouseId}: Type1={Type1}, Type2={Type2}, Radiant={Radiant}, Multiple={Multiple}, Supplementary={Supplementary}",
                heatingCooling.HouseId,
                heatingCooling.GetSelectedType1System(),
                heatingCooling.GetSelectedType2System(),
                heatingCooling.HasRadiantHeating,
                heatingCooling.HasMultipleSystems,
                heatingCooling.SupplementarySystemsCount);
        }

        /// <summary>
        /// Clear unselected Type1 systems for creation (set to null)
        /// </summary>
        private void ClearUnselectedType1SystemsForCreation(Type1 type1, Type1SystemType selectedType)
        {
            if (selectedType != Type1SystemType.Baseboards)
                type1.Baseboards = null;

            if (selectedType != Type1SystemType.Furnace)
                type1.Furnace = null;

            if (selectedType != Type1SystemType.Boiler)
                type1.Boiler = null;

            if (selectedType != Type1SystemType.ComboHeatDhw)
                type1.ComboHeatDhw = null;

            if (selectedType != Type1SystemType.P9)
                type1.P9 = null;

            // IMS would be added here when implemented
        }

        /// <summary>
        /// Clear unselected Type2 systems for creation (set to null)
        /// </summary>
        private void ClearUnselectedType2SystemsForCreation(Type2 type2, Type2SystemType selectedType)
        {
            if (selectedType != Type2SystemType.AirSourceHeatPump)
                type2.AirHeatPump = null;

            if (selectedType != Type2SystemType.WaterSourceHeatPump)
                type2.WaterHeatPump = null;

            if (selectedType != Type2SystemType.GroundSourceHeatPump)
                type2.GroundHeatPump = null;

            if (selectedType != Type2SystemType.AirConditioning)
                type2.AirConditioning = null;
        }

        /// <summary>
        /// Safely remove Type2 entity and all its related heat pump/AC systems
        /// </summary>
        private async Task SafelyRemoveType2Async(Type2 type2)
        {
            try
            {
                // Remove all heat pump systems first
                if (type2.AirHeatPump != null)
                {
                    var airHeatPump = await _context.Set<AirHeatPump>()
                        .FirstOrDefaultAsync(hp => hp.Id == type2.AirHeatPump.Id);
                    if (airHeatPump != null)
                    {
                        _context.Set<AirHeatPump>().Remove(airHeatPump);
                    }
                }

                if (type2.WaterHeatPump != null)
                {
                    var waterHeatPump = await _context.Set<WaterHeatPump>()
                        .FirstOrDefaultAsync(hp => hp.Id == type2.WaterHeatPump.Id);
                    if (waterHeatPump != null)
                    {
                        _context.Set<WaterHeatPump>().Remove(waterHeatPump);
                    }
                }

                if (type2.GroundHeatPump != null)
                {
                    var groundHeatPump = await _context.Set<GroundHeatPump>()
                        .FirstOrDefaultAsync(hp => hp.Id == type2.GroundHeatPump.Id);
                    if (groundHeatPump != null)
                    {
                        _context.Set<GroundHeatPump>().Remove(groundHeatPump);
                    }
                }

                if (type2.AirConditioning != null)
                {
                    var airConditioning = await _context.Set<AirConditioning>()
                        .FirstOrDefaultAsync(ac => ac.Id == type2.AirConditioning.Id);
                    if (airConditioning != null)
                    {
                        _context.Set<AirConditioning>().Remove(airConditioning);
                    }
                }

                // Finally remove the Type2 entity itself
                var type2Entity = await _context.Set<Type2>()
                    .FirstOrDefaultAsync(t => t.Id == type2.Id);
                if (type2Entity != null)
                {
                    _context.Set<Type2>().Remove(type2Entity);
                }

                _logger.LogInformation("Successfully marked Type2 entity and related systems for removal");
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Error during Type2 removal, continuing with update");
                // Don't throw - just log and continue
            }
        }

        /// <summary>
        /// Safely remove RadiantHeating entity
        /// </summary>
        private async Task SafelyRemoveRadiantHeatingAsync(RadiantHeating radiantHeating)
        {
            try
            {
                var entity = await _context.Set<RadiantHeating>()
                    .FirstOrDefaultAsync(rh => rh.Id == radiantHeating.Id);
                if (entity != null)
                {
                    _context.Set<RadiantHeating>().Remove(entity);
                    _logger.LogInformation("Successfully marked RadiantHeating entity for removal");
                }
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Error during RadiantHeating removal, continuing with update");
                // Don't throw - just log and continue
            }
        }

        /// <summary>
        /// Safely remove MultipleSystems entity and related equipment
        /// </summary>
        private async Task SafelyRemoveMultipleSystemsAsync(MultipleSystems multipleSystems)
        {
            try
            {
                // Remove equipment first
                var equipment = await _context.Set<MultipleSystemsEquipment>()
                    .Where(eq => eq.MultipleSystemsId == multipleSystems.Id)
                    .ToListAsync();

                if (equipment.Any())
                {
                    _context.Set<MultipleSystemsEquipment>().RemoveRange(equipment);
                }

                // Remove the MultipleSystems entity
                var entity = await _context.Set<MultipleSystems>()
                    .FirstOrDefaultAsync(ms => ms.Id == multipleSystems.Id);
                if (entity != null)
                {
                    _context.Set<MultipleSystems>().Remove(entity);
                    _logger.LogInformation("Successfully marked MultipleSystems entity and equipment for removal");
                }
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Error during MultipleSystems removal, continuing with update");
                // Don't throw - just log and continue
            }
        }

        /// <summary>
        /// Get a valid energy source, guaranteed to not be null
        /// </summary>
        private SupplementaryEnergySources GetValidEnergySource()
        {
            // Try Electric first
            if (SupplementaryEnergySources.Electric != null)
                return SupplementaryEnergySources.Electric;

            // Try any from the list
            var firstAvailable = SupplementaryEnergySources.All?.FirstOrDefault();
            if (firstAvailable != null)
                return firstAvailable;

            // Try Natural Gas
            if (SupplementaryEnergySources.NaturalGas != null)
                return SupplementaryEnergySources.NaturalGas;

            // This should never happen, but if it does, we have a serious problem
            throw new InvalidOperationException("No valid SupplementaryEnergySources available");
        }

        /// <summary>
        /// Get a valid heating usage, guaranteed to not be null
        /// </summary>
        private HeatingUsages GetValidHeatingUsage()
        {
            // Try Never first (code "1")
            if (HeatingUsages.Never != null)
                return HeatingUsages.Never;

            // Try any from the list
            var firstAvailable = HeatingUsages.All?.FirstOrDefault();
            if (firstAvailable != null)
                return firstAvailable;

            // This should never happen, but if it does, we have a serious problem
            throw new InvalidOperationException("No valid HeatingUsages available");
        }

        /// <summary>
        /// Get a valid heating location, guaranteed to not be null
        /// </summary>
        private HeatingLocations GetValidHeatingLocation()
        {
            // Try MainFloors first (code "1")
            if (HeatingLocations.MainFloors != null)
                return HeatingLocations.MainFloors;

            // Try any from the list
            var firstAvailable = HeatingLocations.All?.FirstOrDefault();
            if (firstAvailable != null)
                return firstAvailable;

            // This should never happen, but if it does, we have a serious problem
            throw new InvalidOperationException("No valid HeatingLocations available");
        }

        public async Task<HeatingCooling?> GetHeatingCoolingByEnergyUpgradeIdAsync(Guid energyUpgradeId)
        {
            return await GetHeatingCoolingWithIncludes()
                .FirstOrDefaultAsync(hc => hc.EnergyUpgradeId == energyUpgradeId);
        }

        public async Task<HeatingCooling> DuplicateHeatingCoolingForEnergyUpgradeAsync(HeatingCooling baseHeatingCooling, Guid energyUpgradeId)
        {
            var duplicatedHeatingCooling = new HeatingCooling
            {
                Id = Guid.NewGuid(),
                HouseId = baseHeatingCooling.HouseId,
                EnergyUpgradeId = energyUpgradeId,
                Label = baseHeatingCooling.Label,
                HeatType = baseHeatingCooling.HeatType,
                PumpType = baseHeatingCooling.PumpType,
                HasRadiantHeating = baseHeatingCooling.HasRadiantHeating,
                HasMultipleSystems = baseHeatingCooling.HasMultipleSystems,
                SupplementarySystemsCount = baseHeatingCooling.SupplementarySystemsCount,
                HasAdditionalOpenings = baseHeatingCooling.HasAdditionalOpenings
            };

            // Deep copy CoolingSeason
            duplicatedHeatingCooling.CoolingSeason = DuplicateCoolingSeason(baseHeatingCooling.CoolingSeason, duplicatedHeatingCooling.Id);

            // Deep copy Type1
            duplicatedHeatingCooling.Type1 = DuplicateType1(baseHeatingCooling.Type1, duplicatedHeatingCooling.Id);

            // Deep copy Type2
            duplicatedHeatingCooling.Type2 = DuplicateType2(baseHeatingCooling.Type2, duplicatedHeatingCooling.Id);

            // Deep copy optional components
            if (baseHeatingCooling.MultipleSystems != null)
            {
                duplicatedHeatingCooling.MultipleSystems = DuplicateMultipleSystems(baseHeatingCooling.MultipleSystems, duplicatedHeatingCooling.Id);
            }

            if (baseHeatingCooling.RadiantHeating != null)
            {
                duplicatedHeatingCooling.RadiantHeating = DuplicateRadiantHeating(baseHeatingCooling.RadiantHeating, duplicatedHeatingCooling.Id);
            }

            if (baseHeatingCooling.AdditionalOpenings != null && baseHeatingCooling.AdditionalOpenings.Any())
            {
                duplicatedHeatingCooling.AdditionalOpenings = baseHeatingCooling.AdditionalOpenings
                    .Select(ao => DuplicateAdditionalOpening(ao, duplicatedHeatingCooling.Id))
                    .ToList();
            }

            if (baseHeatingCooling.SupplementaryHeating != null && baseHeatingCooling.SupplementaryHeating.Any())
            {
                duplicatedHeatingCooling.SupplementaryHeating = baseHeatingCooling.SupplementaryHeating
                    .Select(sh => DuplicateSupplementaryHeat(sh, duplicatedHeatingCooling.Id))
                    .ToList();
            }

            _context.HeatingCoolings.Add(duplicatedHeatingCooling);
            await _context.SaveChangesAsync();

            return duplicatedHeatingCooling;
        }

        private CoolingSeason DuplicateCoolingSeason(CoolingSeason source, Guid heatingCoolingId)
        {
            return new CoolingSeason
            {
                Id = Guid.NewGuid(),
                HeatingCoolingId = heatingCoolingId,
                Start = new Months(source.Start),
                End = new Months(source.End),
                Design = new Months(source.Design)
            };
        }

        private Type1 DuplicateType1(Type1 source, Guid heatingCoolingId)
        {
            var duplicated = new Type1
            {
                Id = Guid.NewGuid(),
                HeatingCoolingId = heatingCoolingId
            };

            // Deep copy owned entities based on system type
            if (source.Baseboards != null)
            {
                duplicated.Baseboards = DuplicateBaseboards(source.Baseboards, duplicated.Id);
            }

            if (source.Furnace != null)
            {
                duplicated.Furnace = DuplicateFurnace(source.Furnace, duplicated.Id);
            }

            if (source.Boiler != null)
            {
                duplicated.Boiler = DuplicateBoiler(source.Boiler, duplicated.Id);
            }

            if (source.ComboHeatDhw != null)
            {
                duplicated.ComboHeatDhw = DuplicateComboHeatDhw(source.ComboHeatDhw, duplicated.Id);
            }

            return duplicated;
        }

        private Type2 DuplicateType2(Type2 source, Guid heatingCoolingId)
        {
            var duplicated = new Type2
            {
                Id = Guid.NewGuid(),
                HeatingCoolingId = heatingCoolingId,
                ShadingInF280Cooling = source.ShadingInF280Cooling
            };

            // Deep copy owned entities based on system type
            if (source.AirHeatPump != null)
            {
                duplicated.AirHeatPump = DuplicateAirHeatPump(source.AirHeatPump, duplicated.Id);
            }

            if (source.WaterHeatPump != null)
            {
                duplicated.WaterHeatPump = DuplicateWaterHeatPump(source.WaterHeatPump, duplicated.Id);
            }

            if (source.GroundHeatPump != null)
            {
                duplicated.GroundHeatPump = DuplicateGroundHeatPump(source.GroundHeatPump, duplicated.Id);
            }

            if (source.AirConditioning != null)
            {
                duplicated.AirConditioning = DuplicateAirConditioning(source.AirConditioning, duplicated.Id);
            }

            return duplicated;
        }

        // Helper methods for deep copying complex owned entities would go here
        // These would be quite extensive given the complexity of the HVAC models
        // For brevity, I'll implement a few key ones as examples:

        private Baseboards DuplicateBaseboards(Baseboards source, Guid type1Id)
        {
            var duplicated = new Baseboards(source)
            {
                Id = Guid.NewGuid(),
                Type1Id = type1Id
            };
            return duplicated;
        }

        private Furnace DuplicateFurnace(Furnace source, Guid type1Id)
        {
            var duplicated = new Furnace(source)
            {
                Id = Guid.NewGuid(),
                Type1Id = type1Id
            };
            return duplicated;
        }

        private Boiler DuplicateBoiler(Boiler source, Guid type1Id)
        {
            var duplicated = new Boiler(source)
            {
                Id = Guid.NewGuid(),
                Type1Id = type1Id
            };
            return duplicated;
        }

        private ComboHeatDhw DuplicateComboHeatDhw(ComboHeatDhw source, Guid type1Id)
        {
            var duplicated = new ComboHeatDhw(source)
            {
                Id = Guid.NewGuid(),
                Type1Id = type1Id
            };
            return duplicated;
        }

        private AirHeatPump DuplicateAirHeatPump(AirHeatPump source, Guid type2Id)
        {
            var duplicated = new AirHeatPump(source)
            {
                Id = Guid.NewGuid(),
                Type2Id = type2Id
            };
            return duplicated;
        }

        private WaterHeatPump DuplicateWaterHeatPump(WaterHeatPump source, Guid type2Id)
        {
            var duplicated = new WaterHeatPump(source)
            {
                Id = Guid.NewGuid(),
                Type2Id = type2Id
            };
            return duplicated;
        }

        private GroundHeatPump DuplicateGroundHeatPump(GroundHeatPump source, Guid type2Id)
        {
            var duplicated = new GroundHeatPump(source)
            {
                Id = Guid.NewGuid(),
                Type2Id = type2Id
            };
            return duplicated;
        }

        private AirConditioning DuplicateAirConditioning(AirConditioning source, Guid type2Id)
        {
            var duplicated = new AirConditioning(source)
            {
                Id = Guid.NewGuid(),
                Type2Id = type2Id
            };
            return duplicated;
        }

        private MultipleSystems DuplicateMultipleSystems(MultipleSystems source, Guid heatingCoolingId)
        {
            var duplicated = new MultipleSystems(source)
            {
                Id = Guid.NewGuid(),
                HeatingCoolingId = heatingCoolingId
            };
            return duplicated;
        }

        private RadiantHeating DuplicateRadiantHeating(RadiantHeating source, Guid heatingCoolingId)
        {
            var duplicated = new RadiantHeating(source)
            {
                Id = Guid.NewGuid(),
                HeatingCoolingId = heatingCoolingId
            };
            return duplicated;
        }

        private AdditionalOpening DuplicateAdditionalOpening(AdditionalOpening source, Guid heatingCoolingId)
        {
            var duplicated = new AdditionalOpening(source)
            {
                Id = Guid.NewGuid(),
                HeatingCoolingId = heatingCoolingId,
                Rank = source.Rank
            };
            return duplicated;
        }

        private SupplementaryHeat DuplicateSupplementaryHeat(SupplementaryHeat source, Guid heatingCoolingId)
        {
            var duplicated = new SupplementaryHeat(source)
            {
                Id = Guid.NewGuid(),
                HeatingCoolingId = heatingCoolingId,
                Rank = source.Rank
            };
            return duplicated;
        }
    }
}