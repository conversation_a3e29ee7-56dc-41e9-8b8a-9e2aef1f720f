using Microsoft.EntityFrameworkCore;
using HvacService.Core.Models;

namespace HvacService.Infrastructure.Data
{
    public class HvacDbContext : DbContext
    {
        public HvacDbContext(DbContextOptions<HvacDbContext> options) : base(options)
        {
        }

        protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder)
        {
            optionsBuilder.ConfigureWarnings(warnings =>
                warnings.Ignore(Microsoft.EntityFrameworkCore.Diagnostics.RelationalEventId.ForeignKeyPropertiesMappedToUnrelatedTables));
        }

        // Main HVAC entities
        public DbSet<HeatingCooling> HeatingCoolings { get; set; } = null!;
        public DbSet<CoolingSeason> CoolingSeasons { get; set; } = null!;
        public DbSet<Type1> Type1s { get; set; } = null!;
        public DbSet<Type2> Type2s { get; set; } = null!;
        public DbSet<MultipleSystems> MultipleSystems { get; set; } = null!;
        public DbSet<RadiantHeating> RadiantHeatings { get; set; } = null!;
        public DbSet<AdditionalOpening> AdditionalOpenings { get; set; } = null!;
        public DbSet<SupplementaryHeat> SupplementaryHeats { get; set; } = null!;

        // Type1 heating system entities
        public DbSet<FansAndPumpsHeating> FansAndPumpsHeatings { get; set; } = null!;
        public DbSet<Baseboards> Baseboards { get; set; } = null!;
        public DbSet<Boiler> Boilers { get; set; } = null!;
        public DbSet<ComboHeatDhw> ComboHeatDhws { get; set; } = null!;
        public DbSet<Furnace> Furnaces { get; set; } = null!;
        public DbSet<P9> P9Systems { get; set; } = null!;

        // Type2 cooling/heating system entities
        public DbSet<AirHeatPump> AirHeatPumps { get; set; } = null!;
        public DbSet<WaterHeatPump> WaterHeatPumps { get; set; } = null!;
        public DbSet<GroundHeatPump> GroundHeatPumps { get; set; } = null!;
        public DbSet<AirConditioning> AirConditionings { get; set; } = null!;

        // Multiple systems entities
        public DbSet<MultipleSystemsEquipment> MultipleSystemsEquipments { get; set; } = null!;

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            // Set default schema for all entities in this context
            modelBuilder.HasDefaultSchema("hvac");

            // Ignore resource classes - they are not database entities
            modelBuilder.Ignore<DhwTankVolumes>();
            modelBuilder.Ignore<ResourceList>();
            modelBuilder.Ignore<ResourceValueList>();

            // Ignore base classes that are used for composition, not inheritance
            // Note: CodeAndText and CodeTextAndValue are only used as owned entities
            modelBuilder.Ignore<CodeAndText>();
            modelBuilder.Ignore<CodeTextAndValue>();

            base.OnModelCreating(modelBuilder);

            // ===============================
            // MAIN HEATING COOLING CONFIGURATION
            // ===============================

            modelBuilder.Entity<HeatingCooling>(entity =>
            {
                entity.ToTable("HeatingCoolings", "hvac");
                entity.HasKey(e => e.Id);
                entity.Property(e => e.EnergyUpgradeId).IsRequired(false);
                entity.Property(e => e.Label).HasMaxLength(100);

                // One-to-one relationships
                entity.HasOne(e => e.CoolingSeason)
                      .WithOne(cs => cs.HeatingCooling)
                      .HasForeignKey<CoolingSeason>(cs => cs.HeatingCoolingId)
                      .OnDelete(DeleteBehavior.Cascade);

                entity.HasOne(e => e.Type1)
                      .WithOne(t1 => t1.HeatingCooling)
                      .HasForeignKey<Type1>(t1 => t1.HeatingCoolingId)
                      .OnDelete(DeleteBehavior.Cascade);

                entity.HasOne(e => e.Type2)
                      .WithOne(t2 => t2.HeatingCooling)
                      .HasForeignKey<Type2>(t2 => t2.HeatingCoolingId)
                      .OnDelete(DeleteBehavior.Cascade);

                // Optional one-to-one relationships
                entity.HasOne(e => e.MultipleSystems)
                      .WithOne(ms => ms.HeatingCooling)
                      .HasForeignKey<MultipleSystems>(ms => ms.HeatingCoolingId)
                      .OnDelete(DeleteBehavior.Cascade);

                entity.HasOne(e => e.RadiantHeating)
                      .WithOne(rh => rh.HeatingCooling)
                      .HasForeignKey<RadiantHeating>(rh => rh.HeatingCoolingId)
                      .OnDelete(DeleteBehavior.Cascade);

                // One-to-many relationships
                entity.HasMany(e => e.AdditionalOpenings)
                      .WithOne(ao => ao.HeatingCooling)
                      .HasForeignKey(ao => ao.HeatingCoolingId)
                      .OnDelete(DeleteBehavior.Cascade);

                entity.HasMany(e => e.SupplementaryHeating)
                      .WithOne(sh => sh.HeatingCooling)
                      .HasForeignKey(sh => sh.HeatingCoolingId)
                      .OnDelete(DeleteBehavior.Cascade);
            });

            // ===============================
            // COOLING SEASON CONFIGURATION
            // ===============================

            modelBuilder.Entity<CoolingSeason>(entity =>
            {
                entity.ToTable("CoolingSeasons", "hvac");
                entity.HasKey(e => e.Id);

                entity.OwnsOne(e => e.Start, months =>
                {
                    months.Property(m => m.Code).HasMaxLength(10);
                    months.Property(m => m.English).HasMaxLength(50);
                    months.Property(m => m.French).HasMaxLength(50);
                    months.Property(m => m.IsUserSpecified);
                });

                entity.OwnsOne(e => e.End, months =>
                {
                    months.Property(m => m.Code).HasMaxLength(10);
                    months.Property(m => m.English).HasMaxLength(50);
                    months.Property(m => m.French).HasMaxLength(50);
                    months.Property(m => m.IsUserSpecified);
                });

                entity.OwnsOne(e => e.Design, months =>
                {
                    months.Property(m => m.Code).HasMaxLength(10);
                    months.Property(m => m.English).HasMaxLength(50);
                    months.Property(m => m.French).HasMaxLength(50);
                    months.Property(m => m.IsUserSpecified);
                });
            });

            // ===============================
            // TYPE1 SYSTEM CONFIGURATIONS
            // ===============================

            modelBuilder.Entity<Type1>(entity =>
            {
                entity.ToTable("Type1s", "hvac");
                entity.HasKey(e => e.Id);

                entity.HasOne(e => e.FansAndPump)
                      .WithOne(f => f.Type1)
                      .HasForeignKey<FansAndPumpsHeating>(f => f.Type1Id)
                      .OnDelete(DeleteBehavior.Cascade);

                entity.HasOne(e => e.Baseboards)
                      .WithOne(b => b.Type1)
                      .HasForeignKey<Baseboards>(b => b.Type1Id)
                      .OnDelete(DeleteBehavior.Cascade);

                entity.HasOne(e => e.Boiler)
                      .WithOne(b => b.Type1)
                      .HasForeignKey<Boiler>(b => b.Type1Id)
                      .OnDelete(DeleteBehavior.Cascade);

                entity.HasOne(e => e.ComboHeatDhw)
                      .WithOne(c => c.Type1)
                      .HasForeignKey<ComboHeatDhw>(c => c.Type1Id)
                      .OnDelete(DeleteBehavior.Cascade);

                entity.HasOne(e => e.Furnace)
                      .WithOne(f => f.Type1)
                      .HasForeignKey<Furnace>(f => f.Type1Id)
                      .OnDelete(DeleteBehavior.Cascade);

                entity.HasOne(e => e.P9)
                      .WithOne(p => p.Type1)
                      .HasForeignKey<P9>(p => p.Type1Id)
                      .OnDelete(DeleteBehavior.Cascade);
            });

            // Configure FansAndPumpsHeating
            modelBuilder.Entity<FansAndPumpsHeating>(entity =>
            {
                entity.ToTable("FansAndPumpsHeatings", "hvac");
                entity.HasKey(e => e.Id);
                
                entity.Property(e => e.HasEnergyEfficientMotor)
                    .HasDefaultValue(false);

                entity.OwnsOne(e => e.Mode, mode =>
                {
                    mode.Property(m => m.Code).HasMaxLength(10);
                    mode.Property(m => m.English).HasMaxLength(50);
                    mode.Property(m => m.French).HasMaxLength(50);
                    mode.Property(m => m.IsUserSpecified);
                });

                entity.OwnsOne(e => e.Power, power =>
                {
                    power.Property(p => p.IsCalculated);
                    power.Property(p => p.Low).HasPrecision(18, 2);
                    power.Property(p => p.High).HasPrecision(18, 2);
                });
            });

            // Configure Baseboards
            modelBuilder.Entity<Baseboards>(entity =>
            {
                entity.ToTable("Baseboards", "hvac");
                entity.HasKey(e => e.Id);

                entity.OwnsOne(e => e.EquipmentInformation, eq =>
                {
                    eq.Property(e => e.Manufacturer).HasMaxLength(200);
                    eq.Property(e => e.Model).HasMaxLength(200);
                    eq.Property(e => e.Description).HasMaxLength(500);
                    eq.Property(e => e.NumberOfElectronicThermostats);
                });

                entity.OwnsOne(e => e.Specifications, spec =>
                {
                    spec.Property(s => s.SizingFactor).HasPrecision(18, 2);
                    spec.Property(s => s.Efficiency).HasPrecision(18, 2);

                    spec.OwnsOne(s => s.OutputCapacity, oc =>
                    {
                        oc.Property(o => o.Code).HasMaxLength(10);
                        oc.Property(o => o.EnglishText).HasMaxLength(100);
                        oc.Property(o => o.FrenchText).HasMaxLength(100);
                        oc.Property(o => o.Text).HasMaxLength(100);
                        oc.Property(o => o.Value).HasPrecision(18, 2);
                        oc.Property(o => o.UiUnits).HasMaxLength(20);
                    });
                });
            });

            // Configure Boiler
            modelBuilder.Entity<Boiler>(entity =>
            {
                entity.ToTable("Boilers", "hvac");
                entity.HasKey(e => e.Id);

                // Configure Type1EquipmentInformation as owned entity (no explicit type constraint)
                entity.OwnsOne(e => e.Type1EquipmentInformation, ei =>
                {
                    ei.Property(e => e.Manufacturer).HasMaxLength(200);
                    ei.Property(e => e.Model).HasMaxLength(200);
                    ei.Property(e => e.Description).HasMaxLength(500);
                    ei.Property(e => e.EnergyStar);
                    ei.Property(e => e.AHRI);
                    ei.Property(e => e.EpaCsa);
                });

                entity.OwnsOne(e => e.Specifications, spec =>
                {
                    spec.Property(s => s.SizingFactor).HasPrecision(18, 2);
                    spec.Property(s => s.Efficiency).HasPrecision(18, 2);
                    spec.Property(s => s.IsSteadyState);
                    spec.Property(s => s.PilotLight).HasPrecision(18, 2);
                    spec.Property(s => s.FlueDiameter).HasPrecision(18, 2);

                    spec.OwnsOne(s => s.OutputCapacity, oc =>
                    {
                        oc.Property(o => o.Code).HasMaxLength(10);
                        oc.Property(o => o.EnglishText).HasMaxLength(100);
                        oc.Property(o => o.FrenchText).HasMaxLength(100);
                        oc.Property(o => o.Text).HasMaxLength(100);
                        oc.Property(o => o.Value).HasPrecision(18, 2);
                        oc.Property(o => o.UiUnits).HasMaxLength(20);
                    });
                });

                entity.OwnsOne(e => e.ComboTankAndPump, ctp =>
                {
                    ctp.Property(c => c.WaterTemperature).HasPrecision(18, 2);

                    // Configure TankCapacity as a complex type with Value property
                    ctp.OwnsOne(c => c.TankCapacity, tc =>
                    {
                        tc.Property(t => t.Code).HasMaxLength(10);
                        tc.Property(t => t.English).HasMaxLength(100);
                        tc.Property(t => t.French).HasMaxLength(100);
                        tc.Property(t => t.IsUserSpecified);
                        tc.Property(t => t.Value).HasPrecision(18, 2);
                    });

                    // Ignore the computed property
                    ctp.Ignore(c => c.TankCapacityXml);

                    // Configure nested owned entities
                    ctp.OwnsOne(c => c.TankLocation, tl =>
                    {
                        tl.Property(t => t.Code).HasMaxLength(10);
                        tl.Property(t => t.EnglishText).HasMaxLength(100);
                        tl.Property(t => t.FrenchText).HasMaxLength(100);
                        tl.Property(t => t.Text).HasMaxLength(100);
                    });

                    ctp.OwnsOne(c => c.EnergyFactor, ef =>
                    {
                        ef.Property(e => e.Value).HasPrecision(18, 2);
                    });

                    ctp.OwnsOne(c => c.CirculationPump, cp =>
                    {
                        cp.Property(c => c.IsCalculated);
                        cp.Property(c => c.Value).HasPrecision(18, 2);
                        cp.Property(c => c.HasEnergyEfficientMotor);
                    });
                });

                // FIXED: Configure BoilerEquipment properly (inherits from CommonEquipment)
                entity.OwnsOne(e => e.Equipment, eq =>
                {
                    // CommonEquipment properties
                    eq.Property(e => e.IsBiEnergy);
                    eq.Property(e => e.SwitchoverTemperature).HasPrecision(18, 2);

                    // Ignore the interface property - Entity Framework cannot handle interfaces
                    eq.Ignore(e => e.EquipmentType);

                    eq.OwnsOne(e => e.EnergySource, es =>
                    {
                        es.Property(e => e.Code).HasMaxLength(10);
                        es.Property(e => e.English).HasMaxLength(50);
                        es.Property(e => e.French).HasMaxLength(50);
                        es.Property(e => e.IsUserSpecified);
                    });

                    // Configure the actual data property instead of the interface
                    eq.OwnsOne(e => e.EquipmentTypeData, etd =>
                    {
                        etd.Property(e => e.Code).HasMaxLength(10);
                        etd.Property(e => e.EnglishText).HasMaxLength(100);
                        etd.Property(e => e.FrenchText).HasMaxLength(100);
                        etd.Property(e => e.Text).HasMaxLength(100);
                    });
                });
            });

            // Configure ComboHeatDhw
            modelBuilder.Entity<ComboHeatDhw>(entity =>
            {
                entity.ToTable("ComboHeatDhws", "hvac");
                entity.HasKey(e => e.Id);

                // Configure Type1EquipmentInformation as owned entity (no explicit type constraint)
                entity.OwnsOne(e => e.Type1EquipmentInformation, ei =>
                {
                    ei.Property(e => e.Manufacturer).HasMaxLength(200);
                    ei.Property(e => e.Model).HasMaxLength(200);
                    ei.Property(e => e.Description).HasMaxLength(500);
                    ei.Property(e => e.EnergyStar);
                    ei.Property(e => e.AHRI);
                    ei.Property(e => e.EpaCsa);
                });

                entity.OwnsOne(e => e.Specifications, spec =>
                {
                    spec.Property(s => s.SizingFactor).HasPrecision(18, 2);
                    spec.Property(s => s.Efficiency).HasPrecision(18, 2);
                    spec.Property(s => s.IsSteadyState);
                    spec.Property(s => s.PilotLight).HasPrecision(18, 2);
                    spec.Property(s => s.FlueDiameter).HasPrecision(18, 2);

                    spec.OwnsOne(s => s.OutputCapacity, oc =>
                    {
                        oc.Property(o => o.Code).HasMaxLength(10);
                        oc.Property(o => o.EnglishText).HasMaxLength(100);
                        oc.Property(o => o.FrenchText).HasMaxLength(100);
                        oc.Property(o => o.Text).HasMaxLength(100);
                        oc.Property(o => o.Value).HasPrecision(18, 2);
                        oc.Property(o => o.UiUnits).HasMaxLength(20);
                    });
                });

                entity.OwnsOne(e => e.ComboTankAndPump, ctp =>
                {
                    ctp.Property(c => c.WaterTemperature).HasPrecision(18, 2);

                    // Configure TankCapacity as a complex type with Value property
                    ctp.OwnsOne(c => c.TankCapacity, tc =>
                    {
                        tc.Property(t => t.Code).HasMaxLength(10);
                        tc.Property(t => t.English).HasMaxLength(100);
                        tc.Property(t => t.French).HasMaxLength(100);
                        tc.Property(t => t.IsUserSpecified);
                        tc.Property(t => t.Value).HasPrecision(18, 2);
                    });

                    // Ignore the computed property
                    ctp.Ignore(c => c.TankCapacityXml);

                    // Configure nested owned entities
                    ctp.OwnsOne(c => c.TankLocation, tl =>
                    {
                        tl.Property(t => t.Code).HasMaxLength(10);
                        tl.Property(t => t.EnglishText).HasMaxLength(100);
                        tl.Property(t => t.FrenchText).HasMaxLength(100);
                        tl.Property(t => t.Text).HasMaxLength(100);
                    });

                    ctp.OwnsOne(c => c.EnergyFactor, ef =>
                    {
                        ef.Property(e => e.Value).HasPrecision(18, 2);
                    });

                    ctp.OwnsOne(c => c.CirculationPump, cp =>
                    {
                        cp.Property(c => c.IsCalculated);
                        cp.Property(c => c.Value).HasPrecision(18, 2);
                        cp.Property(c => c.HasEnergyEfficientMotor);
                    });
                });

                // FIXED: Configure ComboHeatDhwEquipment properly (inherits from CommonEquipment)
                entity.OwnsOne(e => e.Equipment, eq =>
                {
                    // CommonEquipment properties
                    eq.Property(e => e.IsBiEnergy);
                    eq.Property(e => e.SwitchoverTemperature).HasPrecision(18, 2);

                    // Ignore the interface property - Entity Framework cannot handle interfaces
                    eq.Ignore(e => e.EquipmentType);

                    eq.OwnsOne(e => e.EnergySource, es =>
                    {
                        es.Property(e => e.Code).HasMaxLength(10);
                        es.Property(e => e.English).HasMaxLength(50);
                        es.Property(e => e.French).HasMaxLength(50);
                        es.Property(e => e.IsUserSpecified);
                    });

                    // Configure the actual data property instead of the interface
                    eq.OwnsOne(e => e.EquipmentTypeData, etd =>
                    {
                        etd.Property(e => e.Code).HasMaxLength(10);
                        etd.Property(e => e.EnglishText).HasMaxLength(100);
                        etd.Property(e => e.FrenchText).HasMaxLength(100);
                        etd.Property(e => e.Text).HasMaxLength(100);
                    });
                });
            });

            // Configure Furnace
            modelBuilder.Entity<Furnace>(entity =>
            {
                entity.ToTable("Furnaces", "hvac");
                entity.HasKey(e => e.Id);

                // Configure Type1EquipmentInformation as owned entity (no explicit type constraint)
                entity.OwnsOne(e => e.Type1EquipmentInformation, ei =>
                {
                    ei.Property(e => e.Manufacturer).HasMaxLength(200);
                    ei.Property(e => e.Model).HasMaxLength(200);
                    ei.Property(e => e.Description).HasMaxLength(500);
                    ei.Property(e => e.EnergyStar);
                    ei.Property(e => e.AHRI);
                    ei.Property(e => e.EpaCsa);
                });

                entity.OwnsOne(e => e.Specifications, spec =>
                {
                    spec.Property(s => s.SizingFactor).HasPrecision(18, 2);
                    spec.Property(s => s.Efficiency).HasPrecision(18, 2);
                    spec.Property(s => s.IsSteadyState);
                    spec.Property(s => s.PilotLight).HasPrecision(18, 2);
                    spec.Property(s => s.FlueDiameter).HasPrecision(18, 2);

                    spec.OwnsOne(s => s.OutputCapacity, oc =>
                    {
                        oc.Property(o => o.Code).HasMaxLength(10);
                        oc.Property(o => o.EnglishText).HasMaxLength(100);
                        oc.Property(o => o.FrenchText).HasMaxLength(100);
                        oc.Property(o => o.Text).HasMaxLength(100);
                        oc.Property(o => o.Value).HasPrecision(18, 2);
                        oc.Property(o => o.UiUnits).HasMaxLength(20);
                    });
                });

                entity.OwnsOne(e => e.ComboTankAndPump, ctp =>
                {
                    ctp.Property(c => c.WaterTemperature).HasPrecision(18, 2);

                    // Configure TankCapacity as a complex type with Value property
                    ctp.OwnsOne(c => c.TankCapacity, tc =>
                    {
                        tc.Property(t => t.Code).HasMaxLength(10);
                        tc.Property(t => t.English).HasMaxLength(100);
                        tc.Property(t => t.French).HasMaxLength(100);
                        tc.Property(t => t.IsUserSpecified);
                        tc.Property(t => t.Value).HasPrecision(18, 2);
                    });

                    // Ignore the computed property
                    ctp.Ignore(c => c.TankCapacityXml);

                    // Configure nested owned entities
                    ctp.OwnsOne(c => c.TankLocation, tl =>
                    {
                        tl.Property(t => t.Code).HasMaxLength(10);
                        tl.Property(t => t.EnglishText).HasMaxLength(100);
                        tl.Property(t => t.FrenchText).HasMaxLength(100);
                        tl.Property(t => t.Text).HasMaxLength(100);
                    });

                    ctp.OwnsOne(c => c.EnergyFactor, ef =>
                    {
                        ef.Property(e => e.Value).HasPrecision(18, 2);
                    });

                    ctp.OwnsOne(c => c.CirculationPump, cp =>
                    {
                        cp.Property(c => c.IsCalculated);
                        cp.Property(c => c.Value).HasPrecision(18, 2);
                        cp.Property(c => c.HasEnergyEfficientMotor);
                    });
                });

                // FIXED: Configure FurnaceEquipment properly (inherits from CommonEquipment)
                entity.OwnsOne(e => e.Equipment, eq =>
                {
                    // CommonEquipment properties
                    eq.Property(e => e.IsBiEnergy);
                    eq.Property(e => e.SwitchoverTemperature).HasPrecision(18, 2);

                    // Ignore the interface property - Entity Framework cannot handle interfaces
                    eq.Ignore(e => e.EquipmentType);

                    eq.OwnsOne(e => e.EnergySource, es =>
                    {
                        es.Property(e => e.Code).HasMaxLength(10);
                        es.Property(e => e.English).HasMaxLength(50);
                        es.Property(e => e.French).HasMaxLength(50);
                        es.Property(e => e.IsUserSpecified);
                    });

                    // Configure the actual data property instead of the interface
                    eq.OwnsOne(e => e.EquipmentTypeData, etd =>
                    {
                        etd.Property(e => e.Code).HasMaxLength(10);
                        etd.Property(e => e.EnglishText).HasMaxLength(100);
                        etd.Property(e => e.FrenchText).HasMaxLength(100);
                        etd.Property(e => e.Text).HasMaxLength(100);
                    });
                });
            });

            // Configure P9 
            modelBuilder.Entity<P9>(entity =>
            {
                entity.ToTable("P9Systems", "hvac");
                entity.HasKey(e => e.Id);

                entity.Property(e => e.NumberOfSystems);
                entity.Property(e => e.ThermalPerformanceFactor).HasPrecision(18, 2);
                entity.Property(e => e.AnnualElectricity).HasPrecision(18, 2);
                entity.Property(e => e.SpaceHeatingCapacity).HasPrecision(18, 2);
                entity.Property(e => e.SpaceHeatingEfficiency).HasPrecision(18, 2);
                entity.Property(e => e.WaterHeatingPerformanceFactor).HasPrecision(18, 2);
                entity.Property(e => e.BurnerInput).HasPrecision(18, 2);
                entity.Property(e => e.RecoveryEfficiency).HasPrecision(18, 2);
                entity.Property(e => e.IsUserSpecified);

                // FIXED: Use proper EquipmentInformation base type
                entity.OwnsOne(e => e.EquipmentInformation, eq =>
                {
                    eq.Property(e => e.Manufacturer).HasMaxLength(200);
                    eq.Property(e => e.Model).HasMaxLength(200);
                    eq.Property(e => e.Description).HasMaxLength(500);
                });

                entity.OwnsOne(e => e.TestData, td =>
                {
                    td.Property(t => t.ControlsPower).HasPrecision(18, 2);
                    td.Property(t => t.CirculationPower).HasPrecision(18, 2);
                    td.Property(t => t.DailyUse).HasPrecision(18, 2);
                    td.Property(t => t.StandbyLossWithFan).HasPrecision(18, 2);
                    td.Property(t => t.StandbyLossWithoutFan).HasPrecision(18, 2);
                    td.Property(t => t.OneHourRatingHotWater).HasPrecision(18, 2);
                    td.Property(t => t.OneHourRatingConcurrent).HasPrecision(18, 2);

                    td.OwnsOne(t => t.EnergySource, es =>
                    {
                        es.Property(e => e.Code).HasMaxLength(10);
                        es.Property(e => e.English).HasMaxLength(50);
                        es.Property(e => e.French).HasMaxLength(50);
                        es.Property(e => e.IsUserSpecified);
                    });

                    td.OwnsOne(t => t.NetEfficiency, ne =>
                    {
                        ne.Property(n => n.Load10).HasPrecision(18, 2);
                        ne.Property(n => n.Load40).HasPrecision(18, 2);
                        ne.Property(n => n.Load100).HasPrecision(18, 2);
                    });

                    td.OwnsOne(t => t.ElectricalUse, eu =>
                    {
                        eu.Property(e => e.Load10).HasPrecision(18, 2);
                        eu.Property(e => e.Load40).HasPrecision(18, 2);
                        eu.Property(e => e.Load100).HasPrecision(18, 2);
                    });

                    td.OwnsOne(t => t.BlowerPower, bp =>
                    {
                        bp.Property(b => b.Load10).HasPrecision(18, 2);
                        bp.Property(b => b.Load40).HasPrecision(18, 2);
                        bp.Property(b => b.Load100).HasPrecision(18, 2);
                    });
                });
            });

            // ===============================
            // TYPE2 SYSTEM CONFIGURATIONS
            // ===============================

            modelBuilder.Entity<Type2>(entity =>
            {
                entity.ToTable("Type2s", "hvac");
                entity.HasKey(e => e.Id);
                entity.Property(e => e.ShadingInF280Cooling).HasMaxLength(50);

    // One-to-one with AirConditioning
                entity.HasOne(e => e.AirConditioning)
                    .WithOne(ac => ac.Type2)
                    .HasForeignKey<AirConditioning>(ac => ac.Type2Id)
                    .OnDelete(DeleteBehavior.Cascade);

    // NOTE: Heat pump relationships are now configured from the heat pump side to avoid duplicate foreign keys
            });

            // Configure AirHeatPump
            modelBuilder.Entity<AirHeatPump>(entity =>
            {
                entity.ToTable("AirHeatPumps", "hvac");
                entity.HasKey(e => e.Id);
                entity.Property(e => e.Type2Id);

                // Configure relationship to Type2 (avoid duplicate foreign key)
                entity.HasOne(e => e.Type2)
                    .WithOne(t => t.AirHeatPump)
                    .HasForeignKey<AirHeatPump>(e => e.Type2Id)
                    .OnDelete(DeleteBehavior.Cascade);

                // FIXED: Use proper HeatPumpEquipmentInformation type
                entity.OwnsOne(e => e.EquipmentInformation, ei =>
                {
                    ei.Property(e => e.Manufacturer).HasMaxLength(200);
                    ei.Property(e => e.Model).HasMaxLength(200);
                    ei.Property(e => e.Description).HasMaxLength(500);
                    ei.Property(e => e.EnergyStar);
                    ei.Property(e => e.AHRI);
                    ei.Property(e => e.CanCsaC448);
                });

    // Rest of the HeatPump configuration...
                entity.OwnsOne(e => e.Equipment, eq =>
                {
                    eq.Property(e => e.CrankcaseHeater).HasPrecision(18, 2);
                    eq.Property(e => e.NumberOfHeads);

                    // CRITICAL: Ignore the base Type property since HeatPump uses Function instead
                    eq.Ignore(e => e.Type);

                    eq.OwnsOne(e => e.Function, func =>
                    {
                        func.Property(f => f.Code).HasMaxLength(10);
                        func.Property(f => f.English).HasMaxLength(50);
                        func.Property(f => f.French).HasMaxLength(50);
                        func.Property(f => f.IsUserSpecified);
                    });
                });
                entity.OwnsOne(e => e.Specifications, spec =>
                {
                    spec.OwnsOne(s => s.OutputCapacity, oc =>
                    {
                        oc.Property(o => o.Code).HasMaxLength(10);
                        oc.Property(o => o.EnglishText).HasMaxLength(100);
                        oc.Property(o => o.FrenchText).HasMaxLength(100);
                        oc.Property(o => o.Text).HasMaxLength(100);
                        oc.Property(o => o.Value).HasPrecision(18, 2);
                        oc.Property(o => o.UiUnits).HasMaxLength(20);
                    });

                    spec.OwnsOne(s => s.HeatingEfficiency, he =>
                    {
                        he.Property(h => h.IsCop);
                        he.Property(h => h.Unit);
                        he.Property(h => h.Value).HasPrecision(18, 2);
                    });

                    spec.OwnsOne(s => s.CoolingEfficiency, ce =>
                    {
                        ce.Property(c => c.IsCop);
                        ce.Property(c => c.Unit);
                        ce.Property(c => c.Value).HasPrecision(18, 2);
                    });
                });

                entity.OwnsOne(e => e.Temperature, temp =>
                {
                    temp.OwnsOne(t => t.CutoffType, ct =>
                    {
                        ct.Property(c => c.Code).HasMaxLength(10);
                        ct.Property(c => c.English).HasMaxLength(50);
                        ct.Property(c => c.French).HasMaxLength(50);
                        ct.Property(c => c.IsUserSpecified);
                        ct.Property(c => c.Value).HasPrecision(18, 2);
                    });

                    temp.OwnsOne(t => t.RatingType, rt =>
                    {
                        rt.Property(r => r.Code).HasMaxLength(10);
                        rt.Property(r => r.English).HasMaxLength(50);
                        rt.Property(r => r.French).HasMaxLength(50);
                        rt.Property(r => r.IsUserSpecified);
                        rt.Property(r => r.Value).HasPrecision(18, 2);
                    });
                });

                entity.OwnsOne(e => e.SourceTemperature, st =>
                {
                    st.Property(s => s.Depth).HasPrecision(18, 2);

                    st.OwnsOne(s => s.Use, use =>
                    {
                        use.Property(u => u.Code).HasMaxLength(10);
                        use.Property(u => u.English).HasMaxLength(50);
                        use.Property(u => u.French).HasMaxLength(50);
                        use.Property(u => u.IsUserSpecified);
                    });

                    st.OwnsOne(s => s.Temperatures, t =>
                    {
                        t.Property(t => t.January).HasPrecision(18, 2);
                        t.Property(t => t.February).HasPrecision(18, 2);
                        t.Property(t => t.March).HasPrecision(18, 2);
                        t.Property(t => t.April).HasPrecision(18, 2);
                        t.Property(t => t.May).HasPrecision(18, 2);
                        t.Property(t => t.June).HasPrecision(18, 2);
                        t.Property(t => t.July).HasPrecision(18, 2);
                        t.Property(t => t.August).HasPrecision(18, 2);
                        t.Property(t => t.September).HasPrecision(18, 2);
                        t.Property(t => t.October).HasPrecision(18, 2);
                        t.Property(t => t.November).HasPrecision(18, 2);
                        t.Property(t => t.December).HasPrecision(18, 2);
                    });
                });

                entity.OwnsOne(e => e.CoolingParameters, cp =>
                {
                    cp.Property(c => c.SensibleHeatRatio).HasPrecision(18, 2);
                    cp.Property(c => c.OpenableWindowArea).HasPrecision(18, 2);

                    cp.OwnsOne(c => c.FansAndPump, fp =>
                    {
                        fp.Property(f => f.FlowRate).HasPrecision(18, 2);
                        fp.Property(f => f.HasEnergyEfficientMotor);

                        fp.OwnsOne(f => f.Mode, mode =>
                        {
                            mode.Property(m => m.Code).HasMaxLength(10);
                            mode.Property(m => m.English).HasMaxLength(50);
                            mode.Property(m => m.French).HasMaxLength(50);
                            mode.Property(m => m.IsUserSpecified);
                        });

                        fp.OwnsOne(f => f.Power, p =>
                        {
                            p.Property(p => p.IsCalculated);
                            p.Property(p => p.Value).HasPrecision(18, 2);
                        });
                    });
                });

                entity.OwnsOne(e => e.ColdClimateHeatPump, cchp =>
                {
                    cchp.Property(c => c.HeatingEfficiency).HasPrecision(18, 2);
                    cchp.Property(c => c.CoolingEfficiency).HasPrecision(18, 2);
                    cchp.Property(c => c.HeatingEfficiencyUnit).HasPrecision(18, 2);
                    cchp.Property(c => c.CoolingEfficiencyUnit).HasPrecision(18, 2);
                    cchp.Property(c => c.Capacity).HasPrecision(18, 2);
                    cchp.Property(c => c.Cop).HasPrecision(18, 2);
                    cchp.Property(c => c.CapacityMaintenance).HasPrecision(18, 2);
                    cchp.Property(c => c.UiUnits).HasMaxLength(20);
                });
            });

            // Configure WaterHeatPump (same configuration as AirHeatPump)
            modelBuilder.Entity<WaterHeatPump>(entity =>
            {
                entity.ToTable("WaterHeatPumps", "hvac");
                entity.HasKey(e => e.Id);
                entity.Property(e => e.Type2Id);

                // Configure relationship to Type2 (avoid duplicate foreign key)
                entity.HasOne(e => e.Type2)
                    .WithOne(t => t.WaterHeatPump)
                    .HasForeignKey<WaterHeatPump>(e => e.Type2Id)
                    .OnDelete(DeleteBehavior.NoAction);

                // FIXED: Use proper HeatPumpEquipmentInformation type
                entity.OwnsOne(e => e.EquipmentInformation, ei =>
                {
                    ei.Property(e => e.Manufacturer).HasMaxLength(200);
                    ei.Property(e => e.Model).HasMaxLength(200);
                    ei.Property(e => e.Description).HasMaxLength(500);
                    ei.Property(e => e.EnergyStar);
                    ei.Property(e => e.AHRI);
                    ei.Property(e => e.CanCsaC448);
                });

                // Rest of the HeatPump configuration...
                entity.OwnsOne(e => e.Equipment, eq =>
                {
                    eq.Property(e => e.CrankcaseHeater).HasPrecision(18, 2);
                    eq.Property(e => e.NumberOfHeads);

                    // CRITICAL: Ignore the base Type property since HeatPump uses Function instead
                    eq.Ignore(e => e.Type);

                    eq.OwnsOne(e => e.Function, func =>
                    {
                        func.Property(f => f.Code).HasMaxLength(10);
                        func.Property(f => f.English).HasMaxLength(50);
                        func.Property(f => f.French).HasMaxLength(50);
                        func.Property(f => f.IsUserSpecified);
                    });
                });

                entity.OwnsOne(e => e.Specifications, spec =>
                {
                    spec.OwnsOne(s => s.OutputCapacity, oc =>
                    {
                        oc.Property(o => o.Code).HasMaxLength(10);
                        oc.Property(o => o.EnglishText).HasMaxLength(100);
                        oc.Property(o => o.FrenchText).HasMaxLength(100);
                        oc.Property(o => o.Text).HasMaxLength(100);
                        oc.Property(o => o.Value).HasPrecision(18, 2);
                        oc.Property(o => o.UiUnits).HasMaxLength(20);
                    });

                    spec.OwnsOne(s => s.HeatingEfficiency, he =>
                    {
                        he.Property(h => h.IsCop);
                        he.Property(h => h.Unit);
                        he.Property(h => h.Value).HasPrecision(18, 2);
                    });

                    spec.OwnsOne(s => s.CoolingEfficiency, ce =>
                    {
                        ce.Property(c => c.IsCop);
                        ce.Property(c => c.Unit);
                        ce.Property(c => c.Value).HasPrecision(18, 2);
                    });
                });

                entity.OwnsOne(e => e.Temperature, temp =>
                {
                    temp.OwnsOne(t => t.CutoffType, ct =>
                    {
                        ct.Property(c => c.Code).HasMaxLength(10);
                        ct.Property(c => c.English).HasMaxLength(50);
                        ct.Property(c => c.French).HasMaxLength(50);
                        ct.Property(c => c.IsUserSpecified);
                        ct.Property(c => c.Value).HasPrecision(18, 2);
                    });

                    temp.OwnsOne(t => t.RatingType, rt =>
                    {
                        rt.Property(r => r.Code).HasMaxLength(10);
                        rt.Property(r => r.English).HasMaxLength(50);
                        rt.Property(r => r.French).HasMaxLength(50);
                        rt.Property(r => r.IsUserSpecified);
                        rt.Property(r => r.Value).HasPrecision(18, 2);
                    });
                });

                entity.OwnsOne(e => e.SourceTemperature, st =>
                {
                    st.Property(s => s.Depth).HasPrecision(18, 2);

                    st.OwnsOne(s => s.Use, use =>
                    {
                        use.Property(u => u.Code).HasMaxLength(10);
                        use.Property(u => u.English).HasMaxLength(50);
                        use.Property(u => u.French).HasMaxLength(50);
                        use.Property(u => u.IsUserSpecified);
                    });

                    st.OwnsOne(s => s.Temperatures, t =>
                    {
                        t.Property(t => t.January).HasPrecision(18, 2);
                        t.Property(t => t.February).HasPrecision(18, 2);
                        t.Property(t => t.March).HasPrecision(18, 2);
                        t.Property(t => t.April).HasPrecision(18, 2);
                        t.Property(t => t.May).HasPrecision(18, 2);
                        t.Property(t => t.June).HasPrecision(18, 2);
                        t.Property(t => t.July).HasPrecision(18, 2);
                        t.Property(t => t.August).HasPrecision(18, 2);
                        t.Property(t => t.September).HasPrecision(18, 2);
                        t.Property(t => t.October).HasPrecision(18, 2);
                        t.Property(t => t.November).HasPrecision(18, 2);
                        t.Property(t => t.December).HasPrecision(18, 2);
                    });
                });

                entity.OwnsOne(e => e.CoolingParameters, cp =>
                {
                    cp.Property(c => c.SensibleHeatRatio).HasPrecision(18, 2);
                    cp.Property(c => c.OpenableWindowArea).HasPrecision(18, 2);

                    cp.OwnsOne(c => c.FansAndPump, fp =>
                    {
                        fp.Property(f => f.FlowRate).HasPrecision(18, 2);
                        fp.Property(f => f.HasEnergyEfficientMotor);

                        fp.OwnsOne(f => f.Mode, mode =>
                        {
                            mode.Property(m => m.Code).HasMaxLength(10);
                            mode.Property(m => m.English).HasMaxLength(50);
                            mode.Property(m => m.French).HasMaxLength(50);
                            mode.Property(m => m.IsUserSpecified);
                        });

                        fp.OwnsOne(f => f.Power, p =>
                        {
                            p.Property(p => p.IsCalculated);
                            p.Property(p => p.Value).HasPrecision(18, 2);
                        });
                    });
                });

                entity.OwnsOne(e => e.ColdClimateHeatPump, cchp =>
                {
                    cchp.Property(c => c.HeatingEfficiency).HasPrecision(18, 2);
                    cchp.Property(c => c.CoolingEfficiency).HasPrecision(18, 2);
                    cchp.Property(c => c.HeatingEfficiencyUnit).HasPrecision(18, 2);
                    cchp.Property(c => c.CoolingEfficiencyUnit).HasPrecision(18, 2);
                    cchp.Property(c => c.Capacity).HasPrecision(18, 2);
                    cchp.Property(c => c.Cop).HasPrecision(18, 2);
                    cchp.Property(c => c.CapacityMaintenance).HasPrecision(18, 2);
                    cchp.Property(c => c.UiUnits).HasMaxLength(20);
                });
            });

            // Configure GroundHeatPump (same configuration as AirHeatPump)
            modelBuilder.Entity<GroundHeatPump>(entity =>
            {
                entity.ToTable("GroundHeatPumps", "hvac");
                entity.HasKey(e => e.Id);
                entity.Property(e => e.Type2Id);

                // Configure relationship to Type2 (avoid duplicate foreign key)
                entity.HasOne(e => e.Type2)
                    .WithOne(t => t.GroundHeatPump)
                    .HasForeignKey<GroundHeatPump>(e => e.Type2Id)
                    .OnDelete(DeleteBehavior.NoAction);

                // FIXED: Use proper HeatPumpEquipmentInformation type
                entity.OwnsOne(e => e.EquipmentInformation, ei =>
                {
                    ei.Property(e => e.Manufacturer).HasMaxLength(200);
                    ei.Property(e => e.Model).HasMaxLength(200);
                    ei.Property(e => e.Description).HasMaxLength(500);
                    ei.Property(e => e.EnergyStar);
                    ei.Property(e => e.AHRI);
                    ei.Property(e => e.CanCsaC448);
                });

                // Rest of the HeatPump configuration...
                entity.OwnsOne(e => e.Equipment, eq =>
                {
                    eq.Property(e => e.CrankcaseHeater).HasPrecision(18, 2);
                    eq.Property(e => e.NumberOfHeads);

                    // CRITICAL: Ignore the base Type property since HeatPump uses Function instead
                    eq.Ignore(e => e.Type);

                    eq.OwnsOne(e => e.Function, func =>
                    {
                        func.Property(f => f.Code).HasMaxLength(10);
                        func.Property(f => f.English).HasMaxLength(50);
                        func.Property(f => f.French).HasMaxLength(50);
                        func.Property(f => f.IsUserSpecified);
                    });
                });

                entity.OwnsOne(e => e.Specifications, spec =>
                {
                    spec.OwnsOne(s => s.OutputCapacity, oc =>
                    {
                        oc.Property(o => o.Code).HasMaxLength(10);
                        oc.Property(o => o.EnglishText).HasMaxLength(100);
                        oc.Property(o => o.FrenchText).HasMaxLength(100);
                        oc.Property(o => o.Text).HasMaxLength(100);
                        oc.Property(o => o.Value).HasPrecision(18, 2);
                        oc.Property(o => o.UiUnits).HasMaxLength(20);
                    });

                    spec.OwnsOne(s => s.HeatingEfficiency, he =>
                    {
                        he.Property(h => h.IsCop);
                        he.Property(h => h.Unit);
                        he.Property(h => h.Value).HasPrecision(18, 2);
                    });

                    spec.OwnsOne(s => s.CoolingEfficiency, ce =>
                    {
                        ce.Property(c => c.IsCop);
                        ce.Property(c => c.Unit);
                        ce.Property(c => c.Value).HasPrecision(18, 2);
                    });
                });

                entity.OwnsOne(e => e.Temperature, temp =>
                {
                    temp.OwnsOne(t => t.CutoffType, ct =>
                    {
                        ct.Property(c => c.Code).HasMaxLength(10);
                        ct.Property(c => c.English).HasMaxLength(50);
                        ct.Property(c => c.French).HasMaxLength(50);
                        ct.Property(c => c.IsUserSpecified);
                        ct.Property(c => c.Value).HasPrecision(18, 2);
                    });

                    temp.OwnsOne(t => t.RatingType, rt =>
                    {
                        rt.Property(r => r.Code).HasMaxLength(10);
                        rt.Property(r => r.English).HasMaxLength(50);
                        rt.Property(r => r.French).HasMaxLength(50);
                        rt.Property(r => r.IsUserSpecified);
                        rt.Property(r => r.Value).HasPrecision(18, 2);
                    });
                });

                entity.OwnsOne(e => e.SourceTemperature, st =>
                {
                    st.Property(s => s.Depth).HasPrecision(18, 2);

                    st.OwnsOne(s => s.Use, use =>
                    {
                        use.Property(u => u.Code).HasMaxLength(10);
                        use.Property(u => u.English).HasMaxLength(50);
                        use.Property(u => u.French).HasMaxLength(50);
                        use.Property(u => u.IsUserSpecified);
                    });

                    st.OwnsOne(s => s.Temperatures, t =>
                    {
                        t.Property(t => t.January).HasPrecision(18, 2);
                        t.Property(t => t.February).HasPrecision(18, 2);
                        t.Property(t => t.March).HasPrecision(18, 2);
                        t.Property(t => t.April).HasPrecision(18, 2);
                        t.Property(t => t.May).HasPrecision(18, 2);
                        t.Property(t => t.June).HasPrecision(18, 2);
                        t.Property(t => t.July).HasPrecision(18, 2);
                        t.Property(t => t.August).HasPrecision(18, 2);
                        t.Property(t => t.September).HasPrecision(18, 2);
                        t.Property(t => t.October).HasPrecision(18, 2);
                        t.Property(t => t.November).HasPrecision(18, 2);
                        t.Property(t => t.December).HasPrecision(18, 2);
                    });
                });

                entity.OwnsOne(e => e.CoolingParameters, cp =>
                {
                    cp.Property(c => c.SensibleHeatRatio).HasPrecision(18, 2);
                    cp.Property(c => c.OpenableWindowArea).HasPrecision(18, 2);

                    cp.OwnsOne(c => c.FansAndPump, fp =>
                    {
                        fp.Property(f => f.FlowRate).HasPrecision(18, 2);
                        fp.Property(f => f.HasEnergyEfficientMotor);

                        fp.OwnsOne(f => f.Mode, mode =>
                        {
                            mode.Property(m => m.Code).HasMaxLength(10);
                            mode.Property(m => m.English).HasMaxLength(50);
                            mode.Property(m => m.French).HasMaxLength(50);
                            mode.Property(m => m.IsUserSpecified);
                        });

                        fp.OwnsOne(f => f.Power, p =>
                        {
                            p.Property(p => p.IsCalculated);
                            p.Property(p => p.Value).HasPrecision(18, 2);
                        });
                    });
                });

                entity.OwnsOne(e => e.ColdClimateHeatPump, cchp =>
                {
                    cchp.Property(c => c.HeatingEfficiency).HasPrecision(18, 2);
                    cchp.Property(c => c.CoolingEfficiency).HasPrecision(18, 2);
                    cchp.Property(c => c.HeatingEfficiencyUnit).HasPrecision(18, 2);
                    cchp.Property(c => c.CoolingEfficiencyUnit).HasPrecision(18, 2);
                    cchp.Property(c => c.Capacity).HasPrecision(18, 2);
                    cchp.Property(c => c.Cop).HasPrecision(18, 2);
                    cchp.Property(c => c.CapacityMaintenance).HasPrecision(18, 2);
                    cchp.Property(c => c.UiUnits).HasMaxLength(20);
                });
            });

            // Configure AirConditioning
            modelBuilder.Entity<AirConditioning>(entity =>
            {
                entity.ToTable("AirConditionings", "hvac");
                entity.HasKey(e => e.Id);

                // FIXED: Use EnergyStarEquipmentInformation without explicit type constraint
                entity.OwnsOne(e => e.EquipmentInformation, ei =>
                {
                    // Base EquipmentInformation properties
                    ei.Property(e => e.Manufacturer).HasMaxLength(200);
                    ei.Property(e => e.Model).HasMaxLength(200);
                    ei.Property(e => e.Description).HasMaxLength(500);

                    // EnergyStarEquipmentInformation specific properties
                    ei.Property(e => e.EnergyStar);
                    ei.Property(e => e.AHRI);
                });

                entity.OwnsOne(e => e.Equipment, eq =>
                {
                    eq.Property(e => e.CrankcaseHeater).HasPrecision(18, 2);
                    eq.Property(e => e.NumberOfHeads);

                    // CRITICAL: Ignore the base Type property to avoid ownership conflict with CentralType
                    eq.Ignore(e => e.Type);

                    eq.OwnsOne(e => e.CentralType, ct =>
                    {
                        ct.Property(c => c.Code).HasMaxLength(10);
                        ct.Property(c => c.English).HasMaxLength(100);
                        ct.Property(c => c.French).HasMaxLength(100);
                        ct.Property(c => c.IsUserSpecified);
                    });

                    eq.OwnsOne(e => e.WindowUnits, wu =>
                    {
                        wu.Property(w => w.TotalCount);
                        wu.Property(w => w.NumberOfEnergyStarUnits);
                    });
                });

                entity.OwnsOne(e => e.Specifications, spec =>
                {
                    spec.Property(s => s.SizingFactor).HasPrecision(18, 2);

                    spec.OwnsOne(s => s.RatedCapacity, rc =>
                    {
                        rc.Property(r => r.Code).HasMaxLength(10);
                        rc.Property(r => r.EnglishText).HasMaxLength(100);
                        rc.Property(r => r.FrenchText).HasMaxLength(100);
                        rc.Property(r => r.Text).HasMaxLength(100);
                        rc.Property(r => r.Value).HasPrecision(18, 2);
                        rc.Property(r => r.UiUnits).HasMaxLength(20);
                    });

                    spec.OwnsOne(s => s.Efficiency, eff =>
                    {
                        eff.Property(e => e.IsCop);
                        eff.Property(e => e.Unit);
                        eff.Property(e => e.Value).HasPrecision(18, 2);
                    });
                });

                entity.OwnsOne(e => e.CoolingParameters, cp =>
                {
                    cp.Property(c => c.SensibleHeatRatio).HasPrecision(18, 2);
                    cp.Property(c => c.OpenableWindowArea).HasPrecision(18, 2);

                    cp.OwnsOne(c => c.FansAndPump, fp =>
                    {
                        fp.Property(f => f.FlowRate).HasPrecision(18, 2);
                        fp.Property(f => f.HasEnergyEfficientMotor);

                        fp.OwnsOne(f => f.Mode, mode =>
                        {
                            mode.Property(m => m.Code).HasMaxLength(10);
                            mode.Property(m => m.English).HasMaxLength(50);
                            mode.Property(m => m.French).HasMaxLength(50);
                            mode.Property(m => m.IsUserSpecified);
                        });

                        fp.OwnsOne(f => f.Power, p =>
                        {
                            p.Property(p => p.IsCalculated);
                            p.Property(p => p.Value).HasPrecision(18, 2);
                        });
                    });
                });
            });

            // CodeAndText is used only as owned entities, not as a regular entity
            // Remove the regular entity configuration to avoid conflicts
            // ===============================
            // MULTIPLE SYSTEMS CONFIGURATION
            // ===============================

            modelBuilder.Entity<MultipleSystems>(entity =>
            {
                entity.ToTable("MultipleSystems", "hvac");
                entity.HasKey(e => e.Id);

                entity.HasMany(e => e.EquipmentInformation)
                      .WithOne(eq => eq.MultipleSystems)
                      .HasForeignKey(eq => eq.MultipleSystemsId)
                      .OnDelete(DeleteBehavior.Cascade);

                entity.OwnsOne(e => e.Summary, summary =>
                {
                    summary.Property(s => s.EnergySaverHeatingSystems);
                    summary.Property(s => s.EnergySaverAirSourceHeatPump);
                    summary.Property(s => s.WoodAppliances);
                    summary.Property(s => s.EpaCsaHeatingSystems);
                });
            });

            // Configure MultipleSystemsEquipment
            modelBuilder.Entity<MultipleSystemsEquipment>(entity =>
            {
                entity.ToTable("MultipleSystemsEquipments", "hvac");
                entity.HasKey(e => e.Id);

                entity.Property(e => e.Rank);
                entity.Property(e => e.Efficiency).HasPrecision(18, 2);
                entity.Property(e => e.FlueDiameter).HasPrecision(18, 2);
                entity.Property(e => e.HeatingCapacitykW).HasPrecision(18, 2);
                entity.Property(e => e.HeatingCapacityUiUnits).HasMaxLength(20);
                entity.Property(e => e.HeatingCapacityValueInUiUnits).HasPrecision(18, 2);
                entity.Property(e => e.PilotLight);
                entity.Property(e => e.EnergyStar);
                entity.Property(e => e.EnergyEfficientMotor);
                entity.Property(e => e.IdenticalSystems);
                entity.Property(e => e.Manufacturer).HasMaxLength(200);
                entity.Property(e => e.Model).HasMaxLength(200);

                // Ignore the interface property - Entity Framework cannot handle interfaces
                entity.Ignore(e => e.EquipmentType);

                entity.OwnsOne(e => e.EnergySource, es =>
                {
                    es.Property(e => e.Code).HasMaxLength(10);
                    es.Property(e => e.English).HasMaxLength(50);
                    es.Property(e => e.French).HasMaxLength(50);
                    es.Property(e => e.IsUserSpecified);
                });

                entity.OwnsOne(e => e.EfficiencyType, et =>
                {
                    et.Property(e => e.Code).HasMaxLength(10);
                    et.Property(e => e.English).HasMaxLength(50);
                    et.Property(e => e.French).HasMaxLength(50);
                    et.Property(e => e.IsUserSpecified);
                });

                // Configure the actual data property instead of the interface
                entity.OwnsOne(e => e.EquipmentTypeData, etd =>
                {
                    etd.Property(e => e.Code).HasMaxLength(10);
                    etd.Property(e => e.EnglishText).HasMaxLength(100);
                    etd.Property(e => e.FrenchText).HasMaxLength(100);
                    etd.Property(e => e.Text).HasMaxLength(100);
                });
            });

            // ===============================
            // RADIANT HEATING CONFIGURATION
            // ===============================

            modelBuilder.Entity<RadiantHeating>(entity =>
            {
                entity.ToTable("RadiantHeatings", "hvac");
                entity.HasKey(e => e.Id);

                entity.OwnsOne(e => e.AtticCeiling, component =>
                {
                    component.Property(c => c.EffectiveTemperature).HasPrecision(18, 2);
                    component.Property(c => c.FractionOfArea).HasPrecision(18, 2);
                });

                entity.OwnsOne(e => e.FlatRoof, component =>
                {
                    component.Property(c => c.EffectiveTemperature).HasPrecision(18, 2);
                    component.Property(c => c.FractionOfArea).HasPrecision(18, 2);
                });

                entity.OwnsOne(e => e.AboveCrawlspace, component =>
                {
                    component.Property(c => c.EffectiveTemperature).HasPrecision(18, 2);
                    component.Property(c => c.FractionOfArea).HasPrecision(18, 2);
                });

                entity.OwnsOne(e => e.SlabOnGrade, component =>
                {
                    component.Property(c => c.EffectiveTemperature).HasPrecision(18, 2);
                    component.Property(c => c.FractionOfArea).HasPrecision(18, 2);
                });

                entity.OwnsOne(e => e.AboveBasement, component =>
                {
                    component.Property(c => c.EffectiveTemperature).HasPrecision(18, 2);
                    component.Property(c => c.FractionOfArea).HasPrecision(18, 2);
                });

                entity.OwnsOne(e => e.Basement, component =>
                {
                    component.Property(c => c.EffectiveTemperature).HasPrecision(18, 2);
                    component.Property(c => c.FractionOfArea).HasPrecision(18, 2);
                });
            });


            // ===============================
            // ADDITIONAL OPENING CONFIGURATION
            // ===============================

            // Configure AdditionalOpening entity (standalone entity with CodeAndText properties)
            // Explicitly configure as a standalone entity to avoid inheritance detection
            modelBuilder.Entity<AdditionalOpening>(entity =>
    {
        entity.ToTable("AdditionalOpenings", "hvac");
        entity.HasKey(e => e.Id);

        // Explicitly configure this as a non-derived entity
        entity.HasBaseType((Type?)null);

        // Configure CodeAndText properties
        entity.Property(e => e.Code)
            .IsRequired()
            .HasMaxLength(10);

        entity.Property(e => e.EnglishText)
            .HasMaxLength(100);

        entity.Property(e => e.FrenchText)
            .HasMaxLength(100);

        entity.Property(e => e.Text)
            .HasMaxLength(100);

        // Configure AdditionalOpening-specific properties
        entity.Property(e => e.FlueDiameter)
              .HasPrecision(18, 2);

        entity.Property(e => e.Rank);

        entity.Property(e => e.DamperClosed);

        entity.Property(e => e.NumberOfOpenings);

        // Configure relationship
        entity.HasOne(e => e.HeatingCooling)
              .WithMany(hc => hc.AdditionalOpenings)
              .HasForeignKey(e => e.HeatingCoolingId)
              .OnDelete(DeleteBehavior.Cascade);
    });

            // ===============================
            // SUPPLEMENTARY HEAT CONFIGURATION
            // ===============================

            modelBuilder.Entity<SupplementaryHeat>(entity =>
            {
                entity.ToTable("SupplementaryHeats", "hvac");
                entity.HasKey(e => e.Id);

                entity.Property(e => e.Rank);

                // FIXED: Use proper SupplementaryHeatEquipmentInformation type
                entity.OwnsOne(e => e.EquipmentInformation, ei =>
                {
                    ei.Property(e => e.Manufacturer).HasMaxLength(200);
                    ei.Property(e => e.Model).HasMaxLength(200);
                    ei.Property(e => e.Description).HasMaxLength(500);
                    ei.Property(e => e.CsaEpa);
                });

                entity.OwnsOne(e => e.Equipment, eq =>
                {
                    // Ignore the interface property - Entity Framework cannot handle interfaces
                    eq.Ignore(e => e.Type);

                    eq.OwnsOne(e => e.EnergySource, es =>
                    {
                        es.Property(e => e.Code).HasMaxLength(10);
                        es.Property(e => e.English).HasMaxLength(50);
                        es.Property(e => e.French).HasMaxLength(50);
                        es.Property(e => e.IsUserSpecified);
                    });

                    eq.OwnsOne(e => e.TypeData, td =>
                    {
                        td.Property(t => t.Code).HasMaxLength(10);
                        td.Property(t => t.EnglishText).HasMaxLength(200);
                        td.Property(t => t.FrenchText).HasMaxLength(200);
                        td.Property(t => t.Text).HasMaxLength(200);
                    });
                });

                entity.OwnsOne(e => e.Specifications, spec =>
                {
                    spec.Property(s => s.Efficiency).HasPrecision(18, 2);
                    spec.Property(s => s.PilotLight).HasPrecision(18, 2);
                    spec.Property(s => s.DamperClosed);
                    spec.Property(s => s.YearMade);

                    spec.OwnsOne(s => s.Usage, usage =>
                    {
                        usage.Property(u => u.Code).HasMaxLength(10).IsRequired().HasDefaultValue("1");
                        usage.Property(u => u.English).HasMaxLength(50).IsRequired().HasDefaultValue("Never");
                        usage.Property(u => u.French).HasMaxLength(50).IsRequired().HasDefaultValue("Jamais");
                        usage.Property(u => u.IsUserSpecified).HasDefaultValue(false);
                    });

                    spec.OwnsOne(s => s.MonthlyUsage, mu =>
                    {
                        mu.Property(m => m.January).HasPrecision(18, 2);
                        mu.Property(m => m.February).HasPrecision(18, 2);
                        mu.Property(m => m.March).HasPrecision(18, 2);
                        mu.Property(m => m.April).HasPrecision(18, 2);
                        mu.Property(m => m.May).HasPrecision(18, 2);
                        mu.Property(m => m.June).HasPrecision(18, 2);
                        mu.Property(m => m.July).HasPrecision(18, 2);
                        mu.Property(m => m.August).HasPrecision(18, 2);
                        mu.Property(m => m.September).HasPrecision(18, 2);
                        mu.Property(m => m.October).HasPrecision(18, 2);
                        mu.Property(m => m.November).HasPrecision(18, 2);
                        mu.Property(m => m.December).HasPrecision(18, 2);
                    });

                    spec.OwnsOne(s => s.LocationHeated, loc =>
                    {
                        loc.Property(l => l.Code).HasMaxLength(10).IsRequired().HasDefaultValue("1");
                        loc.Property(l => l.English).HasMaxLength(50).IsRequired().HasDefaultValue("Main floors");
                        loc.Property(l => l.French).HasMaxLength(50).IsRequired().HasDefaultValue("Étages principaux");
                        loc.Property(l => l.IsUserSpecified).HasDefaultValue(false);
                        loc.Property(l => l.Value).HasPrecision(18, 2).HasDefaultValue(0.0m);
                    });

                    spec.OwnsOne(s => s.Flue, flue =>
                    {
                        flue.Property(f => f.IsInterior);
                        flue.Property(f => f.Diameter).HasPrecision(18, 2);
                        flue.Property(f => f.Area).HasPrecision(18, 2);

                        flue.OwnsOne(f => f.Type, ft =>
                        {
                            ft.Property(t => t.Code).HasMaxLength(10).IsRequired().HasDefaultValue("1");
                            ft.Property(t => t.English).HasMaxLength(50).IsRequired().HasDefaultValue("Brick");
                            ft.Property(t => t.French).HasMaxLength(50).IsRequired().HasDefaultValue("Brique");
                            ft.Property(t => t.IsUserSpecified).HasDefaultValue(false);
                        });
                    });

                    spec.OwnsOne(s => s.OutputCapacity, oc =>
                    {
                        oc.Property(o => o.Code).HasMaxLength(10).IsRequired().HasDefaultValue("1");
                        oc.Property(o => o.EnglishText).HasMaxLength(100).IsRequired().HasDefaultValue("User specified");
                        oc.Property(o => o.FrenchText).HasMaxLength(100).IsRequired().HasDefaultValue("Spécifié par l'utilisateur");
                        oc.Property(o => o.Text).HasMaxLength(100).IsRequired().HasDefaultValue("User specified");
                        oc.Property(o => o.Value).HasPrecision(18, 2).HasDefaultValue(0.0m);
                        oc.Property(o => o.UiUnits).HasMaxLength(20).IsRequired().HasDefaultValue("kW");
                    });
                });
            });

            // ===============================
            // PERFORMANCE INDEXES
            // ===============================

            modelBuilder.Entity<HeatingCooling>()
                .HasIndex(e => e.HouseId)
                .HasDatabaseName("IX_HeatingCooling_HouseId");

            modelBuilder.Entity<AdditionalOpening>()
                .HasIndex(e => new { e.HeatingCoolingId, e.Rank })
                .HasDatabaseName("IX_AdditionalOpening_HeatingCoolingId_Rank");

            modelBuilder.Entity<SupplementaryHeat>()
                .HasIndex(e => new { e.HeatingCoolingId, e.Rank })
                .HasDatabaseName("IX_SupplementaryHeat_HeatingCoolingId_Rank");

            modelBuilder.Entity<MultipleSystemsEquipment>()
                .HasIndex(e => new { e.MultipleSystemsId, e.Rank })
                .HasDatabaseName("IX_MultipleSystemsEquipment_MultipleSystemsId_Rank");
        }


    }
}