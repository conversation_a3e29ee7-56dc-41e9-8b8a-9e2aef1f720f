using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using AirInfiltrationService.Core.Interfaces;
using AirInfiltrationService.Core.Models;
using AirInfiltrationService.Infrastructure.Data;

namespace AirInfiltrationService.Infrastructure.Repositories
{
    public class AirInfiltrationRepository : IAirInfiltrationRepository
    {
        private readonly AirInfiltrationDbContext _context;

        public AirInfiltrationRepository(AirInfiltrationDbContext context)
        {
            _context = context;
        }

        public async Task<IEnumerable<NaturalAirInfiltration>> GetAllAirInfiltrationsAsync()
        {
            return await _context.NaturalAirInfiltrations
                .Include(n => n.Specifications)
                .Include(n => n.OtherFactors)
                .Include(n => n.AirLeakageTestData)
                    .ThenInclude(a => a.TestData)
                        .ThenInclude(t => t.Data)
                .ToListAsync();
        }

        public async Task<NaturalAirInfiltration?> GetAirInfiltrationByHouseIdAsync(Guid houseId)
        {
            return await _context.NaturalAirInfiltrations
                .Include(n => n.Specifications)
                .Include(n => n.OtherFactors)
                .Include(n => n.AirLeakageTestData)
                    .ThenInclude(a => a.TestData)
                        .ThenInclude(t => t.Data)
                .FirstOrDefaultAsync(n => n.HouseId == houseId);
        }

        public async Task<NaturalAirInfiltration?> GetAirInfiltrationByIdAsync(Guid id)
        {
            return await _context.NaturalAirInfiltrations
                .AsNoTracking()
                .Include(n => n.Specifications)
                .Include(n => n.OtherFactors)
                .Include(n => n.AirLeakageTestData)
                    .ThenInclude(a => a.TestData)
                        .ThenInclude(t => t.Data)
                .FirstOrDefaultAsync(n => n.Id == id);
        }

        public async Task<NaturalAirInfiltration> CreateAirInfiltrationAsync(NaturalAirInfiltration airInfiltration)
        {
            _context.NaturalAirInfiltrations.Add(airInfiltration);
            await _context.SaveChangesAsync();
            return airInfiltration;
        }

        public async Task UpdateAirInfiltrationAsync(NaturalAirInfiltration airInfiltration)
        {
            _context.Entry(airInfiltration).State = EntityState.Modified;
            await _context.SaveChangesAsync();
        }

        public async Task DeleteAirInfiltrationAsync(Guid id)
        {
            var airInfiltration = await _context.NaturalAirInfiltrations.FindAsync(id);
            if (airInfiltration != null)
            {
                _context.NaturalAirInfiltrations.Remove(airInfiltration);
                await _context.SaveChangesAsync();
            }
        }

        public async Task<NaturalAirInfiltration?> GetAirInfiltrationByEnergyUpgradeIdAsync(Guid energyUpgradeId)
        {
            return await _context.NaturalAirInfiltrations
                .Include(n => n.Specifications)
                .Include(n => n.OtherFactors)
                .Include(n => n.AirLeakageTestData)
                    .ThenInclude(a => a.TestData)
                        .ThenInclude(t => t.Data)
                .FirstOrDefaultAsync(n => n.EnergyUpgradeId == energyUpgradeId);
        }

        public async Task<NaturalAirInfiltration> DuplicateAirInfiltrationForEnergyUpgradeAsync(NaturalAirInfiltration baseAirInfiltration, Guid energyUpgradeId)
        {
            // Create a deep copy of the air infiltration
            var duplicatedAirInfiltration = new NaturalAirInfiltration
            {
                Id = Guid.NewGuid(),
                HouseId = baseAirInfiltration.HouseId,
                EnergyUpgradeId = energyUpgradeId,
                Label = baseAirInfiltration.Label,
                Specifications = new NaturalAirSpecifications
                {
                    Id = Guid.NewGuid(),
                    House = baseAirInfiltration.Specifications.House,
                    BlowerTest = baseAirInfiltration.Specifications.BlowerTest,
                    BuildingSite = baseAirInfiltration.Specifications.BuildingSite,
                    LocalShielding = baseAirInfiltration.Specifications.LocalShielding,
                    ExhaustDevicesTest = baseAirInfiltration.Specifications.ExhaustDevicesTest,
                    CommonSurfaceArea = baseAirInfiltration.Specifications.CommonSurfaceArea
                },
                OtherFactors = new OtherFactors
                {
                    Id = Guid.NewGuid(),
                    WeatherStation = new WeatherStation
                    {
                        AnemometerHeight = baseAirInfiltration.OtherFactors.WeatherStation.AnemometerHeight,
                        Terrain = baseAirInfiltration.OtherFactors.WeatherStation.Terrain
                    },
                    LeakageFractions = new LeakageFractions
                    {
                        UseDefaults = baseAirInfiltration.OtherFactors.LeakageFractions.UseDefaults,
                        Ceilings = baseAirInfiltration.OtherFactors.LeakageFractions.Ceilings,
                        Walls = baseAirInfiltration.OtherFactors.LeakageFractions.Walls,
                        Floors = baseAirInfiltration.OtherFactors.LeakageFractions.Floors
                    }
                },
                AirLeakageTestData = baseAirInfiltration.AirLeakageTestData != null ?
                    new AirLeakageTestData
                    {
                        Id = Guid.NewGuid(),
                        HasCgsbConditions = baseAirInfiltration.AirLeakageTestData.HasCgsbConditions,
                        OutsideTemperature = baseAirInfiltration.AirLeakageTestData.OutsideTemperature,
                        BarometricPressure = baseAirInfiltration.AirLeakageTestData.BarometricPressure,
                        TestType = baseAirInfiltration.AirLeakageTestData.TestType,
                        TestData = baseAirInfiltration.AirLeakageTestData.TestData?.Select(test => new Test
                        {
                            Id = Guid.NewGuid(),
                            Rank = test.Rank,
                            Equipment = test.Equipment,
                            InsideTemperature = test.InsideTemperature,
                            ZoneHeatedVolume = test.ZoneHeatedVolume,
                            Manometer = test.Manometer,
                            Pressure = test.Pressure,
                            FanType = test.FanType,
                            Data = test.Data?.Select(dataPoint => new DataPoint
                            {
                                Id = Guid.NewGuid(),
                                Rank = dataPoint.Rank,
                                HousePressure = dataPoint.HousePressure,
                                FanPressure = dataPoint.FanPressure,
                                MeasuredFlow = dataPoint.MeasuredFlow,
                                Zone1Pressure = dataPoint.Zone1Pressure,
                                Zone2Pressure = dataPoint.Zone2Pressure,
                                FlowRanges = dataPoint.FlowRanges
                            }).ToList() ?? new List<DataPoint>()
                        }).ToList() ?? new List<Test>()
                    } : null
            };

            // Set the foreign key for the specifications
            duplicatedAirInfiltration.Specifications.NaturalAirInfiltrationId = duplicatedAirInfiltration.Id;
            duplicatedAirInfiltration.OtherFactors.NaturalAirInfiltrationId = duplicatedAirInfiltration.Id;
            if (duplicatedAirInfiltration.AirLeakageTestData != null)
            {
                duplicatedAirInfiltration.AirLeakageTestData.NaturalAirInfiltrationId = duplicatedAirInfiltration.Id;

                // Set foreign keys for Test objects and their DataPoints
                foreach (var test in duplicatedAirInfiltration.AirLeakageTestData.TestData)
                {
                    test.AirLeakageTestDataId = duplicatedAirInfiltration.AirLeakageTestData.Id;
                    foreach (var dataPoint in test.Data)
                    {
                        dataPoint.TestId = test.Id;
                    }
                }
            }

            _context.NaturalAirInfiltrations.Add(duplicatedAirInfiltration);
            await _context.SaveChangesAsync();
            return duplicatedAirInfiltration;
        }
    }
}