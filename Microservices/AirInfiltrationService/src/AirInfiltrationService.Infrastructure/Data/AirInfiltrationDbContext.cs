using Microsoft.EntityFrameworkCore;
using AirInfiltrationService.Core.Models;

namespace AirInfiltrationService.Infrastructure.Data
{
    public class AirInfiltrationDbContext : DbContext
    {
        public AirInfiltrationDbContext(DbContextOptions<AirInfiltrationDbContext> options) : base(options)
        {
        }

        protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder)
        {
            optionsBuilder.ConfigureWarnings(warnings =>
                warnings.Ignore(Microsoft.EntityFrameworkCore.Diagnostics.RelationalEventId.ForeignKeyPropertiesMappedToUnrelatedTables));
        }

        // Main air infiltration entities
        public DbSet<NaturalAirInfiltration> NaturalAirInfiltrations { get; set; } = null!;
        public DbSet<NaturalAirSpecifications> NaturalAirSpecifications { get; set; } = null!;
        public DbSet<OtherFactors> OtherFactors { get; set; } = null!;
        public DbSet<AirLeakageTestData> AirLeakageTestDatas { get; set; } = null!;
        public DbSet<Test> Tests { get; set; } = null!;
        public DbSet<DataPoint> DataPoints { get; set; } = null!;

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            // Set default schema for all entities in this context
            modelBuilder.HasDefaultSchema("airinfiltration");

            // Ignore resource classes - they are not database entities
            modelBuilder.Ignore<AirTightnessTypes>();
            modelBuilder.Ignore<Terrains>();
            modelBuilder.Ignore<BlowerTestPressures>();
            modelBuilder.Ignore<AirLeakageTestTypes>();
            modelBuilder.Ignore<LocalShieldings>();
            modelBuilder.Ignore<TestStatuses>();
            modelBuilder.Ignore<ResourceList>();
            modelBuilder.Ignore<ResourceValueList>();

            // Ignore base classes that are used for composition, not inheritance
            modelBuilder.Ignore<CodeAndText>();
            modelBuilder.Ignore<CodeTextAndValue>();

            base.OnModelCreating(modelBuilder);

            // ===============================
            // MAIN NATURAL AIR INFILTRATION CONFIGURATION
            // ===============================

            modelBuilder.Entity<NaturalAirInfiltration>(entity =>
            {
                entity.ToTable("NaturalAirInfiltrations", "airinfiltration");
                entity.HasKey(e => e.Id);
                entity.Property(e => e.Label).HasMaxLength(100);
                entity.Property(e => e.EnergyUpgradeId).IsRequired(false);

                // One-to-one relationship with NaturalAirSpecifications
                entity.HasOne(e => e.Specifications)
                      .WithOne()
                      .HasForeignKey<NaturalAirSpecifications>(s => s.NaturalAirInfiltrationId)
                      .OnDelete(DeleteBehavior.Cascade);

                // One-to-one relationship with OtherFactors
                entity.HasOne(e => e.OtherFactors)
                      .WithOne()
                      .HasForeignKey<OtherFactors>(o => o.NaturalAirInfiltrationId)
                      .OnDelete(DeleteBehavior.Cascade);

                // Optional one-to-one relationship with AirLeakageTestData
                entity.HasOne(e => e.AirLeakageTestData)
                      .WithOne()
                      .HasForeignKey<AirLeakageTestData>(a => a.NaturalAirInfiltrationId)
                      .OnDelete(DeleteBehavior.Cascade);
            });

            // ===============================
            // NATURAL AIR SPECIFICATIONS CONFIGURATION
            // ===============================

            modelBuilder.Entity<NaturalAirSpecifications>(entity =>
            {
                entity.ToTable("NaturalAirSpecifications", "airinfiltration");
                entity.HasKey(e => e.Id);

                // Configure owned entities for specifications components
                entity.OwnsOne(e => e.House, house =>
                {
                    house.Property(h => h.Volume).HasPrecision(18, 2);
                    house.Property(h => h.IncludeCrawlspaceVolume);

                    house.OwnsOne(h => h.AirTightnessTest, at =>
                    {
                        at.Property(a => a.Code).HasMaxLength(10);
                        at.Property(a => a.English).HasMaxLength(100);
                        at.Property(a => a.French).HasMaxLength(100);
                        at.Property(a => a.IsUserSpecified);
                        at.Property(a => a.Value).HasPrecision(18, 2);
                    });
                });

                entity.OwnsOne(e => e.BlowerTest, bt =>
                {
                    bt.Property(b => b.AirLeakageTestData);
                    bt.Property(b => b.AirChangeRate).HasPrecision(18, 2);
                    bt.Property(b => b.IsCgsbTest);
                    bt.Property(b => b.IsCalculated);
                    bt.Property(b => b.LeakageArea).HasPrecision(18, 2);
                    bt.Property(b => b.Guarded);

                    bt.OwnsOne(b => b.Pressure, p =>
                    {
                        p.Property(pr => pr.Code).HasMaxLength(10);
                        p.Property(pr => pr.English).HasMaxLength(50);
                        p.Property(pr => pr.French).HasMaxLength(50);
                        p.Property(pr => pr.IsUserSpecified);
                    });
                });

                entity.OwnsOne(e => e.BuildingSite, bs =>
                {
                    bs.Property(b => b.HighestCeiling).HasPrecision(18, 2);

                    bs.OwnsOne(b => b.Terrain, t =>
                    {
                        t.Property(tr => tr.Code).HasMaxLength(10);
                        t.Property(tr => tr.English).HasMaxLength(100);
                        t.Property(tr => tr.French).HasMaxLength(100);
                        t.Property(tr => tr.IsUserSpecified);
                    });
                });

                entity.OwnsOne(e => e.LocalShielding, ls =>
                {
                    ls.OwnsOne(l => l.Walls, w =>
                    {
                        w.Property(wa => wa.Code).HasMaxLength(10);
                        w.Property(wa => wa.English).HasMaxLength(100);
                        w.Property(wa => wa.French).HasMaxLength(100);
                        w.Property(wa => wa.IsUserSpecified);
                    });

                    ls.OwnsOne(l => l.Flue, f =>
                    {
                        f.Property(fl => fl.Code).HasMaxLength(10);
                        f.Property(fl => fl.English).HasMaxLength(100);
                        f.Property(fl => fl.French).HasMaxLength(100);
                        f.Property(fl => fl.IsUserSpecified);
                    });
                });

                entity.OwnsOne(e => e.ExhaustDevicesTest, edt =>
                {
                    edt.Property(e => e.Result).HasPrecision(18, 2);

                    edt.OwnsOne(e => e.TestStatus, ts =>
                    {
                        ts.Property(t => t.Code).HasMaxLength(10);
                        ts.Property(t => t.English).HasMaxLength(100);
                        ts.Property(t => t.French).HasMaxLength(100);
                        ts.Property(t => t.IsUserSpecified);
                    });
                });

                entity.OwnsOne(e => e.CommonSurfaceArea, csa =>
                {
                    csa.Property(c => c.SurfaceArea).HasPrecision(18, 2);
                });
            });

            // ===============================
            // OTHER FACTORS CONFIGURATION
            // ===============================

            modelBuilder.Entity<OtherFactors>(entity =>
            {
                entity.ToTable("OtherFactors", "airinfiltration");
                entity.HasKey(e => e.Id);

                // Configure owned entities for other factors components
                entity.OwnsOne(e => e.WeatherStation, ws =>
                {
                    ws.Property(w => w.AnemometerHeight).HasPrecision(18, 2);

                    ws.OwnsOne(w => w.Terrain, t =>
                    {
                        t.Property(tr => tr.Code).HasMaxLength(10);
                        t.Property(tr => tr.English).HasMaxLength(100);
                        t.Property(tr => tr.French).HasMaxLength(100);
                        t.Property(tr => tr.IsUserSpecified);
                    });
                });

                entity.OwnsOne(e => e.LeakageFractions, lf =>
                {
                    lf.Property(l => l.UseDefaults);
                    lf.Property(l => l.Ceilings).HasPrecision(18, 2);
                    lf.Property(l => l.Walls).HasPrecision(18, 2);
                    lf.Property(l => l.Floors).HasPrecision(18, 2);
                });
            });

            // ===============================
            // AIR LEAKAGE TEST DATA CONFIGURATION
            // ===============================

            modelBuilder.Entity<AirLeakageTestData>(entity =>
            {
                entity.ToTable("AirLeakageTestDatas", "airinfiltration");
                entity.HasKey(e => e.Id);
                entity.Property(e => e.HasCgsbConditions);
                entity.Property(e => e.OutsideTemperature).HasPrecision(18, 2);
                entity.Property(e => e.BarometricPressure).HasPrecision(18, 2);

                entity.OwnsOne(e => e.TestType, tt =>
                {
                    tt.Property(t => t.Code).HasMaxLength(10);
                    tt.Property(t => t.English).HasMaxLength(100);
                    tt.Property(t => t.French).HasMaxLength(100);
                    tt.Property(t => t.IsUserSpecified);
                });

                // One-to-many relationship with Test
                entity.HasMany(e => e.TestData)
                      .WithOne()
                      .HasForeignKey(t => t.AirLeakageTestDataId)
                      .OnDelete(DeleteBehavior.Cascade);
            });

            // ===============================
            // TEST CONFIGURATION
            // ===============================

            modelBuilder.Entity<Test>(entity =>
            {
                entity.ToTable("Tests", "airinfiltration");
                entity.HasKey(e => e.Id);
                entity.Property(e => e.Rank);
                entity.Property(e => e.Equipment);
                entity.Property(e => e.InsideTemperature).HasPrecision(18, 2);
                entity.Property(e => e.ZoneHeatedVolume).HasPrecision(18, 2);
                entity.Property(e => e.Manometer).HasMaxLength(100);

                entity.OwnsOne(e => e.Pressure, p =>
                {
                    p.Property(pr => pr.Value).HasPrecision(18, 2);
                });

                entity.OwnsOne(e => e.FanType, ft =>
                {
                    ft.Property(f => f.Code).HasMaxLength(10);
                    ft.Property(f => f.English).HasMaxLength(100);
                    ft.Property(f => f.French).HasMaxLength(100);
                });

                // One-to-many relationship with DataPoint
                entity.HasMany(e => e.Data)
                      .WithOne()
                      .HasForeignKey(d => d.TestId)
                      .OnDelete(DeleteBehavior.Cascade);
            });

            // ===============================
            // DATA POINT CONFIGURATION
            // ===============================

            modelBuilder.Entity<DataPoint>(entity =>
            {
                entity.ToTable("DataPoints", "airinfiltration");
                entity.HasKey(e => e.Id);
                entity.Property(e => e.Rank);
                entity.Property(e => e.HousePressure).HasPrecision(18, 2);
                entity.Property(e => e.FanPressure).HasPrecision(18, 2);
                entity.Property(e => e.MeasuredFlow).HasPrecision(18, 2);
                entity.Property(e => e.Zone1Pressure).HasPrecision(18, 2);
                entity.Property(e => e.Zone2Pressure).HasPrecision(18, 2);

                entity.OwnsOne(e => e.FlowRanges, fr =>
                {
                    fr.Property(f => f.Code).HasMaxLength(10);
                    fr.Property(f => f.English).HasMaxLength(100);
                    fr.Property(f => f.French).HasMaxLength(100);
                });
            });
        }
    }
}