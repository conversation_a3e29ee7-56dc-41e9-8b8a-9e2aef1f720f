using System;
using System.ComponentModel.DataAnnotations;

namespace AirInfiltrationService.Core.Models
{
    /// <summary>
    /// Represents the natural air infiltration information for a house
    /// Matches the structure of the original NaturalAirInfiltration.cs
    /// </summary>
    public class NaturalAirInfiltration
    {
        public Guid Id { get; set; }

        public Guid HouseId { get; set; }

        public Guid? EnergyUpgradeId { get; set; }

        public string Label { get; set; } = "NaturalAirInfiltration";

        // Navigation properties matching original structure exactly
        public NaturalAirSpecifications Specifications { get; set; } = new NaturalAirSpecifications();

        public OtherFactors OtherFactors { get; set; } = new OtherFactors();

        public AirLeakageTestData? AirLeakageTestData { get; set; }

        public NaturalAirInfiltration()
        {
            Label = "NaturalAirInfiltration";
        }

        public void SetDefaults()
        {
            Specifications.BlowerTest.AirLeakageTestData = AirLeakageTestData != null;
        }
    }
}