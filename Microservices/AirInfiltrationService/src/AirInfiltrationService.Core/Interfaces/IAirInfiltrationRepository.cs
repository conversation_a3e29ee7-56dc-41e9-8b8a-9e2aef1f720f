using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using AirInfiltrationService.Core.Models;

namespace AirInfiltrationService.Core.Interfaces
{
    public interface IAirInfiltrationRepository
    {
        Task<IEnumerable<NaturalAirInfiltration>> GetAllAirInfiltrationsAsync();
        Task<NaturalAirInfiltration?> GetAirInfiltrationByHouseIdAsync(Guid houseId);
        Task<NaturalAirInfiltration?> GetAirInfiltrationByIdAsync(Guid id);
        Task<NaturalAirInfiltration> CreateAirInfiltrationAsync(NaturalAirInfiltration airInfiltration);
        Task UpdateAirInfiltrationAsync(NaturalAirInfiltration airInfiltration);
        Task DeleteAirInfiltrationAsync(Guid id);
        Task<NaturalAirInfiltration?> GetAirInfiltrationByEnergyUpgradeIdAsync(Guid energyUpgradeId);
        Task<NaturalAirInfiltration> DuplicateAirInfiltrationForEnergyUpgradeAsync(NaturalAirInfiltration baseAirInfiltration, Guid energyUpgradeId);
    }
}