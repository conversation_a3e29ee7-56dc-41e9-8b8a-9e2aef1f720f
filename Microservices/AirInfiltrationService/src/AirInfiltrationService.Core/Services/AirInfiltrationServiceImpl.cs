using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using AirInfiltrationService.Core.Interfaces;
using AirInfiltrationService.Core.Models;

namespace AirInfiltrationService.Core.Services
{
    public class AirInfiltrationServiceImpl : IAirInfiltrationService
    {
        private readonly IAirInfiltrationRepository _repository;

        public AirInfiltrationServiceImpl(IAirInfiltrationRepository repository)
        {
            _repository = repository;
        }

        public async Task<IEnumerable<NaturalAirInfiltration>> GetAllAirInfiltrationsAsync()
        {
            return await _repository.GetAllAirInfiltrationsAsync();
        }

        public async Task<NaturalAirInfiltration?> GetAirInfiltrationByHouseIdAsync(Guid houseId)
        {
            return await _repository.GetAirInfiltrationByHouseIdAsync(houseId);
        }

        public async Task<NaturalAirInfiltration?> GetAirInfiltrationByIdAsync(Guid id)
        {
            return await _repository.GetAirInfiltrationByIdAsync(id);
        }

        public async Task<NaturalAirInfiltration> CreateAirInfiltrationAsync(NaturalAirInfiltration airInfiltration)
        {
            // TODO: Add validation and business logic
            return await _repository.CreateAirInfiltrationAsync(airInfiltration);
        }

        public async Task UpdateAirInfiltrationAsync(NaturalAirInfiltration airInfiltration)
        {
            // TODO: Add validation and business logic
            await _repository.UpdateAirInfiltrationAsync(airInfiltration);
        }

        public async Task DeleteAirInfiltrationAsync(Guid id)
        {
            await _repository.DeleteAirInfiltrationAsync(id);
        }

        public async Task<NaturalAirInfiltration?> GetAirInfiltrationByEnergyUpgradeIdAsync(Guid energyUpgradeId)
        {
            return await _repository.GetAirInfiltrationByEnergyUpgradeIdAsync(energyUpgradeId);
        }

        public async Task<NaturalAirInfiltration> DuplicateAirInfiltrationForEnergyUpgradeAsync(NaturalAirInfiltration baseAirInfiltration, Guid energyUpgradeId)
        {
            // TODO: Add validation and business logic
            return await _repository.DuplicateAirInfiltrationForEnergyUpgradeAsync(baseAirInfiltration, energyUpgradeId);
        }
    }
}