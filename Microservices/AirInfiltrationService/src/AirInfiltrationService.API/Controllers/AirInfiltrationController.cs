using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using AutoMapper;
using AirInfiltrationService.API.Models;
using AirInfiltrationService.Core.Interfaces;
using AirInfiltrationService.Core.Models;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;

namespace AirInfiltrationService.API.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class AirInfiltrationController : ControllerBase
    {
        private readonly IAirInfiltrationService _airInfiltrationService;
        private readonly IMapper _mapper;
        private readonly ILogger<AirInfiltrationController> _logger;

        public AirInfiltrationController(
            IAirInfiltrationService airInfiltrationService,
            IMapper mapper,
            ILogger<AirInfiltrationController> logger)
        {
            _airInfiltrationService = airInfiltrationService;
            _mapper = mapper;
            _logger = logger;
        }

        /// <summary>
        /// Gets all air infiltration systems
        /// </summary>
        [HttpGet]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<ActionResult<IEnumerable<NaturalAirInfiltrationDto>>> GetAllAirInfiltrations()
        {
            _logger.LogInformation("Getting all air infiltration systems");

            var airInfiltrations = await _airInfiltrationService.GetAllAirInfiltrationsAsync();

            if (airInfiltrations == null)
            {
                _logger.LogWarning("No air infiltration systems found");
                return NotFound("No air infiltration systems found");
            }

            var airInfiltrationDtos = _mapper.Map<IEnumerable<NaturalAirInfiltrationDto>>(airInfiltrations);
            return Ok(airInfiltrationDtos);
        }

        /// <summary>
        /// Gets air infiltration system for a specific house
        /// </summary>
        [HttpGet("house/{houseId}")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<ActionResult<NaturalAirInfiltrationDto>> GetAirInfiltrationByHouseId(Guid houseId)
        {
            _logger.LogInformation("Getting air infiltration system for house ID: {HouseId}", houseId);

            var airInfiltration = await _airInfiltrationService.GetAirInfiltrationByHouseIdAsync(houseId);

            if (airInfiltration == null)
            {
                _logger.LogWarning("Air infiltration system not found for house ID: {HouseId}", houseId);
                return NotFound($"Air infiltration system not found for house ID: {houseId}");
            }

            var airInfiltrationDto = _mapper.Map<NaturalAirInfiltrationDto>(airInfiltration);
            return Ok(airInfiltrationDto);
        }

        /// <summary>
        /// Gets a specific air infiltration system by ID
        /// </summary>
        [HttpGet("{id}")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<ActionResult<NaturalAirInfiltrationDto>> GetAirInfiltrationById(Guid id)
        {
            _logger.LogInformation("Getting air infiltration system with ID: {Id}", id);

            var airInfiltration = await _airInfiltrationService.GetAirInfiltrationByIdAsync(id);

            if (airInfiltration == null)
            {
                _logger.LogWarning("Air infiltration system not found with ID: {Id}", id);
                return NotFound($"Air infiltration system not found with ID: {id}");
            }

            var airInfiltrationDto = _mapper.Map<NaturalAirInfiltrationDto>(airInfiltration);
            return Ok(airInfiltrationDto);
        }

        /// <summary>
        /// Creates a new air infiltration system
        /// </summary>
        [HttpPost]
        [ProducesResponseType(StatusCodes.Status201Created)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        public async Task<ActionResult<NaturalAirInfiltrationDto>> CreateAirInfiltration([FromBody] NaturalAirInfiltrationDto airInfiltrationDto)
        {
            if (airInfiltrationDto == null)
            {
                _logger.LogWarning("Air infiltration system data is null");
                return BadRequest("Air infiltration system data is required");
            }

            _logger.LogInformation("Creating new air infiltration system for house ID: {HouseId}", airInfiltrationDto.HouseId);

            var airInfiltration = _mapper.Map<NaturalAirInfiltration>(airInfiltrationDto);

            // Populate resource type text from resource classes
            PopulateResourceTypeText(airInfiltration);

            var createdAirInfiltration = await _airInfiltrationService.CreateAirInfiltrationAsync(airInfiltration);
            var createdAirInfiltrationDto = _mapper.Map<NaturalAirInfiltrationDto>(createdAirInfiltration);

            return CreatedAtAction(
                nameof(GetAirInfiltrationById),
                new { id = createdAirInfiltrationDto.Id },
                createdAirInfiltrationDto);
        }

        /// <summary>
        /// Updates an existing air infiltration system
        /// </summary>
        [HttpPut("{id}")]
        [ProducesResponseType(StatusCodes.Status204NoContent)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<IActionResult> UpdateAirInfiltration(Guid id, [FromBody] NaturalAirInfiltrationDto airInfiltrationDto)
        {
            if (airInfiltrationDto == null)
            {
                _logger.LogWarning("Air infiltration system data is null");
                return BadRequest("Air infiltration system data is required");
            }

            if (id != airInfiltrationDto.Id)
            {
                _logger.LogWarning("ID mismatch: {Id} != {DtoId}", id, airInfiltrationDto.Id);
                return BadRequest("ID mismatch");
            }

            _logger.LogInformation("Updating air infiltration system with ID: {Id}", id);

            var airInfiltration = _mapper.Map<NaturalAirInfiltration>(airInfiltrationDto);

            // Populate resource type text from resource classes
            PopulateResourceTypeText(airInfiltration);

            try
            {
                await _airInfiltrationService.UpdateAirInfiltrationAsync(airInfiltration);
            }
            catch (InvalidOperationException ex) when (ex.Message.Contains("not found"))
            {
                _logger.LogWarning("Air infiltration system not found with ID: {Id}", id);
                return NotFound($"Air infiltration system not found with ID: {id}");
            }

            return NoContent();
        }

        /// <summary>
        /// Deletes an air infiltration system
        /// </summary>
        [HttpDelete("{id}")]
        [ProducesResponseType(StatusCodes.Status204NoContent)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<IActionResult> DeleteAirInfiltration(Guid id)
        {
            var existingAirInfiltration = await _airInfiltrationService.GetAirInfiltrationByIdAsync(id);
            if (existingAirInfiltration == null)
            {
                _logger.LogWarning("Air infiltration system not found with ID: {Id}", id);
                return NotFound($"Air infiltration system not found with ID: {id}");
            }

            _logger.LogInformation("Deleting air infiltration system with ID: {Id}", id);
            await _airInfiltrationService.DeleteAirInfiltrationAsync(id);

            return NoContent();
        }

        /// <summary>
        /// Populates resource type text from resource classes based on codes
        /// </summary>
        private void PopulateResourceTypeText(NaturalAirInfiltration airInfiltration)
        {
            // Populate Specifications resource texts
            if (airInfiltration.Specifications != null)
            {
                // Air Tightness Test
                if (airInfiltration.Specifications.House?.AirTightnessTest != null)
                {
                    PopulateAirTightnessTypeText(airInfiltration.Specifications.House.AirTightnessTest);
                }

                // Blower Test Pressure
                if (airInfiltration.Specifications.BlowerTest?.Pressure != null)
                {
                    PopulateBlowerTestPressureText(airInfiltration.Specifications.BlowerTest.Pressure);
                }

                // Building Site Terrain
                if (airInfiltration.Specifications.BuildingSite?.Terrain != null)
                {
                    PopulateTerrainText(airInfiltration.Specifications.BuildingSite.Terrain);
                }

                // Local Shielding - Walls
                if (airInfiltration.Specifications.LocalShielding?.Walls != null)
                {
                    PopulateLocalShieldingText(airInfiltration.Specifications.LocalShielding.Walls);
                }

                // Local Shielding - Flue
                if (airInfiltration.Specifications.LocalShielding?.Flue != null)
                {
                    PopulateLocalShieldingText(airInfiltration.Specifications.LocalShielding.Flue);
                }

                // Exhaust Devices Test Status
                if (airInfiltration.Specifications.ExhaustDevicesTest?.TestStatus != null)
                {
                    PopulateTestStatusText(airInfiltration.Specifications.ExhaustDevicesTest.TestStatus);
                }
            }

            // Populate Other Factors resource texts
            if (airInfiltration.OtherFactors?.WeatherStation?.Terrain != null)
            {
                PopulateTerrainText(airInfiltration.OtherFactors.WeatherStation.Terrain);
            }

            // Populate Air Leakage Test Data resource texts
            if (airInfiltration.AirLeakageTestData != null)
            {
                // Test Type
                if (airInfiltration.AirLeakageTestData.TestType != null)
                {
                    PopulateAirLeakageTestTypeText(airInfiltration.AirLeakageTestData.TestType);
                }

                // Test Data
                if (airInfiltration.AirLeakageTestData.TestData != null)
                {
                    foreach (var test in airInfiltration.AirLeakageTestData.TestData)
                    {
                        // Fan Type and Flow Ranges use generic CodeAndText
                        // These would need a lookup service for proper text population
                        // For now, they preserve existing values
                    }
                }
            }
        }

        private void PopulateAirTightnessTypeText(AirTightnessTypes airTightnessType)
        {
            var resourceItem = AirTightnessTypes.All.FirstOrDefault(x => x.Code == airTightnessType.Code);
            if (resourceItem != null)
            {
                airTightnessType.English = resourceItem.English;
                airTightnessType.French = resourceItem.French;
                airTightnessType.Value = resourceItem.Value;
            }
        }

        private void PopulateBlowerTestPressureText(BlowerTestPressures pressure)
        {
            var resourceItem = BlowerTestPressures.All.FirstOrDefault(x => x.Code == pressure.Code);
            if (resourceItem != null)
            {
                pressure.English = resourceItem.English;
                pressure.French = resourceItem.French;
            }
        }

        private void PopulateTerrainText(Terrains terrain)
        {
            var resourceItem = Terrains.All.FirstOrDefault(x => x.Code == terrain.Code);
            if (resourceItem != null)
            {
                terrain.English = resourceItem.English;
                terrain.French = resourceItem.French;
            }
        }

        private void PopulateLocalShieldingText(LocalShieldings localShielding)
        {
            var resourceItem = LocalShieldings.All.FirstOrDefault(x => x.Code == localShielding.Code);
            if (resourceItem != null)
            {
                localShielding.English = resourceItem.English;
                localShielding.French = resourceItem.French;
            }
        }

        private void PopulateTestStatusText(TestStatuses testStatus)
        {
            var resourceItem = TestStatuses.All.FirstOrDefault(x => x.Code == testStatus.Code);
            if (resourceItem != null)
            {
                testStatus.English = resourceItem.English;
                testStatus.French = resourceItem.French;
            }
        }

        private void PopulateAirLeakageTestTypeText(AirLeakageTestTypes testType)
        {
            var resourceItem = AirLeakageTestTypes.All.FirstOrDefault(x => x.Code == testType.Code);
            if (resourceItem != null)
            {
                testType.English = resourceItem.English;
                testType.French = resourceItem.French;
            }
        }

        /// <summary>
        /// Gets air infiltration system for a specific energy upgrade
        /// </summary>
        [HttpGet("energy-upgrades/{energyUpgradeId}/air-infiltrations")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<ActionResult<NaturalAirInfiltrationDto>> GetAirInfiltrationByEnergyUpgradeId(Guid energyUpgradeId)
        {
            _logger.LogInformation("Getting air infiltration system for energy upgrade ID: {EnergyUpgradeId}", energyUpgradeId);

            var airInfiltration = await _airInfiltrationService.GetAirInfiltrationByEnergyUpgradeIdAsync(energyUpgradeId);

            if (airInfiltration == null)
            {
                _logger.LogWarning("Air infiltration system not found for energy upgrade ID: {EnergyUpgradeId}", energyUpgradeId);
                return NotFound($"Air infiltration system not found for energy upgrade ID: {energyUpgradeId}");
            }

            var airInfiltrationDto = _mapper.Map<NaturalAirInfiltrationDto>(airInfiltration);
            return Ok(airInfiltrationDto);
        }

        /// <summary>
        /// Creates a copy of an existing air infiltration system for energy upgrade purposes
        /// Only requires energyUpgradeId - automatically copies all values from the base air infiltration
        /// </summary>
        [HttpPost("air-infiltrations/{baseAirInfiltrationId}/duplicate-for-energy-upgrade")]
        [ProducesResponseType(StatusCodes.Status201Created)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        public async Task<ActionResult<NaturalAirInfiltrationDto>> DuplicateAirInfiltrationForEnergyUpgrade(
            Guid baseAirInfiltrationId,
            [FromQuery] Guid energyUpgradeId)
        {
            try
            {
                _logger.LogInformation("Duplicating air infiltration {BaseAirInfiltrationId} for energy upgrade {EnergyUpgradeId}",
                    baseAirInfiltrationId, energyUpgradeId);

                // Get the base air infiltration
                var baseAirInfiltration = await _airInfiltrationService.GetAirInfiltrationByIdAsync(baseAirInfiltrationId);
                if (baseAirInfiltration == null)
                {
                    _logger.LogWarning("Base air infiltration with ID: {BaseAirInfiltrationId} not found", baseAirInfiltrationId);
                    return NotFound($"Base air infiltration with ID {baseAirInfiltrationId} not found");
                }

                // Create a duplicate with new ID but same values
                var duplicatedAirInfiltration = await _airInfiltrationService.DuplicateAirInfiltrationForEnergyUpgradeAsync(baseAirInfiltration, energyUpgradeId);

                var duplicatedAirInfiltrationDto = _mapper.Map<NaturalAirInfiltrationDto>(duplicatedAirInfiltration);

                _logger.LogInformation("Successfully duplicated air infiltration {BaseAirInfiltrationId} as {NewAirInfiltrationId} for energy upgrade {EnergyUpgradeId}",
                    baseAirInfiltrationId, duplicatedAirInfiltration.Id, energyUpgradeId);

                return CreatedAtAction(nameof(GetAirInfiltrationByHouseId),
                    new { houseId = duplicatedAirInfiltration.HouseId },
                    duplicatedAirInfiltrationDto);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error duplicating air infiltration {BaseAirInfiltrationId} for energy upgrade {EnergyUpgradeId}",
                    baseAirInfiltrationId, energyUpgradeId);
                return StatusCode(StatusCodes.Status500InternalServerError,
                    "An error occurred while duplicating the air infiltration for energy upgrade");
            }
        }

    }
}