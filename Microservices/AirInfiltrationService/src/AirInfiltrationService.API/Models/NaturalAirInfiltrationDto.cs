using System;

namespace AirInfiltrationService.API.Models
{
    /// <summary>
    /// DTO for NaturalAirInfiltration system
    /// </summary>
    public class NaturalAirInfiltrationDto
    {
        public Guid Id { get; set; }
        public Guid HouseId { get; set; }
        public Guid? EnergyUpgradeId { get; set; }
        public string Label { get; set; } = "NaturalAirInfiltration";

        // Navigation properties
        public NaturalAirSpecificationsDto Specifications { get; set; } = new NaturalAirSpecificationsDto();
        public OtherFactorsDto OtherFactors { get; set; } = new OtherFactorsDto();
        public AirLeakageTestDataDto? AirLeakageTestData { get; set; }
    }
}