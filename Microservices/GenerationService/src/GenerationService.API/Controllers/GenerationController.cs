using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using AutoMapper;
using GenerationService.API.Models;
using GenerationService.Core.Interfaces;
using GenerationService.Core.Models;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;

namespace GenerationService.API.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class GenerationController : ControllerBase
    {
        private readonly IGenerationService _generationService;
        private readonly IMapper _mapper;
        private readonly ILogger<GenerationController> _logger;

        public GenerationController(
            IGenerationService generationService,
            IMapper mapper,
            ILogger<GenerationController> logger)
        {
            _generationService = generationService;
            _mapper = mapper;
            _logger = logger;
        }

        /// <summary>
        /// Gets all generation systems
        /// </summary>
        [HttpGet]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<ActionResult<IEnumerable<GenerationDto>>> GetAllGenerations()
        {
            _logger.LogInformation("Getting all generation systems");

            var generations = await _generationService.GetAllGenerationsAsync();

            if (generations == null)
            {
                _logger.LogWarning("No generation systems found");
                return NotFound("No generation systems found");
            }

            var generationDtos = _mapper.Map<IEnumerable<GenerationDto>>(generations);
            return Ok(generationDtos);
        }

        /// <summary>
        /// Gets generation system for a specific house
        /// </summary>
        [HttpGet("house/{houseId}")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<ActionResult<GenerationDto>> GetGenerationByHouseId(Guid houseId)
        {
            _logger.LogInformation("Getting generation system for house ID: {HouseId}", houseId);

            var generation = await _generationService.GetGenerationByHouseIdAsync(houseId);

            if (generation == null)
            {
                _logger.LogWarning("Generation system not found for house ID: {HouseId}", houseId);
                return NotFound($"Generation system not found for house ID: {houseId}");
            }

            var generationDto = _mapper.Map<GenerationDto>(generation);
            return Ok(generationDto);
        }

        /// <summary>
        /// Gets a specific generation system by ID
        /// </summary>
        [HttpGet("{id}")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<ActionResult<GenerationDto>> GetGenerationById(Guid id)
        {
            _logger.LogInformation("Getting generation system with ID: {Id}", id);

            var generation = await _generationService.GetGenerationByIdAsync(id);

            if (generation == null)
            {
                _logger.LogWarning("Generation system not found with ID: {Id}", id);
                return NotFound($"Generation system not found with ID: {id}");
            }

            var generationDto = _mapper.Map<GenerationDto>(generation);
            return Ok(generationDto);
        }

        /// <summary>
        /// Creates a new generation system
        /// </summary>
        [HttpPost]
        [ProducesResponseType(StatusCodes.Status201Created)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        public async Task<ActionResult<GenerationDto>> CreateGeneration([FromBody] GenerationDto generationDto)
        {
            if (generationDto == null)
            {
                _logger.LogWarning("Generation system data is null");
                return BadRequest("Generation system data is required");
            }

            _logger.LogInformation("Creating new generation system for house ID: {HouseId}", generationDto.HouseId);

            var generation = _mapper.Map<Generation>(generationDto);

            // Populate resource text from resource classes
            PopulateResourceText(generation);

            var createdGeneration = await _generationService.CreateGenerationAsync(generation);
            var createdGenerationDto = _mapper.Map<GenerationDto>(createdGeneration);

            return CreatedAtAction(
                nameof(GetGenerationById),
                new { id = createdGenerationDto.Id },
                createdGenerationDto);
        }

        /// <summary>
        /// Updates an existing generation system
        /// </summary>
        [HttpPut("{id}")]
        [ProducesResponseType(StatusCodes.Status204NoContent)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<IActionResult> UpdateGeneration(Guid id, [FromBody] GenerationDto generationDto)
        {
            if (generationDto == null)
            {
                _logger.LogWarning("Generation system data is null");
                return BadRequest("Generation system data is required");
            }

            if (id != generationDto.Id)
            {
                _logger.LogWarning("ID mismatch: {Id} != {DtoId}", id, generationDto.Id);
                return BadRequest("ID mismatch");
            }

            _logger.LogInformation("Updating generation system with ID: {Id}", id);

            var generation = _mapper.Map<Generation>(generationDto);

            // Populate resource text from resource classes
            PopulateResourceText(generation);

            try
            {
                await _generationService.UpdateGenerationAsync(generation);
            }
            catch (InvalidOperationException ex) when (ex.Message.Contains("not found"))
            {
                _logger.LogWarning("Generation system not found with ID: {Id}", id);
                return NotFound($"Generation system not found with ID: {id}");
            }

            return NoContent();
        }

        /// <summary>
        /// Deletes a generation system
        /// </summary>
        [HttpDelete("{id}")]
        [ProducesResponseType(StatusCodes.Status204NoContent)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<IActionResult> DeleteGeneration(Guid id)
        {
            var existingGeneration = await _generationService.GetGenerationByIdAsync(id);
            if (existingGeneration == null)
            {
                _logger.LogWarning("Generation system not found with ID: {Id}", id);
                return NotFound($"Generation system not found with ID: {id}");
            }

            _logger.LogInformation("Deleting generation system with ID: {Id}", id);
            await _generationService.DeleteGenerationAsync(id);

            return NoContent();
        }

        /// <summary>
        /// Populates resource text from resource classes
        /// ENHANCED VERSION with better validation and logging
        /// </summary>
        private void PopulateResourceText(Generation generation)
        {
            _logger.LogInformation("PopulateResourceText called for generation ID: {GenerationId}", generation.Id);

            // Populate photovoltaic module types
            if (generation.PhotovoltaicSystems != null)
            {
                _logger.LogInformation("Processing {Count} photovoltaic systems", generation.PhotovoltaicSystems.Count);

                for (int i = 0; i < generation.PhotovoltaicSystems.Count; i++)
                {
                    var photovoltaic = generation.PhotovoltaicSystems[i];
                    _logger.LogInformation("Processing photovoltaic system {Index}, ID: {Id}", i, photovoltaic.Id);

                    // Ensure Module is initialized
                    photovoltaic.Module ??= new Module();

                    var originalTypeCode = photovoltaic.Module.Type?.Code;
                    _logger.LogInformation("Original Module.Type.Code: '{Code}'", originalTypeCode ?? "NULL");

                    // Enhanced validation logic
                    PhotovoltaicModules moduleType;
                    
                    if (photovoltaic.Module.Type != null && 
                        !string.IsNullOrWhiteSpace(photovoltaic.Module.Type.Code) && 
                        photovoltaic.Module.Type.Code != "string")
                    {
                        // Find the matching PhotovoltaicModules resource by code
                        moduleType = PhotovoltaicModules.All.FirstOrDefault(m => m.Code == photovoltaic.Module.Type.Code);
                        if (moduleType != null)
                        {
                            _logger.LogDebug("Found matching module type for code '{Code}': {English}", originalTypeCode, moduleType.English);
                            photovoltaic.Module.Type = moduleType;
                        }
                        else
                        {
                            _logger.LogWarning("No matching module type found for code '{Code}', using UserSpecified", originalTypeCode);
                            photovoltaic.Module.Type = PhotovoltaicModules.UserSpecified;
                        }
                    }
                    else
                    {
                        _logger.LogDebug("Module.Type is null, empty, or 'string', using UserSpecified");
                        photovoltaic.Module.Type = PhotovoltaicModules.UserSpecified;
                    }

                    // CRITICAL: Triple-check that Type is never null after this point
                    if (photovoltaic.Module.Type == null ||
                        string.IsNullOrWhiteSpace(photovoltaic.Module.Type.Code) ||
                        string.IsNullOrWhiteSpace(photovoltaic.Module.Type.English) ||
                        string.IsNullOrWhiteSpace(photovoltaic.Module.Type.French))
                    {
                        _logger.LogError("CRITICAL: Module.Type is still invalid after processing, forcing UserSpecified");
                        photovoltaic.Module.Type = PhotovoltaicModules.UserSpecified;
                    }

                    _logger.LogInformation("Final Module.Type for photovoltaic {Index}: Code='{Code}', English='{English}', French='{French}', IsUserSpecified={IsUserSpecified}",
                        i, 
                        photovoltaic.Module.Type.Code, 
                        photovoltaic.Module.Type.English, 
                        photovoltaic.Module.Type.French,
                        photovoltaic.Module.Type.IsUserSpecified);
                }
            }
            else
            {
                _logger.LogDebug("No photovoltaic systems to process");
            }
        }

        /// <summary>
        /// Gets generation system by energy upgrade ID
        /// </summary>
        [HttpGet("energy-upgrades/{energyUpgradeId}/generations")]
        [ProducesResponseType(typeof(GenerationDto), StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<IActionResult> GetGenerationByEnergyUpgradeId(Guid energyUpgradeId)
        {
            _logger.LogInformation("Getting generation system by energy upgrade ID: {EnergyUpgradeId}", energyUpgradeId);

            var generation = await _generationService.GetGenerationByEnergyUpgradeIdAsync(energyUpgradeId);
            if (generation == null)
            {
                _logger.LogWarning("Generation system not found for energy upgrade ID: {EnergyUpgradeId}", energyUpgradeId);
                return NotFound($"Generation system not found for energy upgrade ID: {energyUpgradeId}");
            }

            var generationDto = _mapper.Map<GenerationDto>(generation);
            return Ok(generationDto);
        }

        /// <summary>
        /// Duplicates a generation system for energy upgrade
        /// </summary>
        [HttpPost("generations/{baseGenerationId}/duplicate-for-energy-upgrade")]
        [ProducesResponseType(typeof(GenerationDto), StatusCodes.Status201Created)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<IActionResult> DuplicateGenerationForEnergyUpgrade(
            Guid baseGenerationId,
            [FromQuery] Guid energyUpgradeId)
        {
            if (energyUpgradeId == Guid.Empty)
            {
                _logger.LogWarning("Energy upgrade ID is required");
                return BadRequest("Energy upgrade ID is required");
            }

            _logger.LogInformation("Duplicating generation system {BaseGenerationId} for energy upgrade {EnergyUpgradeId}",
                baseGenerationId, energyUpgradeId);

            var baseGeneration = await _generationService.GetGenerationByIdAsync(baseGenerationId);
            if (baseGeneration == null)
            {
                _logger.LogWarning("Base generation system not found: {BaseGenerationId}", baseGenerationId);
                return NotFound($"Base generation system not found: {baseGenerationId}");
            }

            var duplicatedGeneration = await _generationService.DuplicateGenerationForEnergyUpgradeAsync(
                baseGeneration, energyUpgradeId);

            var duplicatedGenerationDto = _mapper.Map<GenerationDto>(duplicatedGeneration);

            return CreatedAtAction(
                nameof(GetGenerationById),
                new { id = duplicatedGenerationDto.Id },
                duplicatedGenerationDto);
        }
    }
}