using System;
using System.Collections.Generic;

namespace GenerationService.API.Models
{
    /// <summary>
    /// Main Generation DTO following the original GenerationComponents structure
    /// Mirrors the structure from Hot/HouseFileLibrary/Components/GenerationComponents
    /// </summary>
    public class GenerationDto
    {
        public Guid Id { get; set; }
        public Guid HouseId { get; set; }
        public Guid? EnergyUpgradeId { get; set; }
        public string Label { get; set; } = "Generation";

        // Wind energy properties
        public decimal? WindEnergyContribution { get; set; }

        // Solar properties
        public bool SolarReady { get; set; } = false;
        public decimal? PhotovoltaicCapacity { get; set; }
        public bool BatteryStorage { get; set; } = false;

        // Photovoltaic systems collection
        public List<PhotovoltaicDto> PhotovoltaicSystems { get; set; } = new List<PhotovoltaicDto>();
    }
}
