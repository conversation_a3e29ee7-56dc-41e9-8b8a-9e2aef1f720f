using AutoMapper;
using GenerationService.Core.Models;
using GenerationService.API.Models;
using System.Linq;

namespace GenerationService.API.Models
{
    /// <summary>
    /// AutoMapper profile for mapping between Generation entities and DTOs
    /// Following the pattern from HvacService.API.Models.MappingProfile
    /// </summary>
    public class MappingProfile : Profile
    {
        // Helper method to create PhotovoltaicModules from DTO code
        private static PhotovoltaicModules CreatePhotovoltaicModulesFromDto(string moduleTypeCode)
        {
            // Handle null, empty, or invalid codes
            if (string.IsNullOrWhiteSpace(moduleTypeCode) || moduleTypeCode == "string")
            {
                return PhotovoltaicModules.UserSpecified;
            }

            var moduleType = PhotovoltaicModules.All.FirstOrDefault(m => m.Code == moduleTypeCode);
            var result = moduleType ?? PhotovoltaicModules.UserSpecified;

            // Ensure the result has all required properties set
            if (string.IsNullOrWhiteSpace(result.Code) ||
                string.IsNullOrWhiteSpace(result.English) ||
                string.IsNullOrWhiteSpace(result.French))
            {
                return PhotovoltaicModules.UserSpecified;
            }

            return result;
        }

        public MappingProfile()
        {
            // Main Generation mappings (following HvacService pattern)
            CreateMap<Generation, GenerationDto>()
                .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.Id))
                .ForMember(dest => dest.HouseId, opt => opt.MapFrom(src => src.HouseId))
                .ForMember(dest => dest.EnergyUpgradeId, opt => opt.MapFrom(src => src.EnergyUpgradeId))
                .ForMember(dest => dest.Label, opt => opt.MapFrom(src => src.Label))
                .ForMember(dest => dest.WindEnergyContribution, opt => opt.MapFrom(src => src.WindEnergyContribution))
                .ForMember(dest => dest.SolarReady, opt => opt.MapFrom(src => src.SolarReady))
                .ForMember(dest => dest.PhotovoltaicCapacity, opt => opt.MapFrom(src => src.PhotovoltaicCapacity))
                .ForMember(dest => dest.BatteryStorage, opt => opt.MapFrom(src => src.BatteryStorage))
                .ForMember(dest => dest.PhotovoltaicSystems, opt => opt.MapFrom(src => src.PhotovoltaicSystems));

            CreateMap<GenerationDto, Generation>()
                .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.Id))
                .ForMember(dest => dest.HouseId, opt => opt.MapFrom(src => src.HouseId))
                .ForMember(dest => dest.EnergyUpgradeId, opt => opt.MapFrom(src => src.EnergyUpgradeId))
                .ForMember(dest => dest.Label, opt => opt.MapFrom(src => src.Label ?? "Generation"))
                .ForMember(dest => dest.WindEnergyContribution, opt => opt.MapFrom(src => src.WindEnergyContribution))
                .ForMember(dest => dest.SolarReady, opt => opt.MapFrom(src => src.SolarReady))
                .ForMember(dest => dest.PhotovoltaicCapacity, opt => opt.MapFrom(src => src.PhotovoltaicCapacity))
                .ForMember(dest => dest.BatteryStorage, opt => opt.MapFrom(src => src.BatteryStorage))
                .ForMember(dest => dest.PhotovoltaicSystems, opt => opt.MapFrom(src => src.PhotovoltaicSystems ?? new List<PhotovoltaicDto>()));

            // Photovoltaic system mappings
            CreateMap<Photovoltaic, PhotovoltaicDto>()
                .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.Id))
                .ForMember(dest => dest.GenerationId, opt => opt.MapFrom(src => src.GenerationId))
                .ForMember(dest => dest.Rank, opt => opt.MapFrom(src => src.Rank))
                .ForMember(dest => dest.EquipmentInformation, opt => opt.MapFrom(src => src.EquipmentInformation))
                .ForMember(dest => dest.Array, opt => opt.MapFrom(src => src.Array))
                .ForMember(dest => dest.Efficiency, opt => opt.MapFrom(src => src.Efficiency))
                .ForMember(dest => dest.Module, opt => opt.MapFrom(src => src.Module));

            CreateMap<PhotovoltaicDto, Photovoltaic>()
                .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.Id))
                .ForMember(dest => dest.GenerationId, opt => opt.MapFrom(src => src.GenerationId))
                .ForMember(dest => dest.Rank, opt => opt.MapFrom(src => src.Rank))
                .ForMember(dest => dest.EquipmentInformation, opt => opt.MapFrom(src => src.EquipmentInformation ?? new EquipmentInformationDto()))
                .ForMember(dest => dest.Array, opt => opt.MapFrom(src => src.Array ?? new ArrayDto()))
                .ForMember(dest => dest.Efficiency, opt => opt.MapFrom(src => src.Efficiency ?? new EfficiencyDto()))
                .ForMember(dest => dest.Module, opt => opt.MapFrom(src => src.Module ?? new ModuleDto()))
                .ConstructUsing(src => new Photovoltaic())
                .AfterMap((src, dest) => {
                    // Ensure all nested objects are properly initialized
                    dest.EquipmentInformation ??= new EquipmentInformation();
                    dest.Array ??= new GenerationService.Core.Models.Array();
                    dest.Efficiency ??= new Efficiency();
                    dest.Module ??= new Module();

                    // CRITICAL FIX: Always ensure Module.Type is properly set BEFORE any EF operations
                    var moduleTypeCode = src.Module?.Type?.Code;
                    dest.Module.Type = CreatePhotovoltaicModulesFromDto(moduleTypeCode);

                    // Final safety check - this should NEVER be null
                    if (dest.Module.Type == null ||
                        string.IsNullOrWhiteSpace(dest.Module.Type.Code) ||
                        string.IsNullOrWhiteSpace(dest.Module.Type.English) ||
                        string.IsNullOrWhiteSpace(dest.Module.Type.French))
                    {
                        dest.Module.Type = PhotovoltaicModules.UserSpecified;
                    }
                });

            // Component mappings
            CreateMap<EquipmentInformation, EquipmentInformationDto>().ReverseMap();
            CreateMap<GenerationService.Core.Models.Array, ArrayDto>().ReverseMap();
            CreateMap<Efficiency, EfficiencyDto>().ReverseMap();

            // Module mapping with resource handling - FIXED VERSION
            CreateMap<Module, ModuleDto>()
                .ForMember(dest => dest.Efficiency, opt => opt.MapFrom(src => src.Efficiency))
                .ForMember(dest => dest.CellTemperature, opt => opt.MapFrom(src => src.CellTemperature))
                .ForMember(dest => dest.CoefficientOfEfficiency, opt => opt.MapFrom(src => src.CoefficientOfEfficiency))
                .ForMember(dest => dest.Type, opt => opt.MapFrom(src => src.Type));

            CreateMap<ModuleDto, Module>()
                .ForMember(dest => dest.Efficiency, opt => opt.MapFrom(src => src.Efficiency))
                .ForMember(dest => dest.CellTemperature, opt => opt.MapFrom(src => src.CellTemperature))
                .ForMember(dest => dest.CoefficientOfEfficiency, opt => opt.MapFrom(src => src.CoefficientOfEfficiency))
                .ForMember(dest => dest.Type, opt => opt.Ignore()) // CRITICAL: Don't map here, do it in AfterMap
                .ConstructUsing(src => new Module())
                .AfterMap((src, dest) => {
                    // CRITICAL FIX: Set Module.Type in AfterMap to ensure it's always valid
                    var moduleTypeCode = src.Type?.Code;
                    dest.Type = CreatePhotovoltaicModulesFromDto(moduleTypeCode);

                    // Final verification - this is the root cause fix
                    if (dest.Type == null ||
                        string.IsNullOrWhiteSpace(dest.Type.Code) ||
                        string.IsNullOrWhiteSpace(dest.Type.English) ||
                        string.IsNullOrWhiteSpace(dest.Type.French))
                    {
                        dest.Type = PhotovoltaicModules.UserSpecified;
                    }
                });

            // Resource mappings
            CreateMap<PhotovoltaicModules, PhotovoltaicModulesDto>()
                .ForMember(dest => dest.Code, opt => opt.MapFrom(src => src.Code))
                .ForMember(dest => dest.English, opt => opt.MapFrom(src => src.English))
                .ForMember(dest => dest.French, opt => opt.MapFrom(src => src.French))
                .ForMember(dest => dest.IsUserSpecified, opt => opt.MapFrom(src => src.IsUserSpecified));

            CreateMap<PhotovoltaicModulesDto, PhotovoltaicModules>()
                .ConvertUsing(src => CreatePhotovoltaicModulesFromDto(src != null ? src.Code : null));
        }
    }
}