using Microsoft.EntityFrameworkCore;
using GenerationService.Core.Models;

namespace GenerationService.Infrastructure.Data
{
    public class GenerationDbContext : DbContext
    {
        public GenerationDbContext(DbContextOptions<GenerationDbContext> options) : base(options)
        {
        }

        protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder)
        {
            optionsBuilder.ConfigureWarnings(warnings =>
                warnings.Ignore(Microsoft.EntityFrameworkCore.Diagnostics.RelationalEventId.ForeignKeyPropertiesMappedToUnrelatedTables));
        }

        // Main Generation entities
        public DbSet<Generation> Generations { get; set; } = null!;
        public DbSet<Photovoltaic> Photovoltaics { get; set; } = null!;

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            // Set default schema for all entities in this context
            modelBuilder.HasDefaultSchema("generation");

            // Ignore resource classes - they are not database entities
            modelBuilder.Ignore<ResourceList>();
            modelBuilder.Ignore<PhotovoltaicModules>();

            // Ignore base classes that are used for composition, not inheritance
            modelBuilder.Ignore<CodeAndText>();

            base.OnModelCreating(modelBuilder);

            // ===============================
            // MAIN GENERATION CONFIGURATION
            // ===============================

            modelBuilder.Entity<Generation>(entity =>
            {
                entity.ToTable("Generations", "generation");
                entity.HasKey(e => e.Id);
                entity.Property(e => e.EnergyUpgradeId).IsRequired(false);
                entity.Property(e => e.Label).HasMaxLength(100);
                entity.Property(e => e.WindEnergyContribution).HasPrecision(18, 2);
                entity.Property(e => e.PhotovoltaicCapacity).HasPrecision(18, 2);

                // One-to-many relationship with Photovoltaic systems
                entity.HasMany(e => e.PhotovoltaicSystems)
                      .WithOne()
                      .HasForeignKey("GenerationId")
                      .OnDelete(DeleteBehavior.Cascade);
            });

            // ===============================
            // PHOTOVOLTAIC SYSTEM CONFIGURATION
            // ===============================

            modelBuilder.Entity<Photovoltaic>(entity =>
            {
                entity.ToTable("Photovoltaics", "generation");
                entity.HasKey(e => e.Id);
                entity.Property(e => e.Rank);

                // Configure EquipmentInformation as owned entity
                entity.OwnsOne(e => e.EquipmentInformation, eq =>
                {
                    eq.Property(e => e.Manufacturer).HasMaxLength(200);
                    eq.Property(e => e.Model).HasMaxLength(200);
                    eq.Property(e => e.Description).HasMaxLength(500);
                });

                // Configure Array as owned entity
                entity.OwnsOne(e => e.Array, array =>
                {
                    array.Property(a => a.Area).HasPrecision(18, 2);
                    array.Property(a => a.Slope).HasPrecision(18, 2);
                    array.Property(a => a.Azimuth).HasPrecision(18, 2);
                });

                // Configure Efficiency as owned entity
                entity.OwnsOne(e => e.Efficiency, eff =>
                {
                    eff.Property(e => e.MiscellaneousLosses).HasPrecision(18, 2);
                    eff.Property(e => e.OtherPowerLosses).HasPrecision(18, 2);
                    eff.Property(e => e.InverterEfficiency).HasPrecision(18, 2);
                    eff.Property(e => e.GridAbsorptionRate).HasPrecision(18, 2);
                });

                // Configure Module as owned entity
                entity.OwnsOne(e => e.Module, mod =>
                {
                    mod.Property(m => m.Efficiency).HasPrecision(18, 2);
                    mod.Property(m => m.CellTemperature).HasPrecision(18, 2);
                    mod.Property(m => m.CoefficientOfEfficiency).HasPrecision(18, 2);

                    // Configure PhotovoltaicModules as owned entity
                    mod.OwnsOne(m => m.Type, type =>
                    {
                        type.Property(t => t.Code).HasMaxLength(10).IsRequired();
                        type.Property(t => t.English).HasMaxLength(100).IsRequired();
                        type.Property(t => t.French).HasMaxLength(100).IsRequired();
                        type.Property(t => t.IsUserSpecified).IsRequired().HasDefaultValue(false);
                    });
                });
            });
        }
    }
}