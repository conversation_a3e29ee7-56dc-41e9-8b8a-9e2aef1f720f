using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using GenerationService.Core.Interfaces;
using GenerationService.Core.Models;
using GenerationService.Infrastructure.Data;

namespace GenerationService.Infrastructure.Repositories
{
    /// <summary>
    /// Repository implementation for generation operations
    /// Following HvacService pattern with enhanced Module.Type handling
    /// </summary>
    public class GenerationRepository : IGenerationRepository
    {
        private readonly GenerationDbContext _context;
        private readonly ILogger<GenerationRepository> _logger;

        public GenerationRepository(
            GenerationDbContext context,
            ILogger<GenerationRepository> logger)
        {
            _context = context;
            _logger = logger;
        }

        /// <summary>
        /// Gets all generation systems
        /// </summary>
        public async Task<IEnumerable<Generation>> GetAllGenerationsAsync()
        {
            _logger.LogInformation("Getting all generation systems from repository");
            return await GetGenerationWithIncludes().ToListAsync();
        }

        /// <summary>
        /// Gets generation system for a specific house
        /// </summary>
        public async Task<Generation?> GetGenerationByHouseIdAsync(Guid houseId)
        {
            _logger.LogInformation("Getting generation system for house ID: {HouseId} from repository", houseId);
            return await GetGenerationWithIncludes()
                .FirstOrDefaultAsync(g => g.HouseId == houseId);
        }

        /// <summary>
        /// Gets a specific generation system by ID
        /// </summary>
        public async Task<Generation?> GetGenerationByIdAsync(Guid id)
        {
            _logger.LogInformation("Getting generation system with ID: {Id} from repository", id);
            return await GetGenerationWithIncludes()
                .FirstOrDefaultAsync(g => g.Id == id);
        }

        /// <summary>
        /// Creates a new generation system
        /// </summary>
        public async Task<Generation> CreateGenerationAsync(Generation generation)
        {
            _logger.LogInformation("Creating new generation system for house ID: {HouseId} from repository", generation.HouseId);

            // Clear any existing tracking to prevent conflicts
            _context.ChangeTracker.Clear();

            // Recreate photovoltaic systems manually to prevent EF tracking issues
            if (generation.PhotovoltaicSystems != null && generation.PhotovoltaicSystems.Any())
            {
                var originalSystems = generation.PhotovoltaicSystems.ToList();
                generation.PhotovoltaicSystems.Clear();

                foreach (var originalSystem in originalSystems)
                {
                    var manualSystem = CreatePhotovoltaicManually(originalSystem, generation.Id);
                    generation.PhotovoltaicSystems.Add(manualSystem);
                }
            }

            _context.Generations.Add(generation);
            await _context.SaveChangesAsync();

            _logger.LogInformation("Successfully created generation system with ID: {Id}", generation.Id);
            return generation;
        }

        /// <summary>
        /// Updates an existing generation system
        /// </summary>
        public async Task UpdateGenerationAsync(Generation generation)
        {
            _logger.LogInformation("Updating generation system with ID: {Id} from repository", generation.Id);

            var strategy = _context.Database.CreateExecutionStrategy();

            await strategy.ExecuteAsync(async () =>
            {
                await using var transaction = await _context.Database.BeginTransactionAsync();

                try
                {
                    var existingEntity = await GetGenerationWithIncludes()
                        .FirstOrDefaultAsync(g => g.Id == generation.Id);

                    if (existingEntity == null)
                    {
                        throw new InvalidOperationException($"Generation with ID {generation.Id} not found");
                    }

                    // Update main properties
                    _context.Entry(existingEntity).CurrentValues.SetValues(generation);

                    // Update photovoltaic systems collection
                    await UpdatePhotovoltaicSystems(existingEntity, generation);

                    // Save changes and commit
                    await _context.SaveChangesAsync();
                    await transaction.CommitAsync();

                    _logger.LogInformation("Successfully updated generation system with ID: {Id}", generation.Id);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error updating generation system with ID: {Id}", generation.Id);
                    await transaction.RollbackAsync();
                    throw;
                }
            });
        }

        /// <summary>
        /// Deletes a generation system
        /// </summary>
        public async Task DeleteGenerationAsync(Guid id)
        {
            _logger.LogInformation("Deleting generation system with ID: {Id} from repository", id);

            var generation = await GetGenerationWithIncludes()
                .FirstOrDefaultAsync(g => g.Id == id);

            if (generation != null)
            {
                _context.Generations.Remove(generation);
                await _context.SaveChangesAsync();
                _logger.LogInformation("Successfully deleted generation system with ID: {Id}", id);
            }
        }

        /// <summary>
        /// Helper method to get Generation queryable with all nested includes
        /// </summary>
        private IQueryable<Generation> GetGenerationWithIncludes()
        {
            return _context.Generations
                .AsNoTracking()
                .Include(g => g.PhotovoltaicSystems)
                    .ThenInclude(pv => pv.EquipmentInformation)
                .Include(g => g.PhotovoltaicSystems)
                    .ThenInclude(pv => pv.Array)
                .Include(g => g.PhotovoltaicSystems)
                    .ThenInclude(pv => pv.Efficiency)
                .Include(g => g.PhotovoltaicSystems)
                    .ThenInclude(pv => pv.Module);
        }

        /// <summary>
        /// Updates photovoltaic systems collection following VentilationService pattern
        /// ENHANCED VERSION with better Module.Type handling
        /// </summary>
        private async Task UpdatePhotovoltaicSystems(Generation existingGeneration, Generation updatedGeneration)
        {
            // Load existing entities from database (not from tracked entities)
            var existingEntities = await _context.Photovoltaics
                .Where(p => p.GenerationId == existingGeneration.Id)
                .ToListAsync();

            var newEntities = updatedGeneration.PhotovoltaicSystems ?? new List<Photovoltaic>();

            // Create a mapping of existing entities by ID for quick lookup
            var existingEntitiesDict = existingEntities.ToDictionary(e => e.Id);

            // Track which existing entities should be kept (either updated or unchanged)
            var entitiesToKeep = new HashSet<Guid>();

            // First pass: Update existing entities and track which ones to keep
            foreach (var newEntity in newEntities)
            {
                if (newEntity.Id != Guid.Empty && existingEntitiesDict.ContainsKey(newEntity.Id))
                {
                    entitiesToKeep.Add(newEntity.Id);
                    var existingEntity = existingEntitiesDict[newEntity.Id];

                    // Update existing entity properties manually (following HvacService pattern)
                    UpdatePhotovoltaicPropertiesManually(existingEntity, newEntity);

                    // Mark as modified
                    _context.Entry(existingEntity).State = EntityState.Modified;
                }
            }

            // Remove entities that are no longer needed
            var entitiesToRemove = existingEntities.Where(existing =>
                !entitiesToKeep.Contains(existing.Id)).ToList();

            if (entitiesToRemove.Any())
            {
                _context.RemoveRange(entitiesToRemove);
            }

            // Second pass: Add new entities manually (following HvacService pattern)
            foreach (var newEntity in newEntities)
            {
                if (newEntity.Id == Guid.Empty || !existingEntitiesDict.ContainsKey(newEntity.Id))
                {
                    _logger.LogInformation("Creating new photovoltaic entity. Original Module.Type.Code: '{Code}'", 
                        newEntity.Module?.Type?.Code ?? "NULL");
                    
                    // Create new photovoltaic entity manually with explicit property assignment
                    var photovoltaic = CreatePhotovoltaicManually(newEntity, existingGeneration.Id);
                    
                    _logger.LogInformation("Created photovoltaic entity. Final Module.Type.Code: '{Code}', English: '{English}'", 
                        photovoltaic.Module?.Type?.Code ?? "NULL",
                        photovoltaic.Module?.Type?.English ?? "NULL");
                    
                    _context.Add(photovoltaic);
                }
            }
        }

        /// <summary>
        /// Create new photovoltaic entity manually (following HvacService pattern)
        /// ENHANCED VERSION with comprehensive Module.Type validation
        /// </summary>
        private Photovoltaic CreatePhotovoltaicManually(Photovoltaic source, Guid generationId)
        {
            _logger.LogInformation("CreatePhotovoltaicManually called. Source Module.Type.Code: '{Code}'", 
                source.Module?.Type?.Code ?? "NULL");

            // Get a valid module type first - with extra validation
            var sourceModuleType = source.Module?.Type;
            PhotovoltaicModules validModuleType;

            if (sourceModuleType == null || 
                string.IsNullOrWhiteSpace(sourceModuleType.Code) ||
                sourceModuleType.Code == "string" ||
                string.IsNullOrWhiteSpace(sourceModuleType.English) ||
                string.IsNullOrWhiteSpace(sourceModuleType.French))
            {
                _logger.LogWarning("Source module type is invalid, using UserSpecified default");
                validModuleType = PhotovoltaicModules.UserSpecified;
            }
            else
            {
                // Try to find matching type from static list
                var matchingType = PhotovoltaicModules.All.FirstOrDefault(m => m.Code == sourceModuleType.Code);
                validModuleType = matchingType ?? PhotovoltaicModules.UserSpecified;
                
                _logger.LogInformation("Found matching module type: '{Code}' -> '{English}'", 
                    validModuleType.Code, validModuleType.English);
            }
            
            var photovoltaic = new Photovoltaic
            {
                Id = source.Id != Guid.Empty ? source.Id : Guid.NewGuid(),
                GenerationId = generationId,
                Rank = source.Rank,

                // Create EquipmentInformation manually
                EquipmentInformation = new EquipmentInformation
                {
                    Manufacturer = source.EquipmentInformation?.Manufacturer,
                    Model = source.EquipmentInformation?.Model,
                    Description = source.EquipmentInformation?.Description
                },

                // Create Array manually
                Array = new GenerationService.Core.Models.Array
                {
                    Area = source.Array?.Area ?? 0m,
                    Slope = source.Array?.Slope ?? 0m,
                    Azimuth = source.Array?.Azimuth ?? 0m
                },

                // Create Efficiency manually
                Efficiency = new Efficiency
                {
                    MiscellaneousLosses = source.Efficiency?.MiscellaneousLosses ?? 0m,
                    OtherPowerLosses = source.Efficiency?.OtherPowerLosses ?? 0m,
                    InverterEfficiency = source.Efficiency?.InverterEfficiency ?? 0m,
                    GridAbsorptionRate = source.Efficiency?.GridAbsorptionRate ?? 0m
                },

                // Create Module manually with explicit Type initialization - CRITICAL FIX
                Module = new Module
                {
                    Efficiency = source.Module?.Efficiency ?? 14.2m,
                    CellTemperature = source.Module?.CellTemperature ?? 45.0m,
                    CoefficientOfEfficiency = source.Module?.CoefficientOfEfficiency ?? 0.72m,
                    Type = validModuleType // Use the pre-validated type
                }
            };

            // Final safety check before returning - TRIPLE CHECK
            if (photovoltaic.Module?.Type == null ||
                string.IsNullOrWhiteSpace(photovoltaic.Module.Type.Code) ||
                string.IsNullOrWhiteSpace(photovoltaic.Module.Type.English) ||
                string.IsNullOrWhiteSpace(photovoltaic.Module.Type.French))
            {
                _logger.LogError("CRITICAL: Module.Type is still invalid after creation, forcing UserSpecified");
                photovoltaic.Module ??= new Module();
                photovoltaic.Module.Type = PhotovoltaicModules.UserSpecified;
            }

            _logger.LogInformation("CreatePhotovoltaicManually completed. Final Module.Type - Code: '{Code}', English: '{English}', French: '{French}', IsUserSpecified: {IsUserSpecified}",
                photovoltaic.Module.Type.Code,
                photovoltaic.Module.Type.English,
                photovoltaic.Module.Type.French,
                photovoltaic.Module.Type.IsUserSpecified);

            return photovoltaic;
        }

        /// <summary>
        /// Update photovoltaic properties manually (following HvacService pattern)
        /// ENHANCED VERSION with better Module.Type handling
        /// </summary>
        private void UpdatePhotovoltaicPropertiesManually(Photovoltaic existing, Photovoltaic updated)
        {
            // Update main properties
            existing.Rank = updated.Rank;

            // Update EquipmentInformation properties
            if (updated.EquipmentInformation != null)
            {
                existing.EquipmentInformation ??= new EquipmentInformation();
                existing.EquipmentInformation.Manufacturer = updated.EquipmentInformation.Manufacturer;
                existing.EquipmentInformation.Model = updated.EquipmentInformation.Model;
                existing.EquipmentInformation.Description = updated.EquipmentInformation.Description;
            }

            // Update Array properties
            if (updated.Array != null)
            {
                existing.Array ??= new GenerationService.Core.Models.Array();
                existing.Array.Area = updated.Array.Area;
                existing.Array.Slope = updated.Array.Slope;
                existing.Array.Azimuth = updated.Array.Azimuth;
            }

            // Update Efficiency properties
            if (updated.Efficiency != null)
            {
                existing.Efficiency ??= new Efficiency();
                existing.Efficiency.MiscellaneousLosses = updated.Efficiency.MiscellaneousLosses;
                existing.Efficiency.OtherPowerLosses = updated.Efficiency.OtherPowerLosses;
                existing.Efficiency.InverterEfficiency = updated.Efficiency.InverterEfficiency;
                existing.Efficiency.GridAbsorptionRate = updated.Efficiency.GridAbsorptionRate;
            }

            // Update Module properties with enhanced validation
            if (updated.Module != null)
            {
                existing.Module ??= new Module();
                existing.Module.Efficiency = updated.Module.Efficiency;
                existing.Module.CellTemperature = updated.Module.CellTemperature;
                existing.Module.CoefficientOfEfficiency = updated.Module.CoefficientOfEfficiency;

                // Update Module.Type manually to prevent EF tracking issues
                existing.Module.Type = GetValidModuleType(updated.Module.Type);
                
                _logger.LogInformation("Updated existing photovoltaic Module.Type to: Code='{Code}', English='{English}'",
                    existing.Module.Type.Code, existing.Module.Type.English);
            }
        }

        /// <summary>
        /// Get valid module type, ensuring it's never null (following HvacService pattern)
        /// ENHANCED VERSION with better validation
        /// </summary>
        private PhotovoltaicModules GetValidModuleType(PhotovoltaicModules sourceType)
        {
            // If source type is null or invalid, return UserSpecified
            if (sourceType == null)
            {
                _logger.LogDebug("Source module type is null, returning UserSpecified");
                return PhotovoltaicModules.UserSpecified;
            }

            // Validate that all required properties are set
            if (string.IsNullOrWhiteSpace(sourceType.Code) ||
                sourceType.Code == "string" ||
                string.IsNullOrWhiteSpace(sourceType.English) ||
                string.IsNullOrWhiteSpace(sourceType.French))
            {
                _logger.LogDebug("Source module type has invalid properties (Code: '{Code}', English: '{English}'), returning UserSpecified",
                    sourceType.Code ?? "NULL", sourceType.English ?? "NULL");
                return PhotovoltaicModules.UserSpecified;
            }

            // Try to find matching type from static list to ensure consistency
            var validType = PhotovoltaicModules.All.FirstOrDefault(m => m.Code == sourceType.Code);
            var result = validType ?? PhotovoltaicModules.UserSpecified;
            
            _logger.LogDebug("GetValidModuleType result: Code='{Code}', English='{English}'",
                result.Code, result.English);
            
            return result;
        }

        /// <summary>
        /// Gets generation system by energy upgrade ID
        /// </summary>
        public async Task<Generation?> GetGenerationByEnergyUpgradeIdAsync(Guid energyUpgradeId)
        {
            _logger.LogInformation("Getting generation system by energy upgrade ID: {EnergyUpgradeId}", energyUpgradeId);

            return await GetGenerationWithIncludes()
                .FirstOrDefaultAsync(g => g.EnergyUpgradeId == energyUpgradeId);
        }

        /// <summary>
        /// Duplicates a generation system for energy upgrade
        /// </summary>
        public async Task<Generation> DuplicateGenerationForEnergyUpgradeAsync(Generation baseGeneration, Guid energyUpgradeId)
        {
            _logger.LogInformation("Duplicating generation system {BaseId} for energy upgrade {EnergyUpgradeId}",
                baseGeneration.Id, energyUpgradeId);

            // Create a deep copy of the generation system
            var duplicatedGeneration = new Generation
            {
                Id = Guid.NewGuid(),
                HouseId = baseGeneration.HouseId,
                EnergyUpgradeId = energyUpgradeId,
                Label = baseGeneration.Label,
                WindEnergyContribution = baseGeneration.WindEnergyContribution,
                SolarReady = baseGeneration.SolarReady,
                PhotovoltaicCapacity = baseGeneration.PhotovoltaicCapacity,
                BatteryStorage = baseGeneration.BatteryStorage,
                PhotovoltaicSystems = new List<Photovoltaic>()
            };

            // Deep copy photovoltaic systems
            foreach (var basePv in baseGeneration.PhotovoltaicSystems)
            {
                var duplicatedPv = new Photovoltaic
                {
                    Id = Guid.NewGuid(),
                    Rank = basePv.Rank,
                    EquipmentInformation = basePv.EquipmentInformation != null ? new EquipmentInformation
                    {
                        Manufacturer = basePv.EquipmentInformation.Manufacturer,
                        Model = basePv.EquipmentInformation.Model,
                        Description = basePv.EquipmentInformation.Description
                    } : new EquipmentInformation(),
                    Array = basePv.Array != null ? new GenerationService.Core.Models.Array
                    {
                        Area = basePv.Array.Area,
                        Slope = basePv.Array.Slope,
                        Azimuth = basePv.Array.Azimuth
                    } : new GenerationService.Core.Models.Array(),
                    Module = basePv.Module != null ? new Module
                    {
                        Efficiency = basePv.Module.Efficiency,
                        CellTemperature = basePv.Module.CellTemperature,
                        CoefficientOfEfficiency = basePv.Module.CoefficientOfEfficiency,
                        Type = basePv.Module.Type
                    } : new Module(),
                    Efficiency = basePv.Efficiency != null ? new Efficiency
                    {
                        MiscellaneousLosses = basePv.Efficiency.MiscellaneousLosses,
                        OtherPowerLosses = basePv.Efficiency.OtherPowerLosses,
                        InverterEfficiency = basePv.Efficiency.InverterEfficiency,
                        GridAbsorptionRate = basePv.Efficiency.GridAbsorptionRate
                    } : new Efficiency()
                };

                duplicatedGeneration.PhotovoltaicSystems.Add(duplicatedPv);
            }

            // Add to context and save
            _context.Generations.Add(duplicatedGeneration);
            await _context.SaveChangesAsync();

            _logger.LogInformation("Successfully duplicated generation system with new ID: {Id}", duplicatedGeneration.Id);
            return duplicatedGeneration;
        }
    }
}