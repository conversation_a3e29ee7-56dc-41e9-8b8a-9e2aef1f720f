using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using GenerationService.Core.Interfaces;
using GenerationService.Core.Models;
using Microsoft.Extensions.Logging;

namespace GenerationService.Core.Services
{
    /// <summary>
    /// Service implementation for generation operations
    /// Following HvacService pattern
    /// </summary>
    public class GenerationServiceImpl : IGenerationService
    {
        private readonly IGenerationRepository _repository;
        private readonly ILogger<GenerationServiceImpl> _logger;

        public GenerationServiceImpl(IGenerationRepository repository, ILogger<GenerationServiceImpl> logger)
        {
            _repository = repository;
            _logger = logger;
        }

        /// <summary>
        /// Gets all generation systems
        /// </summary>
        public async Task<IEnumerable<Generation>> GetAllGenerationsAsync()
        {
            _logger.LogInformation("Getting all generation systems from repository");
            return await _repository.GetAllGenerationsAsync();
        }

        /// <summary>
        /// Gets generation system for a specific house
        /// </summary>
        public async Task<Generation?> GetGenerationByHouseIdAsync(Guid houseId)
        {
            _logger.LogInformation("Getting generation system for house ID: {HouseId}", houseId);
            return await _repository.GetGenerationByHouseIdAsync(houseId);
        }

        /// <summary>
        /// Gets a specific generation system by ID
        /// </summary>
        public async Task<Generation?> GetGenerationByIdAsync(Guid id)
        {
            _logger.LogInformation("Getting generation system with ID: {Id}", id);
            return await _repository.GetGenerationByIdAsync(id);
        }

        /// <summary>
        /// Creates a new generation system
        /// </summary>
        public async Task<Generation> CreateGenerationAsync(Generation generation)
        {
            if (generation == null)
                throw new ArgumentNullException(nameof(generation));

            _logger.LogInformation("Creating new generation system for house ID: {HouseId}", generation.HouseId);

            // TODO: Add business validation logic here

            return await _repository.CreateGenerationAsync(generation);
        }

        /// <summary>
        /// Updates an existing generation system
        /// </summary>
        public async Task UpdateGenerationAsync(Generation generation)
        {
            if (generation == null)
                throw new ArgumentNullException(nameof(generation));

            _logger.LogInformation("Updating generation system with ID: {Id}", generation.Id);

            // Check if the generation system exists
            var existingGeneration = await _repository.GetGenerationByIdAsync(generation.Id);
            if (existingGeneration == null)
            {
                throw new InvalidOperationException($"Generation system with ID {generation.Id} not found");
            }

            // TODO: Add business validation logic here

            await _repository.UpdateGenerationAsync(generation);
        }

        /// <summary>
        /// Deletes a generation system
        /// </summary>
        public async Task DeleteGenerationAsync(Guid id)
        {
            _logger.LogInformation("Deleting generation system with ID: {Id}", id);

            // Check if the generation system exists
            var existingGeneration = await _repository.GetGenerationByIdAsync(id);
            if (existingGeneration == null)
            {
                throw new InvalidOperationException($"Generation system with ID {id} not found");
            }

            await _repository.DeleteGenerationAsync(id);
        }

        /// <summary>
        /// Gets generation system by energy upgrade ID
        /// </summary>
        public async Task<Generation?> GetGenerationByEnergyUpgradeIdAsync(Guid energyUpgradeId)
        {
            _logger.LogInformation("Getting generation system by energy upgrade ID: {EnergyUpgradeId}", energyUpgradeId);
            return await _repository.GetGenerationByEnergyUpgradeIdAsync(energyUpgradeId);
        }

        /// <summary>
        /// Duplicates a generation system for energy upgrade
        /// </summary>
        public async Task<Generation> DuplicateGenerationForEnergyUpgradeAsync(Generation baseGeneration, Guid energyUpgradeId)
        {
            _logger.LogInformation("Duplicating generation system {BaseId} for energy upgrade {EnergyUpgradeId}",
                baseGeneration.Id, energyUpgradeId);

            // TODO: Add business validation logic here

            return await _repository.DuplicateGenerationForEnergyUpgradeAsync(baseGeneration, energyUpgradeId);
        }
    }
}