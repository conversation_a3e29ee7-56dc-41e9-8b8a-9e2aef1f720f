using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using GenerationService.Core.Models;

namespace GenerationService.Core.Interfaces
{
    /// <summary>
    /// Repository interface for generation operations
    /// Following HvacService pattern
    /// </summary>
    public interface IGenerationRepository
    {
        /// <summary>
        /// Gets all generation systems
        /// </summary>
        Task<IEnumerable<Generation>> GetAllGenerationsAsync();

        /// <summary>
        /// Gets generation system for a specific house
        /// </summary>
        Task<Generation?> GetGenerationByHouseIdAsync(Guid houseId);

        /// <summary>
        /// Gets a specific generation system by ID
        /// </summary>
        Task<Generation?> GetGenerationByIdAsync(Guid id);

        /// <summary>
        /// Creates a new generation system
        /// </summary>
        Task<Generation> CreateGenerationAsync(Generation generation);

        /// <summary>
        /// Updates an existing generation system
        /// </summary>
        Task UpdateGenerationAsync(Generation generation);

        /// <summary>
        /// Deletes a generation system
        /// </summary>
        Task DeleteGenerationAsync(Guid id);

        /// <summary>
        /// Gets generation system by energy upgrade ID
        /// </summary>
        Task<Generation?> GetGenerationByEnergyUpgradeIdAsync(Guid energyUpgradeId);

        /// <summary>
        /// Duplicates a generation system for energy upgrade
        /// </summary>
        Task<Generation> DuplicateGenerationForEnergyUpgradeAsync(Generation baseGeneration, Guid energyUpgradeId);
    }
}