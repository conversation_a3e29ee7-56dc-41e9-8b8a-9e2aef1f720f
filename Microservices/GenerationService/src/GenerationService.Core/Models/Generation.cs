using System;
using System.Collections.Generic;

namespace GenerationService.Core.Models
{
    /// <summary>
    /// Main Generation model following the original GenerationComponents structure
    /// Mirrors the structure from Hot/HouseFileLibrary/Components/GenerationComponents
    /// </summary>
    public class Generation
    {
        public Guid Id { get; set; }
        public Guid HouseId { get; set; }
        public Guid? EnergyUpgradeId { get; set; }
        public string Label { get; set; } = "Generation";

        // Wind energy properties
        public decimal? WindEnergyContribution { get; set; }

        // Solar properties
        public bool SolarReady { get; set; } = false;
        public decimal? PhotovoltaicCapacity { get; set; }
        public bool BatteryStorage { get; set; } = false;

        // Photovoltaic systems collection
        public List<Photovoltaic> PhotovoltaicSystems { get; set; } = new List<Photovoltaic>();

        public Generation()
        {
        }

        public Generation(Generation toCopy)
        {
            if (toCopy != null)
            {
                Id = toCopy.Id;
                HouseId = toCopy.HouseId;
                Label = toCopy.Label;
                WindEnergyContribution = toCopy.WindEnergyContribution;
                SolarReady = toCopy.SolarReady;
                PhotovoltaicCapacity = toCopy.PhotovoltaicCapacity;
                BatteryStorage = toCopy.BatteryStorage;

                // Deep copy the photovoltaic systems collection
                PhotovoltaicSystems = new List<Photovoltaic>();
                if (toCopy.PhotovoltaicSystems != null)
                {
                    foreach (var photovoltaic in toCopy.PhotovoltaicSystems)
                    {
                        PhotovoltaicSystems.Add(new Photovoltaic(photovoltaic));
                    }
                }
            }
        }
    }
}
