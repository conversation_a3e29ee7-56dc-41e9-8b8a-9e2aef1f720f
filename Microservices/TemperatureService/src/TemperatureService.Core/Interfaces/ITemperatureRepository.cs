using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using TemperatureService.Core.Models;

namespace TemperatureService.Core.Interfaces
{
    public interface ITemperatureRepository
    {
        // Temperature CRUD operations
        Task<Temperature> AddAsync(Temperature temperature);
        Task<Temperature> GetByIdAsync(Guid id);
        Task<Temperature> GetByHouseIdAsync(Guid houseId);
        Task<IEnumerable<Temperature>> GetAllAsync();
        Task<Temperature> UpdateAsync(Temperature temperature);
        Task<bool> DeleteAsync(Guid id);
        Task<bool> ExistsAsync(Guid id);

        // Individual component operations removed - use main CRUD operations instead

        // Energy upgrade operations
        Task<Temperature?> GetTemperatureByEnergyUpgradeIdAsync(Guid energyUpgradeId);
        Task<Temperature> DuplicateTemperatureForEnergyUpgradeAsync(Temperature baseTemperature, Guid energyUpgradeId);
    }
}
