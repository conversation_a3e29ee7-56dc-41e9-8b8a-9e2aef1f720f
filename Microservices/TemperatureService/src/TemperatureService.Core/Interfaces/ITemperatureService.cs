using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using TemperatureService.Core.Models;

namespace TemperatureService.Core.Interfaces
{
    public interface ITemperatureService
    {
        // Temperature CRUD operations
        Task<Temperature> CreateTemperatureAsync(Temperature temperature);
        Task<Temperature> GetTemperatureByIdAsync(Guid id);
        Task<Temperature> GetTemperatureByHouseIdAsync(Guid houseId);
        Task<IEnumerable<Temperature>> GetAllTemperaturesAsync();
        Task<Temperature> UpdateTemperatureAsync(Temperature temperature);
        Task<bool> DeleteTemperatureAsync(Guid id);
        
        // Individual component operations removed - use main CRUD operations instead
        
        // Reference Data
        Task<IEnumerable<AllowableRise>> GetAllowableRiseOptionsAsync();
        
        // Validation
        Task<bool> ValidateTemperatureAsync(Temperature temperature);
        
        // Utility
        Task<bool> ExistsAsync(Guid id);

        // Energy upgrade operations
        Task<Temperature?> GetTemperatureByEnergyUpgradeIdAsync(Guid energyUpgradeId);
        Task<Temperature> DuplicateTemperatureForEnergyUpgradeAsync(Temperature baseTemperature, Guid energyUpgradeId);
    }
}
