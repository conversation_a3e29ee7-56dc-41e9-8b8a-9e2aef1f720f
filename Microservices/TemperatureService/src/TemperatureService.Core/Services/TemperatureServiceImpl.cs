using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using TemperatureService.Core.Interfaces;
using TemperatureService.Core.Models;

namespace TemperatureService.Core.Services
{
    public class TemperatureServiceImpl : ITemperatureService
    {
        private readonly ITemperatureRepository _repository;
        private readonly ILogger<TemperatureServiceImpl> _logger;

        public TemperatureServiceImpl(ITemperatureRepository repository, ILogger<TemperatureServiceImpl> logger)
        {
            _repository = repository ?? throw new ArgumentNullException(nameof(repository));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        }

        public async Task<Temperature> CreateTemperatureAsync(Temperature temperature)
        {
            _logger.LogInformation("Creating new temperature for house ID: {HouseId}", temperature.HouseId);

            if (temperature.Id == Guid.Empty)
                temperature.Id = Guid.NewGuid();

            // Following EnvelopeService pattern - call SetDefaults and set up relationships
            temperature.SetDefaults();

            // Set up MainFloors relationship
            if (temperature.MainFloors != null)
            {
                if (temperature.MainFloors.Id == Guid.Empty)
                    temperature.MainFloors.Id = Guid.NewGuid();
                temperature.MainFloors.TemperatureId = temperature.Id;
            }

            // Set up VentilationBasement relationship
            if (temperature.VentilationBasement != null)
            {
                if (temperature.VentilationBasement.Id == Guid.Empty)
                    temperature.VentilationBasement.Id = Guid.NewGuid();
                temperature.VentilationBasement.TemperatureId = temperature.Id;
            }

            // Set up Equipment relationship
            if (temperature.Equipment != null)
            {
                if (temperature.Equipment.Id == Guid.Empty)
                    temperature.Equipment.Id = Guid.NewGuid();
                temperature.Equipment.TemperatureId = temperature.Id;
            }

            // Set up Crawlspace relationship
            if (temperature.Crawlspace != null)
            {
                if (temperature.Crawlspace.Id == Guid.Empty)
                    temperature.Crawlspace.Id = Guid.NewGuid();
                temperature.Crawlspace.TemperatureId = temperature.Id;
            }

            return await _repository.AddAsync(temperature);
        }

        public async Task<Temperature> GetTemperatureByIdAsync(Guid id)
        {
            _logger.LogInformation("Getting temperature with ID: {TemperatureId}", id);
            return await _repository.GetByIdAsync(id);
        }

        public async Task<Temperature> GetTemperatureByHouseIdAsync(Guid houseId)
        {
            _logger.LogInformation("Getting temperature for house ID: {HouseId}", houseId);
            return await _repository.GetByHouseIdAsync(houseId);
        }

        public async Task<IEnumerable<Temperature>> GetAllTemperaturesAsync()
        {
            _logger.LogInformation("Getting all temperatures");
            return await _repository.GetAllAsync();
        }

        public async Task<Temperature> UpdateTemperatureAsync(Temperature temperature)
        {
            _logger.LogInformation("Updating temperature with ID: {TemperatureId}", temperature.Id);
            
            var existingTemperature = await _repository.GetByIdAsync(temperature.Id);
            if (existingTemperature == null)
                throw new KeyNotFoundException($"Temperature with ID {temperature.Id} not found");

            return await _repository.UpdateAsync(temperature);
        }

        public async Task<bool> DeleteTemperatureAsync(Guid id)
        {
            _logger.LogInformation("Deleting temperature with ID: {TemperatureId}", id);
            return await _repository.DeleteAsync(id);
        }

        // Individual component operations removed - use main CRUD operations instead

        public async Task<IEnumerable<AllowableRise>> GetAllowableRiseOptionsAsync()
        {
            _logger.LogInformation("Getting allowable rise options");
            return await Task.FromResult(AllowableRise.All);
        }

        public async Task<bool> ValidateTemperatureAsync(Temperature temperature)
        {
            _logger.LogInformation("Validating temperature with ID: {TemperatureId}", temperature.Id);
            
            // Basic validation logic
            if (temperature == null) return false;
            if (temperature.HouseId == Guid.Empty) return false;
            
            // Validate temperature ranges
            if (temperature.MainFloors != null)
            {
                if (temperature.MainFloors.CoolingSetPoint < -50 || temperature.MainFloors.CoolingSetPoint > 50) return false;
                if (temperature.MainFloors.DaytimeHeatingSetPoint < -50 || temperature.MainFloors.DaytimeHeatingSetPoint > 50) return false;
                if (temperature.MainFloors.NighttimeHeatingSetPoint < -50 || temperature.MainFloors.NighttimeHeatingSetPoint > 50) return false;
                if (temperature.MainFloors.NighttimeSetbackDuration < 0 || temperature.MainFloors.NighttimeSetbackDuration > 24) return false;
            }
            
            return await Task.FromResult(true);
        }

        public async Task<bool> ExistsAsync(Guid id)
        {
            return await _repository.ExistsAsync(id);
        }

        public async Task<Temperature?> GetTemperatureByEnergyUpgradeIdAsync(Guid energyUpgradeId)
        {
            return await _repository.GetTemperatureByEnergyUpgradeIdAsync(energyUpgradeId);
        }

        public async Task<Temperature> DuplicateTemperatureForEnergyUpgradeAsync(Temperature baseTemperature, Guid energyUpgradeId)
        {
            return await _repository.DuplicateTemperatureForEnergyUpgradeAsync(baseTemperature, energyUpgradeId);
        }
    }
}
