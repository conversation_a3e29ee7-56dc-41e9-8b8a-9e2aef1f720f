using System;
using System.ComponentModel.DataAnnotations;

namespace TemperatureService.Core.Models
{
    /// <summary>
    /// Main temperature container class
    /// Based on Temperatures from HouseFileLibrary HouseComponents
    /// </summary>
    public class Temperature
    {
        public Guid Id { get; set; }
        public Guid HouseId { get; set; }
        public Guid? EnergyUpgradeId { get; set; }

        [StringLength(100)]
        public string Label { get; set; } = "Temperatures";

        // Navigation properties - following BaseLoadService pattern (no virtual keyword)
        public MainFloors MainFloors { get; set; } = null!;
        public VentilationBasement VentilationBasement { get; set; } = null!;
        public Equipment Equipment { get; set; } = null!;
        public Crawlspace Crawlspace { get; set; } = null!;

        public Temperature()
        {
            SetDefaults();
        }

        public Temperature(Temperature toCopy)
        {
            if (toCopy != null)
            {
                Id = toCopy.Id;
                HouseId = toCopy.HouseId;
                Label = toCopy.Label ?? "Temperatures";
                MainFloors = toCopy.MainFloors != null ? new MainFloors(toCopy.MainFloors) : new MainFloors();
                VentilationBasement = toCopy.VentilationBasement != null ? new VentilationBasement(toCopy.VentilationBasement) : new VentilationBasement();
                Equipment = toCopy.Equipment != null ? new Equipment(toCopy.Equipment) : new Equipment();
                Crawlspace = toCopy.Crawlspace != null ? new Crawlspace(toCopy.Crawlspace) : new Crawlspace();
            }
            else
            {
                SetDefaults();
            }
        }

        public void SetDefaults()
        {
            Label = "Temperatures";
            MainFloors = new MainFloors();
            VentilationBasement = new VentilationBasement();
            Equipment = new Equipment();
            Crawlspace = new Crawlspace();

            // Initialize related objects with their defaults
            MainFloors.SetDefaults();
            VentilationBasement.SetDefaults();
            Equipment.SetDefaults();
            Crawlspace.SetDefaults();
        }
    }
}
