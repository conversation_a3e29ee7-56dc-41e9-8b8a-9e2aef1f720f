using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using TemperatureService.Core.Interfaces;
using TemperatureService.Core.Models;
using TemperatureService.Infrastructure.Data;

namespace TemperatureService.Infrastructure.Repositories
{
    public class TemperatureRepository : ITemperatureRepository
    {
        private readonly TemperatureDbContext _context;
        private readonly ILogger<TemperatureRepository> _logger;

        public TemperatureRepository(TemperatureDbContext context, ILogger<TemperatureRepository> logger)
        {
            _context = context ?? throw new ArgumentNullException(nameof(context));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        }

        public async Task<Temperature> AddAsync(Temperature temperature)
        {
            _logger.LogInformation("Adding new temperature for house ID: {HouseId}", temperature.HouseId);

            // Set IDs for the temperature and related entities
            if (temperature.Id == Guid.Empty)
                temperature.Id = Guid.NewGuid();

            if (temperature.MainFloors != null)
            {
                if (temperature.MainFloors.Id == Guid.Empty)
                    temperature.MainFloors.Id = Guid.NewGuid();
                temperature.MainFloors.TemperatureId = temperature.Id;
            }

            if (temperature.VentilationBasement != null)
            {
                if (temperature.VentilationBasement.Id == Guid.Empty)
                    temperature.VentilationBasement.Id = Guid.NewGuid();
                temperature.VentilationBasement.TemperatureId = temperature.Id;
            }

            if (temperature.Equipment != null)
            {
                if (temperature.Equipment.Id == Guid.Empty)
                    temperature.Equipment.Id = Guid.NewGuid();
                temperature.Equipment.TemperatureId = temperature.Id;
            }

            if (temperature.Crawlspace != null)
            {
                if (temperature.Crawlspace.Id == Guid.Empty)
                    temperature.Crawlspace.Id = Guid.NewGuid();
                temperature.Crawlspace.TemperatureId = temperature.Id;
            }

            _context.Temperatures.Add(temperature);
            await _context.SaveChangesAsync();
            return temperature;
        }

        public async Task<Temperature> GetByIdAsync(Guid id)
        {
            _logger.LogInformation("Getting temperature with ID: {TemperatureId}", id);

            return await _context.Temperatures
                .AsNoTracking()
                .Include(t => t.MainFloors)
                .Include(t => t.VentilationBasement)
                .Include(t => t.Equipment)
                .Include(t => t.Crawlspace)
                .FirstOrDefaultAsync(t => t.Id == id);
        }

        public async Task<Temperature> GetByHouseIdAsync(Guid houseId)
        {
            _logger.LogInformation("Getting temperature for house ID: {HouseId}", houseId);

            return await _context.Temperatures
                .Include(t => t.MainFloors)
                .Include(t => t.VentilationBasement)
                .Include(t => t.Equipment)
                .Include(t => t.Crawlspace)
                .FirstOrDefaultAsync(t => t.HouseId == houseId);
        }

        public async Task<IEnumerable<Temperature>> GetAllAsync()
        {
            _logger.LogInformation("Getting all temperatures");

            return await _context.Temperatures
                .Include(t => t.MainFloors)
                .Include(t => t.VentilationBasement)
                .Include(t => t.Equipment)
                .Include(t => t.Crawlspace)
                .ToListAsync();
        }

        public async Task<Temperature> UpdateAsync(Temperature temperature)
        {
            _logger.LogInformation("Updating temperature with ID: {TemperatureId}", temperature.Id);

            var existingTemperature = await GetByIdAsync(temperature.Id);
            if (existingTemperature == null)
                return null;

            // Update temperature properties manually
            existingTemperature.HouseId = temperature.HouseId;
            existingTemperature.Label = temperature.Label;

            // Update MainFloors
            if (temperature.MainFloors != null)
            {
                if (existingTemperature.MainFloors != null)
                {
                    existingTemperature.MainFloors.CoolingSetPoint = temperature.MainFloors.CoolingSetPoint;
                    existingTemperature.MainFloors.DaytimeHeatingSetPoint = temperature.MainFloors.DaytimeHeatingSetPoint;
                    existingTemperature.MainFloors.NighttimeHeatingSetPoint = temperature.MainFloors.NighttimeHeatingSetPoint;
                    existingTemperature.MainFloors.NighttimeSetbackDuration = temperature.MainFloors.NighttimeSetbackDuration;
                    existingTemperature.MainFloors.AllowableRise = temperature.MainFloors.AllowableRise;
                }
                else
                {
                    temperature.MainFloors.Id = Guid.NewGuid();
                    temperature.MainFloors.TemperatureId = temperature.Id;
                    existingTemperature.MainFloors = temperature.MainFloors;
                }
            }

            // Update VentilationBasement
            if (temperature.VentilationBasement != null)
            {
                if (existingTemperature.VentilationBasement != null)
                {
                    existingTemperature.VentilationBasement.Heated = temperature.VentilationBasement.Heated;
                    existingTemperature.VentilationBasement.Cooled = temperature.VentilationBasement.Cooled;
                    existingTemperature.VentilationBasement.SeparateThermostat = temperature.VentilationBasement.SeparateThermostat;
                    existingTemperature.VentilationBasement.HeatingSetPoint = temperature.VentilationBasement.HeatingSetPoint;
                    existingTemperature.VentilationBasement.BasementUnit = temperature.VentilationBasement.BasementUnit;
                }
                else
                {
                    temperature.VentilationBasement.Id = Guid.NewGuid();
                    temperature.VentilationBasement.TemperatureId = temperature.Id;
                    existingTemperature.VentilationBasement = temperature.VentilationBasement;
                }
            }

            // Update Equipment
            if (temperature.Equipment != null)
            {
                if (existingTemperature.Equipment != null)
                {
                    existingTemperature.Equipment.HeatingSetPoint = temperature.Equipment.HeatingSetPoint;
                    existingTemperature.Equipment.CoolingSetPoint = temperature.Equipment.CoolingSetPoint;
                }
                else
                {
                    temperature.Equipment.Id = Guid.NewGuid();
                    temperature.Equipment.TemperatureId = temperature.Id;
                    existingTemperature.Equipment = temperature.Equipment;
                }
            }

            // Update Crawlspace
            if (temperature.Crawlspace != null)
            {
                if (existingTemperature.Crawlspace != null)
                {
                    existingTemperature.Crawlspace.Heated = temperature.Crawlspace.Heated;
                    existingTemperature.Crawlspace.HeatingSetPoint = temperature.Crawlspace.HeatingSetPoint;
                }
                else
                {
                    temperature.Crawlspace.Id = Guid.NewGuid();
                    temperature.Crawlspace.TemperatureId = temperature.Id;
                    existingTemperature.Crawlspace = temperature.Crawlspace;
                }
            }

            await _context.SaveChangesAsync();
            return existingTemperature;
        }

        public async Task<bool> DeleteAsync(Guid id)
        {
            _logger.LogInformation("Deleting temperature with ID: {TemperatureId}", id);

            var temperature = await GetByIdAsync(id);
            if (temperature == null)
                return false;

            _context.Temperatures.Remove(temperature);
            await _context.SaveChangesAsync();
            return true;
        }

        public async Task<bool> ExistsAsync(Guid id)
        {
            return await _context.Temperatures.AnyAsync(t => t.Id == id);
        }

        // Individual component get methods removed - use main CRUD operations instead

        // Individual component update methods removed - use main CRUD operations instead

        // All individual component update methods removed - use main CRUD operations instead

        public async Task<Temperature?> GetTemperatureByEnergyUpgradeIdAsync(Guid energyUpgradeId)
        {
            _logger.LogInformation("Getting temperature by energy upgrade ID: {EnergyUpgradeId}", energyUpgradeId);

            return await _context.Temperatures
                .Include(t => t.MainFloors)
                .Include(t => t.VentilationBasement)
                .Include(t => t.Equipment)
                .Include(t => t.Crawlspace)
                .FirstOrDefaultAsync(t => t.EnergyUpgradeId == energyUpgradeId);
        }

        public async Task<Temperature> DuplicateTemperatureForEnergyUpgradeAsync(Temperature baseTemperature, Guid energyUpgradeId)
        {
            _logger.LogInformation("Duplicating temperature {TemperatureId} for energy upgrade {EnergyUpgradeId}",
                baseTemperature.Id, energyUpgradeId);

            // Create a deep copy using the copy constructor
            var duplicatedTemperature = new Temperature(baseTemperature)
            {
                Id = Guid.NewGuid(),
                EnergyUpgradeId = energyUpgradeId
            };

            // Set new IDs for all related entities
            if (duplicatedTemperature.MainFloors != null)
            {
                duplicatedTemperature.MainFloors.Id = Guid.NewGuid();
                duplicatedTemperature.MainFloors.TemperatureId = duplicatedTemperature.Id;
            }

            if (duplicatedTemperature.VentilationBasement != null)
            {
                duplicatedTemperature.VentilationBasement.Id = Guid.NewGuid();
                duplicatedTemperature.VentilationBasement.TemperatureId = duplicatedTemperature.Id;
            }

            if (duplicatedTemperature.Equipment != null)
            {
                duplicatedTemperature.Equipment.Id = Guid.NewGuid();
                duplicatedTemperature.Equipment.TemperatureId = duplicatedTemperature.Id;
            }

            if (duplicatedTemperature.Crawlspace != null)
            {
                duplicatedTemperature.Crawlspace.Id = Guid.NewGuid();
                duplicatedTemperature.Crawlspace.TemperatureId = duplicatedTemperature.Id;
            }

            // Add to context and save
            _context.Temperatures.Add(duplicatedTemperature);
            await _context.SaveChangesAsync();

            return duplicatedTemperature;
        }
    }
}
