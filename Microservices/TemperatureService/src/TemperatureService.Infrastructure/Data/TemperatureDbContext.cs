using Microsoft.EntityFrameworkCore;
using TemperatureService.Core.Models;

namespace TemperatureService.Infrastructure.Data
{
    public class TemperatureDbContext : DbContext
    {
        public TemperatureDbContext(DbContextOptions<TemperatureDbContext> options) : base(options)
        {
        }

        public DbSet<Temperature> Temperatures { get; set; } = null!;
        public DbSet<MainFloors> MainFloors { get; set; } = null!;
        public DbSet<VentilationBasement> VentilationBasements { get; set; } = null!;
        public DbSet<Equipment> Equipment { get; set; } = null!;
        public DbSet<Crawlspace> Crawlspaces { get; set; } = null!;

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            // Set default schema for all entities in this context
            modelBuilder.HasDefaultSchema("temperature");

            base.OnModelCreating(modelBuilder);

            // Temperature configuration - following BaseLoadService pattern
            modelBuilder.Entity<Temperature>(entity =>
            {
                entity.ToTable("Temperatures", "temperature"); // Explicitly specify table name and schema
                entity.HasKey(e => e.Id);
                entity.Property(e => e.HouseId).IsRequired();
                entity.Property(e => e.EnergyUpgradeId).IsRequired(false);
                entity.Property(e => e.Label).HasMaxLength(100);

                // One-to-one relationships - following BaseLoadService pattern
                entity.HasOne(e => e.MainFloors)
                    .WithOne(e => e.Temperature)
                    .HasForeignKey<MainFloors>(e => e.TemperatureId)
                    .OnDelete(DeleteBehavior.Cascade);

                entity.HasOne(e => e.VentilationBasement)
                    .WithOne(e => e.Temperature)
                    .HasForeignKey<VentilationBasement>(e => e.TemperatureId)
                    .OnDelete(DeleteBehavior.Cascade);

                entity.HasOne(e => e.Equipment)
                    .WithOne(e => e.Temperature)
                    .HasForeignKey<Equipment>(e => e.TemperatureId)
                    .OnDelete(DeleteBehavior.Cascade);

                entity.HasOne(e => e.Crawlspace)
                    .WithOne(e => e.Temperature)
                    .HasForeignKey<Crawlspace>(e => e.TemperatureId)
                    .OnDelete(DeleteBehavior.Cascade);
            });

            // MainFloors configuration - following BaseLoadService pattern
            modelBuilder.Entity<MainFloors>(entity =>
            {
                entity.ToTable("MainFloors", "temperature"); // Specify schema
                entity.HasKey(e => e.Id);
                entity.Property(e => e.TemperatureId).IsRequired();
                entity.Property(e => e.CoolingSetPoint).HasPrecision(18, 2);
                entity.Property(e => e.DaytimeHeatingSetPoint).HasPrecision(18, 2);
                entity.Property(e => e.NighttimeHeatingSetPoint).HasPrecision(18, 2);
                entity.Property(e => e.NighttimeSetbackDuration).HasPrecision(18, 2);

                // Configure AllowableRise as owned entity - following VentilationService/HvacService pattern
                entity.OwnsOne(e => e.AllowableRise, allowableRise =>
                {
                    allowableRise.Property(ar => ar.Code).HasColumnName("AllowableRiseCode").HasMaxLength(10);
                    allowableRise.Property(ar => ar.English).HasColumnName("AllowableRiseEnglish").HasMaxLength(100);
                    allowableRise.Property(ar => ar.French).HasColumnName("AllowableRiseFrench").HasMaxLength(100);
                    allowableRise.Property(ar => ar.IsUserSpecified).HasColumnName("AllowableRiseIsUserSpecified");
                });
            });

            // VentilationBasement configuration - following BaseLoadService pattern
            modelBuilder.Entity<VentilationBasement>(entity =>
            {
                entity.ToTable("VentilationBasements", "temperature"); // Specify schema
                entity.HasKey(e => e.Id);
                entity.Property(e => e.TemperatureId).IsRequired();
                entity.Property(e => e.HeatingSetPoint).HasPrecision(18, 2);
            });

            // Equipment configuration - following BaseLoadService pattern
            modelBuilder.Entity<Equipment>(entity =>
            {
                entity.ToTable("Equipment", "temperature"); // Specify schema
                entity.HasKey(e => e.Id);
                entity.Property(e => e.TemperatureId).IsRequired();
                entity.Property(e => e.HeatingSetPoint).HasPrecision(18, 2);
                entity.Property(e => e.CoolingSetPoint).HasPrecision(18, 2);
            });

            // Crawlspace configuration - following BaseLoadService pattern
            modelBuilder.Entity<Crawlspace>(entity =>
            {
                entity.ToTable("Crawlspaces", "temperature"); // Specify schema
                entity.HasKey(e => e.Id);
                entity.Property(e => e.TemperatureId).IsRequired();
                entity.Property(e => e.HeatingSetPoint).HasPrecision(18, 2);
            });
        }
    }
}
