using AutoMapper;
using Microsoft.AspNetCore.Mvc;
using TemperatureService.API.Models;
using TemperatureService.Core.Interfaces;
using TemperatureService.Core.Models;

namespace TemperatureService.API.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class TemperatureController : ControllerBase
    {
        private readonly ITemperatureService _temperatureService;
        private readonly IMapper _mapper;
        private readonly ILogger<TemperatureController> _logger;

        public TemperatureController(
            ITemperatureService temperatureService,
            IMapper mapper,
            ILogger<TemperatureController> logger)
        {
            _temperatureService = temperatureService ?? throw new ArgumentNullException(nameof(temperatureService));
            _mapper = mapper ?? throw new ArgumentNullException(nameof(mapper));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        }

        // GET: api/temperature/{id}
        [HttpGet("{id}")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<ActionResult<TemperatureDto>> GetTemperatureById(Guid id)
        {
            _logger.LogInformation("Getting temperature with ID: {TemperatureId}", id);

            var temperature = await _temperatureService.GetTemperatureByIdAsync(id);
            if (temperature == null)
            {
                _logger.LogWarning("Temperature with ID: {TemperatureId} not found", id);
                return NotFound();
            }

            var temperatureDto = _mapper.Map<TemperatureDto>(temperature);
            return Ok(temperatureDto);
        }

        // GET: api/temperature/house/{houseId}
        [HttpGet("house/{houseId}")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<ActionResult<TemperatureDto>> GetTemperatureByHouseId(Guid houseId)
        {
            _logger.LogInformation("Getting temperature for house ID: {HouseId}", houseId);

            var temperature = await _temperatureService.GetTemperatureByHouseIdAsync(houseId);
            if (temperature == null)
            {
                _logger.LogWarning("Temperature for house ID: {HouseId} not found", houseId);
                return NotFound();
            }

            var temperatureDto = _mapper.Map<TemperatureDto>(temperature);
            return Ok(temperatureDto);
        }

        // GET: api/temperature
        [HttpGet]
        [ProducesResponseType(StatusCodes.Status200OK)]
        public async Task<ActionResult<IEnumerable<TemperatureDto>>> GetAllTemperatures()
        {
            _logger.LogInformation("Getting all temperatures");

            var temperatures = await _temperatureService.GetAllTemperaturesAsync();
            var temperatureDtos = _mapper.Map<IEnumerable<TemperatureDto>>(temperatures);

            return Ok(temperatureDtos);
        }

        // POST: api/temperature
        [HttpPost]
        [ProducesResponseType(StatusCodes.Status201Created)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        public async Task<ActionResult<TemperatureDto>> CreateTemperature([FromBody] TemperatureDto temperatureDto)
        {
            _logger.LogInformation("Creating new temperature for house ID: {HouseId}", temperatureDto.HouseId);

            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }

            var temperature = _mapper.Map<Temperature>(temperatureDto);
            var createdTemperature = await _temperatureService.CreateTemperatureAsync(temperature);
            var createdTemperatureDto = _mapper.Map<TemperatureDto>(createdTemperature);

            return CreatedAtAction(nameof(GetTemperatureById), new { id = createdTemperatureDto.Id }, createdTemperatureDto);
        }

        // PUT: api/temperature/{id}
        [HttpPut("{id}")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<ActionResult<TemperatureDto>> UpdateTemperature(Guid id, [FromBody] TemperatureDto temperatureDto)
        {
            _logger.LogInformation("Updating temperature with ID: {TemperatureId}", id);

            if (id != temperatureDto.Id)
            {
                _logger.LogWarning("ID mismatch: {Id} != {DtoId}", id, temperatureDto.Id);
                return BadRequest("ID mismatch");
            }

            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }

            try
            {
                var temperature = _mapper.Map<Temperature>(temperatureDto);
                var updatedTemperature = await _temperatureService.UpdateTemperatureAsync(temperature);
                var updatedTemperatureDto = _mapper.Map<TemperatureDto>(updatedTemperature);

                return Ok(updatedTemperatureDto);
            }
            catch (KeyNotFoundException)
            {
                _logger.LogWarning("Temperature with ID: {TemperatureId} not found for update", id);
                return NotFound();
            }
        }

        // DELETE: api/temperature/{id}
        [HttpDelete("{id}")]
        [ProducesResponseType(StatusCodes.Status204NoContent)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<IActionResult> DeleteTemperature(Guid id)
        {
            _logger.LogInformation("Deleting temperature with ID: {TemperatureId}", id);

            var result = await _temperatureService.DeleteTemperatureAsync(id);
            if (!result)
            {
                _logger.LogWarning("Temperature with ID: {TemperatureId} not found for deletion", id);
                return NotFound();
            }

            return NoContent();
        }

        // GET: api/temperature/allowable-rise
        [HttpGet("allowable-rise")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        public async Task<ActionResult<IEnumerable<AllowableRiseDto>>> GetAllowableRiseOptions()
        {
            _logger.LogInformation("Getting allowable rise options");

            var allowableRiseOptions = await _temperatureService.GetAllowableRiseOptionsAsync();
            var allowableRiseDtos = _mapper.Map<IEnumerable<AllowableRiseDto>>(allowableRiseOptions);

            return Ok(allowableRiseDtos);
        }

        // Individual component endpoints removed - use main PUT endpoint instead

        // GET: api/temperature/energy-upgrades/{energyUpgradeId}/temperatures
        [HttpGet("energy-upgrades/{energyUpgradeId}/temperatures")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<ActionResult<TemperatureDto>> GetTemperaturesByEnergyUpgradeId(Guid energyUpgradeId)
        {
            try
            {
                _logger.LogInformation("Getting temperature by energy upgrade ID: {EnergyUpgradeId}", energyUpgradeId);

                var temperature = await _temperatureService.GetTemperatureByEnergyUpgradeIdAsync(energyUpgradeId);

                if (temperature == null)
                {
                    return NotFound(new { Error = "Temperature not found for energy upgrade", EnergyUpgradeId = energyUpgradeId });
                }

                var temperatureDto = _mapper.Map<TemperatureDto>(temperature);
                return Ok(temperatureDto);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting temperature by energy upgrade ID: {EnergyUpgradeId}", energyUpgradeId);
                return StatusCode(500, new { Error = "Internal server error" });
            }
        }

        // POST: api/temperature/{baseTemperatureId}/duplicate-for-energy-upgrade
        [HttpPost("{baseTemperatureId}/duplicate-for-energy-upgrade")]
        [ProducesResponseType(StatusCodes.Status201Created)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<ActionResult<TemperatureDto>> DuplicateTemperatureForEnergyUpgrade(
            Guid baseTemperatureId,
            [FromQuery] Guid energyUpgradeId)
        {
            try
            {
                _logger.LogInformation("Duplicating temperature {BaseTemperatureId} for energy upgrade {EnergyUpgradeId}",
                    baseTemperatureId, energyUpgradeId);

                var baseTemperature = await _temperatureService.GetTemperatureByIdAsync(baseTemperatureId);

                if (baseTemperature == null)
                {
                    return NotFound(new { Error = "Base temperature not found", BaseTemperatureId = baseTemperatureId });
                }

                var duplicatedTemperature = await _temperatureService.DuplicateTemperatureForEnergyUpgradeAsync(
                    baseTemperature, energyUpgradeId);

                var temperatureDto = _mapper.Map<TemperatureDto>(duplicatedTemperature);

                return CreatedAtAction(
                    nameof(GetTemperatureById),
                    new { id = temperatureDto.Id },
                    temperatureDto);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error duplicating temperature {BaseTemperatureId} for energy upgrade {EnergyUpgradeId}",
                    baseTemperatureId, energyUpgradeId);
                return StatusCode(500, new { Error = "Internal server error" });
            }
        }
    }
}
