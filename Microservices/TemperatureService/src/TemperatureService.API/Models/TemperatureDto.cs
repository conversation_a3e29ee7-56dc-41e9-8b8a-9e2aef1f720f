using System;
using System.ComponentModel.DataAnnotations;

namespace TemperatureService.API.Models
{
    /// <summary>
    /// Main DTO for Temperature data
    /// </summary>
    public class TemperatureDto
    {
        public Guid Id { get; set; }
        public Guid HouseId { get; set; }
        public Guid? EnergyUpgradeId { get; set; }

        [StringLength(100)]
        public string Label { get; set; } = "Temperatures";

        public MainFloorsDto MainFloors { get; set; } = new MainFloorsDto();
        public VentilationBasementDto VentilationBasement { get; set; } = new VentilationBasementDto();
        public EquipmentDto Equipment { get; set; } = new EquipmentDto();
        public CrawlspaceDto Crawlspace { get; set; } = new CrawlspaceDto();
    }
}
